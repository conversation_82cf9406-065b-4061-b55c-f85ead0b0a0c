export const command10 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Security Configuration",
    "Configure SSL"
  ],
  "description": "Enables and configures SSL/TLS encryption for all incoming and outgoing database connections, securing data in transit.",
  "query_template": "ALTER SYSTEM SET [parameter] = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Configuration Command",
      "description": "Modifies the main `postgresql.conf` file directly (by writing to `postgresql.auto.conf`), allowing for persistent configuration changes."
    },
    {
      "component": "ssl = 'on'",
      "name": "Enable SSL",
      "description": "The primary switch to enable SSL functionality on the server."
    },
    {
      "component": "ssl_cert_file",
      "name": "Server Certificate",
      "description": "Specifies the file system path to the server's SSL certificate file (e.g., 'server.crt')."
    },
    {
      "component": "ssl_key_file",
      "name": "Server Private Key",
      "description": "Specifies the file system path to the private key for the server certificate (e.g., 'server.key')."
    }
  ],
  "scenarios": [
    {
      "title": "Enforcing Encrypted Client-Server Connections",
      "details": {
        "setup": "You are the administrator of a production database that currently communicates with application servers over an internal network. A security audit requires that all data-in-transit, including credentials and query results, must be encrypted to prevent eavesdropping.",
        "goal": "To configure the PostgreSQL server to use SSL/TLS, and then enforce its use for all client connections.",
        "outcome": "The server is successfully configured for SSL. When clients attempt to connect without SSL, their connection is rejected, ensuring all communication is encrypted and secure."
      },
      "example": {
        "query": "-- Step 1: Set the required SSL parameters\nALTER SYSTEM SET ssl = 'on';\nALTER SYSTEM SET ssl_cert_file = '/etc/postgresql/15/main/server.crt';\nALTER SYSTEM SET ssl_key_file = '/etc/postgresql/15/main/server.key';\n\n-- Step 2: Reload the configuration to apply the changes\nSELECT pg_reload_conf();",
        "explanation": "This sequence of `ALTER SYSTEM` commands enables SSL and tells the server where to find the certificate and private key. These files must be generated beforehand. The `pg_reload_conf()` function is then called to make the server apply these new settings without a full restart. The file paths shown are typical for a Debian/Ubuntu Linux installation."
      },
      "notes": [
        "CRITICAL: After enabling SSL, you must edit `pg_hba.conf` and change connection types from `host` to `hostssl` to actually *enforce* SSL. Otherwise, the server will still accept unencrypted connections.",
        "The private key file (`server.key`) must have strict file permissions (e.g., `chmod 600`) and be owned by the `postgres` user.",
        "For production environments, use certificates signed by a trusted Certificate Authority (CA). For development, you can generate self-signed certificates.",
        "File paths for certificates and keys will vary by operating system. On Windows, a path might look like 'C:/Program Files/PostgreSQL/15/data/server.crt'."
      ]
    }
  ]
};