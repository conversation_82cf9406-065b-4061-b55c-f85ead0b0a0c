export const command12 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Query Optimization",
    "Configure Query Planner"
  ],
  "description": "Adjusts the query planner's cost estimates to better reflect the underlying server hardware (e.g., SSDs vs HDDs), leading to more accurate and performant query plans.",
  "query_template": "ALTER SYSTEM SET [parameter] = [value];",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Alter System Command",
      "description": "A powerful command that writes a given configuration parameter and its value to the `postgresql.auto.conf` file. This setting will persist across server restarts."
    },
    {
      "component": "random_page_cost",
      "name": "Parameter Name",
      "description": "The name of the configuration parameter you want to change. In this case, `random_page_cost` estimates the cost of fetching a single, non-sequential database page from disk."
    }
  ],
  "scenarios": [
    {
      "title": "Tuning the Query Planner for Modern SSD Storage",
      "details": {
        "setup": "Your PostgreSQL database server has recently been migrated to a machine with high-performance SSD storage. However, you notice that some queries are not using indexes as often as you'd expect, leading to suboptimal performance.",
        "goal": "To inform the PostgreSQL query planner that the cost of random disk access is very low on SSDs, encouraging it to favor index scans over sequential scans more frequently.",
        "outcome": "The query planner now has a more accurate cost model for your hardware. After reloading the configuration, you observe that previously slow queries are now faster because they correctly use available indexes."
      },
      "example": {
        "query": "ALTER SYSTEM SET random_page_cost = 1.1;",
        "explanation": "PostgreSQL's default `random_page_cost` is 4.0, assuming a traditional spinning-disk HDD where random reads are much slower than sequential ones. By lowering it to 1.1, we tell the planner that for our fast SSD, a random read is only slightly more expensive than a sequential read. This makes using an index (which involves random I/O) a much more attractive choice for the planner."
      },
      "notes": [
        "This change is not applied immediately. You must run `SELECT pg_reload_conf();` or restart the PostgreSQL server for it to take effect.",
        "Changing `random_page_cost` is one of the single most effective performance tuning steps when using SSDs.",
        "For a holistic tuning approach, you should also review related parameters like `effective_cache_size` to give the planner a complete picture of the server's memory and I/O capabilities."
      ]
    }
  ]
};