export const command6 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Logging Configuration",
    "Configure Log Level"
  ],
  "description": "Sets the minimum message level that is written to the server log. This is a persistent, system-wide configuration change.",
  "query_template": "ALTER SYSTEM SET log_min_messages = '[level]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "System Configuration Change",
      "description": "A command used to change a configuration parameter globally across the entire database system. The change is written to the 'postgresql.auto.conf' file."
    },
    {
      "component": "log_min_messages",
      "name": "Parameter Name",
      "description": "The specific configuration parameter that controls the verbosity of the server logs."
    },
    {
      "component": "'warning' | 'error' | 'info' | 'notice' | etc.",
      "name": "Log Level",
      "description": "Specifies the severity threshold. Only messages at this level or higher will be logged. Common levels are 'warning', 'error', 'info', and 'debug1'."
    }
  ],
  "scenarios": [
    {
      "title": "Reducing Log Noise on a Production Database Server",
      "details": {
        "setup": "Your production database server is generating a large volume of logs, mostly consisting of `NOTICE` and `INFO` messages from routine operations. This 'log noise' consumes disk space and makes it difficult to spot actual errors or critical warnings.",
        "goal": "To reconfigure the server to log only messages that are 'warning' level or more severe, ensuring that important issues are not buried in verbose log files.",
        "outcome": "The server's configuration is updated. After a configuration reload, the PostgreSQL logs are significantly cleaner, only showing relevant warnings, errors, and fatal messages."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_min_messages = 'warning';",
        "explanation": "This query writes the `log_min_messages = 'warning'` setting to the `postgresql.auto.conf` file. This ensures the setting will persist even after a server restart. However, the change is not yet active."
      },
      "notes": [
        "CRITICAL: After running `ALTER SYSTEM`, you must run `SELECT pg_reload_conf();` or restart the server for the change to take effect.",
        "Setting the level to `warning` will also log `ERROR`, `LOG`, `FATAL`, and `PANIC` messages, as they are higher severity.",
        "To change the log level only for your current session (for temporary debugging), use the command `SET log_min_messages = 'debug1';` without `ALTER SYSTEM`."
      ]
    },
    {
      "title": "Enabling Detailed Debugging for Troubleshooting",
      "details": {
        "setup": "You are trying to diagnose a complex issue with a specific function or a stored procedure that is behaving unexpectedly, but the standard logs provide no useful information.",
        "goal": "To temporarily increase the log verbosity to the highest debug level to get detailed execution information from the server.",
        "outcome": "The server begins logging extremely detailed information, which can be used to trace the execution path and identify the source of the problem."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_min_messages = 'debug5';",
        "explanation": "This sets the logging level to the most verbose setting possible. Remember to run `SELECT pg_reload_conf();` to apply it."
      },
      "notes": [
        "Warning: Debug levels generate a massive amount of log data and can impact performance. Only use them for active troubleshooting and remember to set the level back to `warning` or `error` afterward.",
        "To revert the change, run `ALTER SYSTEM SET log_min_messages = 'warning';` followed by another `SELECT pg_reload_conf();`."
      ]
    }
  ]
};