export const command7 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Logging Configuration",
    "Configure Slow Query Logging"
  ],
  "description": "Configures the PostgreSQL server to automatically log SQL statements that exceed a specified execution time. This is one of the most effective ways to identify and diagnose performance bottlenecks in an application.",
  "query_template": "ALTER SYSTEM SET log_min_duration_statement = [milliseconds];",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Modify System Configuration",
      "description": "A powerful SQL command that writes the given configuration parameter directly to the `postgresql.auto.conf` file, ensuring the setting persists after a server restart."
    },
    {
      "component": "log_min_duration_statement",
      "name": "Logging Threshold",
      "description": "The core parameter. It defines the minimum amount of time (in milliseconds) a statement must run for it to be logged. A value of '0' logs all statements, while '-1' (the default) disables this feature."
    }
  ],
  "scenarios": [
    {
      "title": "Proactively Identifying Performance Bottlenecks in a Production API",
      "details": {
        "setup": "Your application's performance monitoring shows that certain API endpoints are occasionally slow, but it's difficult to know which specific database queries are the cause.",
        "goal": "To configure the database to automatically capture any query that takes longer than 500 milliseconds, allowing developers to review and optimize them.",
        "outcome": "The PostgreSQL log file now contains the full text and duration of any slow-running queries, providing clear, actionable data for optimization efforts."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_min_duration_statement = 500;\nSELECT pg_reload_conf();",
        "explanation": "The first command sets the slow-query threshold to 500ms. The second command, `SELECT pg_reload_conf();`, is crucial as it reloads the server's configuration and applies the change immediately without requiring a full server restart."
      },
      "notes": [
        "This setting requires `log_statement` to be at its default setting. If `log_statement` is set to 'none', it may prevent this from working.",
        "After identifying and optimizing queries, remember to set the value back to '-1' to disable logging and reduce I/O overhead on a busy server.",
        "The location of the PostgreSQL log file varies by operating system. Check your `postgresql.conf` for the `log_directory` setting."
      ]
    },
    {
      "title": "Detailed Debugging During a Complex Data Migration",
      "details": {
        "setup": "You are running a large data migration script with hundreds of complex queries. You need to log the duration of every single statement to analyze the script's performance profile.",
        "goal": "To temporarily configure the database to log all executed statements and their durations, regardless of how fast they are.",
        "outcome": "A complete log of every query from the migration script is generated, allowing for a step-by-step performance analysis."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_min_duration_statement = 0;\nSELECT pg_reload_conf();",
        "explanation": "By setting `log_min_duration_statement` to 0, you instruct PostgreSQL to log every completed statement. This provides maximum visibility during development or debugging but should be used with caution in production due to the high volume of logs it can generate."
      },
      "notes": [
        "This setting can fill up disk space quickly on a busy system. Use it for temporary analysis and remember to disable it (`-1`) afterward.",
        "Combine this with a custom `log_line_prefix` in `postgresql.conf` to add more context to each log entry, such as the application name or user ID."
      ]
    }
  ]
};