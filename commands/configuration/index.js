import { command0 } from './server-configuration/view-current-settings.js';
import { command1 } from './server-configuration/modify-configuration.js';
import { command2 } from './server-configuration/reset-configuration.js';
import { command3 } from './memory-settings/configure-shared-buffers.js';
import { command4 } from './memory-settings/configure-work-memory.js';
import { command5 } from './connection-settings/configure-max-connections.js';
import { command6 } from './logging-configuration/configure-log-level.js';
import { command7 } from './logging-configuration/configure-slow-query-logging.js';
import { command8 } from './performance-tuning/configure-checkpoint-settings.js';
import { command9 } from './performance-tuning/configure-wal-settings.js';
import { command10 } from './security-configuration/configure-ssl.js';
import { command11 } from './backup-configuration/configure-archive-mode.js';
import { command12 } from './query-optimization/configure-query-planner.js';
import { command13 } from './monitoring-configuration/configure-statistics.js';

export const configurationCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13
];

export function validateConfigurationCommands() {
  return configurationCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Configuration Management";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
