export const command11 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Backup Configuration",
    "Configure Archive Mode"
  ],
  "description": "Enables and configures Write-Ahead Log (WAL) archiving. This is the foundational step for setting up advanced backup strategies like Point-in-Time Recovery (PITR) and creating standby servers for replication.",
  "query_template": "ALTER SYSTEM SET [parameter] = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM",
      "name": "Configuration Clause",
      "description": "The standard SQL clause for modifying the postgresql.conf configuration file directly."
    },
    {
      "component": "wal_level",
      "name": "Prerequisite Setting",
      "description": "Must be set to at least 'replica' for WAL archiving to function. Defaults to 'minimal' on older versions."
    },
    {
      "component": "archive_mode",
      "name": "Enable Archiving",
      "description": "The primary parameter to enable ('on'), disable ('off'), or force archiving on standbys ('always')."
    },
    {
      "component": "archive_command",
      "name": "Archiving Mechanism",
      "description": "Defines the operating system shell command that PostgreSQL will execute to copy a completed WAL file to your archive location."
    }
  ],
  "scenarios": [
    {
      "title": "Enabling Point-in-Time Recovery (PITR) for a Critical Database",
      "details": {
        "setup": "A critical production database is backed up nightly using `pg_dump`. If a server failure or accidental data deletion occurs late in the day, this model risks up to 24 hours of data loss.",
        "goal": "To configure the database to continuously archive its transaction logs (WAL files) to a secure storage location. This will enable restoration to any specific moment in time, reducing potential data loss to minutes or seconds.",
        "outcome": "The server configuration is updated. After a restart, PostgreSQL automatically copies every completed WAL segment to the archive directory, making PITR possible with a base backup."
      },
      "example": {
        "query": "-- Step 1: Set the required WAL level\nALTER SYSTEM SET wal_level = 'replica';\n\n-- Step 2: Enable the archive mode\nALTER SYSTEM SET archive_mode = 'on';\n\n-- Step 3: Define the archive command (Choose one based on your OS)\n\n-- Linux/macOS Example:\nALTER SYSTEM SET archive_command = 'test ! -f /mnt/backups/wal_archive/%f && cp %p /mnt/backups/wal_archive/%f';\n\n-- Windows Example:\nALTER SYSTEM SET archive_command = 'copy \"%p\" \"C:\\Postgres\\wal_archive\\%f\"';",
        "explanation": "This sequence of commands prepares the database for PITR. `wal_level = 'replica'` ensures the transaction logs contain enough information. `archive_mode = 'on'` activates the feature. The `archive_command` is the OS-level instruction that performs the copy, where `%p` is a placeholder for the path of the WAL file to be archived and `%f` is the filename only. The Linux example includes a `test` to prevent re-archiving an existing file."
      },
      "notes": [
        "IMPORTANT: The server must be restarted for these changes (`wal_level`, `archive_mode`) to take effect.",
        "The `archive_command` is executed by the `postgres` user. Ensure this user has write permissions to the destination directory (e.g., `/mnt/backups/wal_archive/`).",
        "A failing `archive_command` will halt database operations, as PostgreSQL cannot recycle WAL files until they are successfully archived. Test it thoroughly!",
        "This configuration is just one part of PITR. You must also have a recent base backup (e.g., from `pg_basebackup`) from which to start the recovery."
      ]
    }
  ]
};