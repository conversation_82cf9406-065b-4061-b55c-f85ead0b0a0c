export const command8 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Performance Tuning",
    "Configure Checkpoint Settings"
  ],
  "description": "Adjusts checkpoint behavior to smooth out I/O load over time, preventing performance drops caused by intense bursts of disk writes. This is a critical tuning parameter for write-intensive databases.",
  "query_template": "ALTER SYSTEM SET [parameter_name] = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Configuration Command",
      "description": "A SQL command used to change a server configuration parameter in the `postgresql.auto.conf` file. This change persists across server restarts."
    },
    {
      "component": "checkpoint_completion_target",
      "name": "Parameter to Set",
      "description": "Specifies the target for completing a checkpoint as a fraction of the total time between checkpoints. It controls the pacing of writes."
    },
    {
      "component": "pg_reload_conf()",
      "name": "Apply Configuration",
      "description": "After running ALTER SYSTEM, you must run `SELECT pg_reload_conf();` or restart the server for the change to take effect."
    }
  ],
  "scenarios": [
    {
      "title": "Smoothing I/O Spikes on a Write-Heavy Analytics Database",
      "details": {
        "setup": "You manage a database that ingests a high volume of tracking data. Every few minutes, your monitoring tools show a massive spike in disk I/O, and application latency increases significantly during these events. This corresponds to the server's checkpoint process, which is writing all dirty buffers to disk in a short, intense burst.",
        "goal": "To spread the checkpoint's disk writing activity over a longer period, reducing the intensity of the I/O spikes and creating a more consistent performance profile for the application.",
        "outcome": "The database's I/O pattern becomes smoother. Instead of sharp, disruptive spikes, the write activity is spread out over several minutes, leading to stable application latency and predictable performance."
      },
      "example": {
        "query": "ALTER SYSTEM SET checkpoint_completion_target = 0.9;",
        "explanation": "By setting `checkpoint_completion_target` to `0.9`, you are telling PostgreSQL to take up to 90% of the time between checkpoints (as determined by `checkpoint_timeout` and `max_wal_size`) to complete its writes. This forces a slower, more paced approach instead of a frantic burst, effectively smoothing I/O load."
      },
      "notes": [
        "After executing the `ALTER SYSTEM` command, you must run `SELECT pg_reload_conf();` as a superuser to apply the setting without a full server restart.",
        "The default value is often `0.5`, which can be too aggressive for modern hardware. A value of `0.9` is a widely recommended starting point for most systems.",
        "This parameter works in conjunction with `checkpoint_timeout` (time-based trigger) and `max_wal_size` (size-based trigger) which determine how often checkpoints occur."
      ]
    }
  ]
};