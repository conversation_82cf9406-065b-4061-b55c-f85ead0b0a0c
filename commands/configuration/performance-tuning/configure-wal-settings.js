export const command9 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Performance Tuning",
    "Configure WAL Settings"
  ],
  "description": "Modifies a PostgreSQL configuration parameter directly using SQL. This command writes the setting to the postgresql.auto.conf file, overriding the value in postgresql.conf.",
  "query_template": "ALTER SYSTEM SET [parameter_name] = '[value]';'",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Alter System Clause",
      "description": "The SQL command used to change server configuration parameters globally."
    },
    {
      "component": "wal_buffers = '16MB'",
      "name": "Parameter and Value",
      "description": "The specific configuration parameter to change ('wal_buffers') and the new value it should be set to."
    }
  ],
  "scenarios": [
    {
      "title": "Tuning WAL Buffers for a Write-Intensive Application",
      "details": {
        "setup": "You are managing a database for an IoT application that receives thousands of small data writes per second. Monitoring shows high I/O wait times, and you suspect that the frequent flushing of the Write-Ahead Log (WAL) to disk is a bottleneck.",
        "goal": "To improve write throughput by increasing the amount of memory dedicated to WAL buffers. This allows PostgreSQL to accumulate more WAL data in memory before flushing it to disk in a larger, more efficient batch.",
        "outcome": "The `wal_buffers` parameter is permanently set to 16MB. After a configuration reload, the database can handle higher write loads with reduced disk I/O contention."
      },
      "example": {
        "query": "ALTER SYSTEM SET wal_buffers = '16MB';",
        "explanation": "This command sets the `wal_buffers` to 16MB, a commonly recommended maximum. It edits the `postgresql.auto.conf` file, ensuring the setting persists after restarts. A larger buffer reduces the frequency of small, inefficient disk writes, improving performance for write-heavy workloads."
      },
      "notes": [
        "This change requires a server reload to take effect. You can apply it without a full restart by running `SELECT pg_reload_conf();`",
        "For comprehensive WAL tuning, also investigate the `synchronous_commit` parameter. Setting it to `off` can provide a significant performance boost at the cost of a small risk of data loss in a server crash.",
        "The `wal_sync_method` parameter controls how WAL updates are forced to disk and can be tuned based on your operating system (e.g., `fdatasync` on Linux)."
      ]
    }
  ]
};