export const command5 = {
  // --- Re-typed as a 'query' ---
  "type": "query",

  "path": [
    "Configuration Management",
    "Connection Settings",
    "Configure Max Connections"
  ],
  "description": "Sets the maximum number of concurrent client connections allowed by the server. This is a fundamental setting for tuning server capacity.",

  // --- Query-specific fields ---
  "query_template": "ALTER SYSTEM SET max_connections = [value];",
  "components": [
    {
      "component": "ALTER SYSTEM",
      "name": "Alter System Command",
      "description": "A powerful SQL command that directly modifies the 'postgresql.auto.conf' file, ensuring the setting persists after a server restart."
    },
    {
      "component": "max_connections",
      "name": "Parameter Name",
      "description": "The specific server configuration parameter to modify. Default is typically 100."
    }
  ],

  // --- <PERSON><PERSON><PERSON> for practical application ---
  "scenarios": [
    {
      "title": "Scaling a Web Application to Handle Increased User Traffic",
      "details": {
        "setup": "A growing web application is starting to log 'FATAL: sorry, too many clients already' errors during peak business hours. The current PostgreSQL connection limit is the default of 100.",
        "goal": "To increase the server's connection capacity to 200 to accommodate the increased load from the application's connection pool.",
        "outcome": "The configuration is updated. After the server is restarted, the new connection limit is active, and the application no longer experiences connection errors."
      },
      "example": {
        "query": "ALTER SYSTEM SET max_connections = 200;",
        "explanation": "This query writes `max_connections = 200` to the `postgresql.auto.conf` file. This method is superior to manually editing the `.conf` file as it is less error-prone and its effects are persistent. The change will be applied the next time the server is restarted."
      },
      "notes": [
        "IMPORTANT: This parameter change REQUIRES a full server restart to take effect. A reload (`pg_ctl reload`) is not sufficient.",
        "Each connection consumes memory. Before increasing this value, ensure your server has enough RAM to support the new limit.",
        "For managing a very large number of clients, using a connection pooler like PgBouncer is often a better architecture than a very high `max_connections` value.",
        "PostgreSQL reserves connections for superusers (see `superuser_reserved_connections`), ensuring you can still log in for administrative tasks even if the limit is reached."
      ]
    }
  ]
};