export const command13 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Monitoring Configuration",
    "Enable Activity Tracking"
  ],
  "description": "Enables the collection of statistics about currently executing commands for all database sessions, which is essential for real-time monitoring and performance diagnostics.",
  "query_template": "ALTER SYSTEM SET [parameter_name] = [value];",
  "components": [
    {
      "component": "ALTER SYSTEM",
      "name": "Alter System Statement",
      "description": "Modifies a PostgreSQL configuration parameter globally. This command writes the change to the 'postgresql.auto.conf' file, ensuring the setting persists after a server restart."
    },
    {
      "component": "track_activities",
      "name": "Parameter Name",
      "description": "The specific configuration parameter to change. Other related parameters include 'track_counts', 'track_io_timing', and 'track_functions'."
    },
    {
      "component": "ON | OFF",
      "name": "Parameter Value",
      "description": "The new value for the parameter. For boolean parameters, this is typically 'on' or 'off'."
    }
  ],
  "scenarios": [
    {
      "title": "Enabling Real-Time Query Monitoring to Troubleshoot Performance",
      "details": {
        "setup": "As a database administrator, you are receiving reports of intermittent slowdowns on your application server. You suspect long-running queries might be the cause, but the default view in `pg_stat_activity` does not show the text of the queries being executed.",
        "goal": "To enable the real-time tracking of all executing commands so you can connect to the database at any time and see exactly which queries are running, who is running them, and for how long.",
        "outcome": "The `pg_stat_activity` view is now populated with the complete query text for all active sessions, allowing for immediate identification of problematic, long-running, or stuck queries."
      },
      "example": {
        "query": "ALTER SYSTEM SET track_activities = on;",
        "explanation": "This query tells PostgreSQL to permanently enable the `track_activities` parameter by writing it to `postgresql.auto.conf`. This setting allows the `pg_stat_activity.query` column to be populated. Note: This change does not apply immediately."
      },
      "notes": [
        "IMPORTANT: After running `ALTER SYSTEM`, you must run `SELECT pg_reload_conf();` for the change to take effect without restarting the server.",
        "Enabling this parameter adds a small amount of performance overhead, but it is generally considered safe and highly valuable for production monitoring.",
        "You can view the results by querying the `pg_stat_activity` view: `SELECT pid, usename, datname, query, state FROM pg_stat_activity;`",
        "This command requires superuser privileges to execute."
      ]
    }
  ]
};