export const command2 = {
  // --- Converted to 'query' type ---
  "type": "query",

  "path": [
    "Configuration Management",
    "Server Configuration",
    "Reset Configuration"
  ],
  "description": "Resets a configuration parameter that was previously set with `ALTER SYSTEM`, removing its entry from the `postgresql.auto.conf` file. The parameter will revert to the value in `postgresql.conf` or the system default upon the next server reload.",
  
  // --- Query-specific fields ---
  "query_template": "ALTER SYSTEM RESET [parameter_name];",
  "components": [
    {
      "component": "ALTER SYSTEM RESET",
      "name": "Reset Command",
      "description": "The SQL command used to remove a specific parameter override from the `postgresql.auto.conf` file."
    },
    {
      "component": "[parameter_name]",
      "name": "Parameter Name",
      "description": "The name of the server configuration parameter you wish to reset (e.g., `shared_buffers`)."
    },
    {
      "component": "ALL",
      "name": "Reset All (Alternative)",
      "description": "Using `ALTER SYSTEM RESET ALL;` will remove all parameter settings from `postgresql.auto.conf`."
    }
  ],
  
  // --- Detailed Scenario ---
  "scenarios": [
    {
      "title": "Reverting a Problematic Performance Tuning Change",
      "details": {
        "setup": "A database administrator attempted to increase the memory allocated to PostgreSQL by running `ALTER SYSTEM SET shared_buffers = '8GB';`. After restarting the server, PostgreSQL fails to start because the new value is too high for the available system memory, causing a crash loop.",
        "goal": "To safely remove the invalid `shared_buffers` setting from the configuration so that the server can start successfully using its previous, valid value from `postgresql.conf`.",
        "outcome": "The `shared_buffers` entry is removed from `postgresql.auto.conf`. The server is now able to start, and `shared_buffers` has reverted to its former, stable value."
      },
      "example": {
        "query": "ALTER SYSTEM RESET shared_buffers;",
        "explanation": "This query targets and removes only the `shared_buffers` setting from the `postgresql.auto.conf` file. It's the standard and safest way to undo a specific change made with `ALTER SYSTEM SET` without affecting other runtime settings."
      },
      "notes": [
        "A server **reload** (using `pg_ctl reload` or `SELECT pg_reload_conf();`) or a **restart** is required for this change to take effect.",
        "This command only affects settings in the `postgresql.auto.conf` file; it does not modify your main `postgresql.conf` file.",
        "To execute this command, you need superuser privileges."
      ]
    }
  ]
};