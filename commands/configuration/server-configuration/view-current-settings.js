export const command0 = {
  // --- This is a query, not a command-line utility ---
  "type": "query",

  "path": [
    "Configuration Management",
    "Server Configuration",
    "View Current Settings"
  ],
  "description": "Displays the current values of PostgreSQL server configuration parameters. This is essential for auditing, troubleshooting, and understanding the server's behavior.",
  
  // --- Query-specific structure ---
  "query_template": "SHOW [parameter_name | ALL];  -- OR --  SELECT * FROM pg_settings;",
  "components": [
    {
      "component": "SHOW parameter_name;",
      "name": "Show Specific Parameter",
      "description": "The quickest way to check the value of a single, known configuration parameter."
    },
    {
      "component": "SHOW ALL;",
      "name": "Show All Parameters",
      "description": "Provides a simple, human-readable list of all 300+ configuration settings and their current values."
    },
    {
      "component": "SELECT * FROM pg_settings;",
      "name": "Query the pg_settings View",
      "description": "The most powerful and flexible method. It exposes all parameters and their metadata in a table that can be filtered and queried with standard SQL."
    }
  ],

  "scenarios": [
    {
      "title": "Verifying a Critical Performance Tuning Parameter",
      "details": {
        "setup": "Your application is running complex analytical queries that are performing poorly. You suspect that the memory allocated for sorting (`work_mem`) is too low, causing operations to spill to disk.",
        "goal": "To instantly check the current value of the `work_mem` parameter as seen by your active session.",
        "outcome": "The query returns the current setting (e.g., '4MB'), confirming it is lower than expected and needs to be adjusted."
      },
      "example": {
        "query": "SHOW work_mem;",
        "explanation": "The `SHOW` command is the most direct way to inspect a single parameter's value. It's simple, fast, and perfect for targeted troubleshooting."
      },
      "notes": [
        "To change a setting, you might use `ALTER SYSTEM SET work_mem = '256MB';` followed by `SELECT pg_reload_conf();`."
      ]
    },
    {
      "title": "Auditing All Security and Logging Settings",
      "details": {
        "setup": "As part of a regular security audit, you need to document all configuration parameters related to client authentication, SSL, and statement logging.",
        "goal": "To get a complete, filterable list of all server settings, allowing you to easily isolate only those relevant to the audit.",
        "outcome": "You get a structured table of all relevant parameters, which can be exported or reviewed directly."
      },
      "example": {
        "query": "SELECT name, setting, short_desc, context FROM pg_settings WHERE category IN ('Client Connection Defaults', 'Reporting and Logging');",
        "explanation": "Querying the `pg_settings` view provides a powerful way to inspect configuration. Unlike `SHOW ALL`, it allows for standard SQL `WHERE` clauses, making it easy to find and group related settings programmatically."
      },
      "notes": [
        "The `pg_settings` view also shows the `source` of the setting (e.g., configuration file, environment variable, or default)."
      ]
    },
    {
      "title": "Getting a Quick Snapshot of a New Server",
      "details": {
        "setup": "You are connecting to an unfamiliar PostgreSQL server for the first time and need a quick overview of its entire configuration.",
        "goal": "To display all current parameters and their values in a simple, easy-to-read list for a manual review.",
        "outcome": "A two-column list of all server parameters and their current values is displayed directly in your SQL client."
      },
      "example": {
        "query": "SHOW ALL;",
        "explanation": "`SHOW ALL` is excellent for getting a comprehensive, human-readable snapshot of the server's state without the complexity of a `SELECT` query. It's ideal for a quick manual check."
      },
      "notes": [
        "The output of `SHOW ALL` is designed for readability in a tool like `psql`, whereas `SELECT * FROM pg_settings` is better for scripts or applications that need to parse the output."
      ]
    }
  ]
};