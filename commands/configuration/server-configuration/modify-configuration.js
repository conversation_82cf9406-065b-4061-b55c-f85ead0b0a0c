export const command1 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Server Configuration",
    "Modify Configuration"
  ],
  "description": "Permanently modifies a global server configuration parameter in the postgresql.auto.conf file. This method has the highest precedence and overrides settings in postgresql.conf. Some parameter changes require a configuration reload, while others require a full server restart to take effect.",
  "query_template": "ALTER SYSTEM SET [parameter_name] TO '[new_value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Command",
      "description": "The SQL command used to modify a configuration parameter in the postgresql.auto.conf file."
    },
    {
      "component": "[parameter_name]",
      "name": "Parameter Name",
      "description": "The name of the configuration parameter you wish to change (e.g., work_mem, shared_buffers)."
    },
    {
      "component": "TO '[new_value]'",
      "name": "Parameter Value",
      "description": "The new value for the specified parameter. String values must be enclosed in single quotes."
    }
  ],
  "scenarios": [
    {
      "title": "Increase Memory for Sort Operations Without a Restart",
      "details": {
        "setup": "Your database is running complex analytical queries that are performing poorly. You've noticed from query plans (`EXPLAIN ANALYZE`) that large sort operations are writing to disk instead of being processed in memory.",
        "goal": "To increase the `work_mem` parameter to allow larger sort operations to occur in RAM, thereby improving query performance without incurring downtime.",
        "outcome": "The `work_mem` setting is updated. After reloading the configuration, the analytical queries run faster, and query plans show 'in-memory sort' instead of 'external disk sort'."
      },
      "example": {
        "query": "ALTER SYSTEM SET work_mem = '128MB';",
        "explanation": "This command sets the `work_mem` parameter to 128MB. Since `work_mem` is a parameter that does not require a restart, the change can be applied immediately by running `SELECT pg_reload_conf();`. This allows for dynamic performance tuning on a live server."
      },
      "notes": [
        "After running `ALTER SYSTEM`, you must run `SELECT pg_reload_conf();` for the change to take effect.",
        "`work_mem` is applied per-operation, so a single query with multiple sort nodes can use several times this amount of memory.",
        "Always test changes in a staging environment before applying them to production."
      ]
    },
    {
      "title": "Allocate More System Memory to the Database Buffer Cache (Requires Restart)",
      "details": {
        "setup": "You are managing a production database server with 64GB of RAM, but the database performance is suboptimal due to high disk I/O. Investigation reveals that `shared_buffers` is set to the default low value of 128MB.",
        "goal": "To increase the `shared_buffers` to a value appropriate for the server's RAM (typically 25% of total RAM) to improve the cache-hit ratio and overall performance.",
        "outcome": "The configuration change is queued. After the next scheduled server restart, PostgreSQL starts with the new 16GB buffer pool, significantly reducing disk I/O and improving response times."
      },
      "example": {
        "query": "ALTER SYSTEM SET shared_buffers = '16GB';",
        "explanation": "This command writes `shared_buffers = '16GB'` to `postgresql.auto.conf`. Because `shared_buffers` is a fundamental memory pool allocated only when PostgreSQL starts, a full server restart is required for this change to be applied."
      },
      "notes": [
        "This change will NOT take effect until the PostgreSQL service is restarted (e.g., `sudo systemctl restart postgresql`).",
        "Setting `shared_buffers` too high can negatively impact system performance by taking memory away from the OS for other tasks.",
        "Use `ALTER SYSTEM RESET shared_buffers;` to remove the setting from `postgresql.auto.conf` and revert to the value in `postgresql.conf`."
      ]
    }
  ]
};