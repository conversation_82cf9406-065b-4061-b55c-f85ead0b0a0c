export const command4 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Memory Settings",
    "Configure Work Memory"
  ],
  "description": "Sets the maximum amount of memory to be used by a query operation (such as a sort or hash table) before writing to temporary disk files. Setting this value appropriately is critical for query performance.",
  "query_template": "ALTER SYSTEM SET work_mem = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "System-Wide Configuration",
      "description": "Modifies a PostgreSQL configuration parameter globally. This command writes the setting to the 'postgresql.auto.conf' file and requires a configuration reload to take effect."
    },
    {
      "component": "work_mem = '[value]'",
      "name": "Work Memory Assignment",
      "description": "Specifies the amount of memory for internal sort and hash operations. The value is a string with units (e.g., '16MB', '256KB')."
    }
  ],
  "scenarios": [
    {
      "title": "Optimizing Slow Analytical Queries with Large Sorts",
      "details": {
        "setup": "A reporting database runs complex analytical queries with large `ORDER BY`, `GROUP BY`, or window functions. `EXPLAIN ANALYZE` output for these queries shows 'External Merge Disk Sort', indicating that PostgreSQL is running out of memory and spilling sort data to disk, which is very slow.",
        "goal": "To increase `work_mem` so that these large sort operations can be performed entirely in memory, dramatically speeding up the reports.",
        "outcome": "After increasing `work_mem` and reloading the configuration, the analytical queries run significantly faster. A new `EXPLAIN ANALYE` shows 'in-memory sort' instead of a disk-based sort."
      },
      "example": {
        "query": "ALTER SYSTEM SET work_mem = '32MB';",
        "explanation": "This command sets the default work memory for all sessions to 32MB. This provides enough memory for the reporting queries to avoid spilling to disk. After running this, you must run `SELECT pg_reload_conf();` or restart the server for the change to take effect."
      },
      "notes": [
        "Warning: Be very careful when setting `work_mem`. The total potential memory usage is `work_mem * max_connections`. A high `work_mem` can lead to server memory exhaustion.",
        "For a single, very large query, it is often safer to set `work_mem` only for the current session: `SET work_mem = '256MB';` before you run your query.",
        "Consider `maintenance_work_mem` for operations like `VACUUM` and `CREATE INDEX`. It is configured separately and can typically be set much higher than `work_mem`."
      ]
    }
  ]
};