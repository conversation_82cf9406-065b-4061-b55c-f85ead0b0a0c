export const command3 = {
  "type": "query",
  "path": [
    "Configuration Management",
    "Memory Settings",
    "Configure Shared Buffers"
  ],
  "description": "Sets the amount of memory PostgreSQL dedicates to its primary data cache (shared buffers). This is one of the most critical performance tuning settings, as a larger cache can significantly reduce disk I/O and accelerate query performance.",
  "query_template": "ALTER SYSTEM SET shared_buffers = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET",
      "name": "Persistent Configuration Change",
      "description": "A SQL command that writes the given configuration parameter directly to the `postgresql.auto.conf` file. This change persists across server restarts."
    },
    {
      "component": "shared_buffers = '[value]'",
      "name": "Setting and Value",
      "description": "Specifies the parameter to change and its new value. The value is a string and can use units like `MB` or `GB`."
    }
  ],
  "scenarios": [
    {
      "title": "Optimizing Cache Performance for a Read-Heavy Application",
      "details": {
        "setup": "You are managing a PostgreSQL database on a dedicated server with 32GB of RAM. The application is a reporting dashboard, which involves many complex, read-heavy queries. Monitoring shows high disk read activity (I/O wait) and a low cache-hit ratio.",
        "goal": "To increase the size of `shared_buffers` to allow more of the frequently accessed data to be cached in RAM, thereby reducing disk reads and speeding up dashboard loading times.",
        "outcome": "The `shared_buffers` setting is persistently updated. After a configuration reload, the database's cache-hit ratio improves, and application queries respond noticeably faster."
      },
      "example": {
        "query": "ALTER SYSTEM SET shared_buffers = '8GB';",
        "explanation": "This query sets the `shared_buffers` to 8GB, which is 25% of the server's 32GB RAM—a common and effective starting point for a dedicated database server. The `ALTER SYSTEM` command ensures this setting will be automatically applied the next time PostgreSQL starts."
      },
      "notes": [
        "A configuration reload is required for this change to take effect. You can do this without a full restart by running `SELECT pg_reload_conf();` as a superuser.",
        "The general recommendation for `shared_buffers` on a dedicated database server is 25% of the total system RAM.",
        "Avoid setting `shared_buffers` too high (e.g., >40% of RAM), as it can starve the operating system's own file system cache, leading to poor performance.",
        "This setting is different from `work_mem`, which is temporary memory allocated for individual operations like sorting or hashing within a query."
      ]
    }
  ]
};