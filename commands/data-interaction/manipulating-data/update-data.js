export const command17 = {
  // --- Identifier for your app's logic ---
  "type": "query",

  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Update Data"
  ],
  "description": "Modifies existing records in a table that match a specified condition. The WHERE clause is crucial to target specific rows; omitting it will dangerously update every row in the table.",
  
  // --- Query-specific fields ---
  "query_template": "UPDATE [table_name] SET [column1] = [value1], [column2] = [value2] WHERE [condition];",
  "components": [
    {
      "component": "UPDATE [table_name]",
      "name": "Target Table",
      "description": "Specifies the table where the records will be modified."
    },
    {
      "component": "SET [column] = [value]",
      "name": "Set Clause",
      "description": "The assignment part of the query. It specifies which column(s) to change and what their new values should be."
    },
    {
      "component": "WHERE [condition]",
      "name": "Condition Clause",
      "description": "A critical filter that determines exactly which rows should be updated. If this is omitted, all rows in the table will be updated."
    }
  ],

  // --- <PERSON><PERSON><PERSON>s providing professional context ---
  "scenarios": [
    {
      "title": "Correcting a Single Employee's Information",
      "details": {
        "setup": "An employee, <PERSON>, has moved to a new department. His record in the `employees` table needs to be updated. His employee ID is 42.",
        "goal": "To change the `department_id` for the specific employee with `employee_id` 42, without affecting any other records.",
        "outcome": "The department for employee 42 is successfully changed in the database, and a quick `SELECT` confirms the new value."
      },
      "example": {
        "query": "UPDATE employees SET department_id = 5 WHERE employee_id = 42;",
        "explanation": "This query targets the `employees` table. The `SET` clause assigns the new department ID. Critically, the `WHERE employee_id = 42` clause ensures that only this single employee record is modified."
      },
      "notes": [
        "Best Practice: Before running an `UPDATE`, write a `SELECT` statement with the same `WHERE` clause to verify you are targeting the correct rows. For example: `SELECT * FROM employees WHERE employee_id = 42;`"
      ]
    },
    {
      "title": "Applying a Price Increase to a Product Category",
      "details": {
        "setup": "Due to increased material costs, the company needs to apply a 10% price increase to all products in the 'Electronics' category.",
        "goal": "To increase the `price` for all products where the `category` is 'Electronics'.",
        "outcome": "The prices of all products in the 'Electronics' category are increased by 10%, while all other product prices remain unchanged."
      },
      "example": {
        "query": "UPDATE products SET price = price * 1.10 WHERE category = 'Electronics';",
        "explanation": "This query updates the `products` table by setting the `price` column to its current value multiplied by 1.10. The `WHERE` clause isolates this change to only those products within the 'Electronics' category."
      },
      "notes": [
        "When performing bulk updates, it is highly recommended to wrap the statement in a transaction (`BEGIN; ... COMMIT;` or `ROLLBACK;`) so you can preview the changes and undo them if they are incorrect."
      ]
    }
  ]
};