export const command15 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Query Data",
    "Select Specific Columns"
  ],
  "description": "Retrieves only specific, named columns from a table, which is a fundamental best practice for efficient data retrieval.",
  "query_template": "SELECT column1, column2 FROM table_name;",
  "components": [
    {
      "component": "SELECT column1, column2",
      "name": "Column Selection",
      "description": "A comma-separated list of the columns you want to retrieve data from. Specifying columns is more efficient than using '*'."
    },
    {
      "component": "FROM table_name",
      "name": "Target Table",
      "description": "The clause that specifies the table from which to retrieve the data."
    }
  ],
  "scenarios": [
    {
      "title": "Optimizing a User Profile API Endpoint",
      "details": {
        "setup": "You have a large `users` table with 30 columns, including sensitive data (`password_hash`, `security_question`) and large objects (`profile_picture_blob`). An API endpoint that fetches user data is slow because it's using `SELECT * FROM users;`, retrieving unnecessary and potentially sensitive information.",
        "goal": "To improve the performance and security of the API by retrieving only the essential user information needed for a public profile: `username`, `full_name`, and `join_date`.",
        "outcome": "A lean and efficient query that returns only the three specified columns, reducing network traffic, database load, and the risk of exposing sensitive data."
      },
      "example": {
        "query": "SELECT username, full_name, join_date FROM users WHERE user_id = '1a2b3c4d-5e6f-7890-1234-567890abcdef';",
        "explanation": "This query explicitly lists the required columns. By avoiding `SELECT *`, the database does less work, the amount of data sent over the network is minimized, and the application's memory usage is reduced, leading to a faster and more secure API."
      },
      "notes": [
        "Always specify the columns you need. It makes your application more resilient to table structure changes (e.g., adding or removing columns).",
        "This practice is crucial for performance, especially on tables with many columns or columns containing large amounts of data (like `TEXT` or `BYTEA`)."
      ]
    }
  ]
};