export const command14 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Query Data",
    "Select All Columns"
  ],
  "description": "Retrieves all columns from all rows in a specified table. While useful for quick data inspection, it is a significant anti-pattern in production code due to performance and maintenance risks.",
  "query_template": "SELECT * FROM [table_name];",
  "components": [
    {
      "component": "SELECT *",
      "name": "Select All Columns",
      "description": "The `*` is a wildcard character that represents all columns in the table."
    },
    {
      "component": "FROM [table_name]",
      "name": "From Table",
      "description": "Specifies the table from which to retrieve the data."
    }
  ],
  "scenarios": [
    {
      "title": "Good Practice: Quick Data Inspection in a Development Environment",
      "details": {
        "setup": "You are a developer who has just created a new `products` table and populated it with a few sample rows for testing purposes.",
        "goal": "You need to quickly verify that all the data, including auto-generated IDs and default timestamps, was inserted correctly.",
        "outcome": "A complete result set showing all columns and the newly inserted rows is returned, allowing for immediate confirmation of the data's integrity."
      },
      "example": {
        "query": "SELECT * FROM products;",
        "explanation": "In this context, `SELECT *` is perfectly acceptable. It provides a fast and simple way to view the entire contents of a small, non-production table for debugging or verification."
      },
      "notes": [
        "This practice is safe and efficient for tables with a small number of rows and columns during development."
      ]
    },
    {
      "title": "Bad Practice: The Pitfall of Using `SELECT *` in Production Code",
      "details": {
        "setup": "A production application has a feature to display user profiles. The backend code uses `SELECT * FROM users WHERE user_id = ?;` to fetch user data. The `users` table is large and contains many columns, including sensitive ones like `password_hash` and large ones like `profile_picture_blob`.",
        "goal": "The application only needs to display the `username`, `full_name`, and `join_date`.",
        "outcome": "The query needlessly retrieves all columns, causing high memory usage on the database server and increased network latency. It also creates a security risk by pulling sensitive data into the application layer unnecessarily. If a new column is added to the `users` table, it could break the application.",
        "alternative_fix": "The query should be rewritten to specify only the required columns, like this: \n`SELECT username, full_name, join_date FROM users WHERE user_id = ?;`"
      },
      "example": {
        "query": "-- ANTI-PATTERN: Do not do this in production code\nSELECT * FROM users WHERE user_id = 101;",
        "explanation": "This is a dangerous anti-pattern. Fetching unnecessary data leads to poor performance and security vulnerabilities. Explicitly listing the columns makes the query more efficient, secure, and resilient to future changes in the table schema."
      },
      "notes": [
        "Best Practice: Always specify the exact columns you need in your application's SQL queries.",
        "Using `SELECT *` in production can lead to fragile code that breaks when the database schema is modified."
      ]
    }
  ]
};