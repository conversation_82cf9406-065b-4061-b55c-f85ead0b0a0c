export const command16 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Query Data",
    "Select with a Filter"
  ],
  "description": "Retrieves a subset of rows from a table that match one or more specific criteria, forming the basis of most data retrieval operations.",
  "query_template": "SELECT [columns] FROM [table_name] WHERE [condition];",
  "components": [
    {
      "component": "SELECT [columns]",
      "name": "Column Selection",
      "description": "Specifies which columns you want to retrieve. Use '*' to select all columns."
    },
    {
      "component": "FROM [table_name]",
      "name": "Table Selection",
      "description": "Specifies the table from which to retrieve the data."
    },
    {
      "component": "WHERE [condition]",
      "name": "Filtering Condition",
      "description": "The clause that filters the rows. Only rows for which the condition evaluates to true will be returned."
    }
  ],
  "scenarios": [
    {
      "title": "Find an Employee's Record in an HR System",
      "details": {
        "setup": "An HR administrator needs to access the complete record for an employee named '<PERSON>' to verify their employment details.",
        "goal": "To retrieve all available information for every employee with the last name '<PERSON>' from the company's 'employees' table.",
        "outcome": "A result set containing all columns for the rows where the 'last_name' field is exactly 'Martinez'."
      },
      "example": {
        "query": "SELECT * FROM employees WHERE last_name = 'Martinez';",
        "explanation": "The `*` selects all columns, providing the complete record. The `WHERE` clause filters the millions of potential rows down to only those for employees named 'Martinez', making it a fast and targeted operation."
      },
      "notes": [
        "For better performance on large tables, ensure there is an index on the 'last_name' column.",
        "String comparisons are often case-sensitive by default. `WHERE last_name = 'martinez'` might not return any results if the data is stored as 'Martinez'."
      ]
    },
    {
      "title": "Generate a List of Products to Restock",
      "details": {
        "setup": "An e-commerce inventory manager needs to identify all products that are currently out of stock to create a new purchase order.",
        "goal": "To get a list of product names and their supplier IDs from the 'products' table where the stock quantity is zero.",
        "outcome": "A precise list of products that need to be reordered, which can be exported or sent to another system."
      },
      "example": {
        "query": "SELECT product_name, supplier_id FROM products WHERE stock_quantity = 0;",
        "explanation": "This query selects only the necessary information (`product_name`, `supplier_id`) instead of all columns (`*`), making it more efficient. The `WHERE stock_quantity = 0` condition directly targets the out-of-stock items, providing an actionable list."
      },
      "notes": [
        "This type of query is often used as the basis for automated inventory alerting systems.",
        "You can add more conditions, such as `...AND discontinued = false`, to make the filter even more specific."
      ]
    }
  ]
};