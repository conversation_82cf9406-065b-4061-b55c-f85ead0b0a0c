export const command13 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Insert Data"
  ],
  "description": "Adds one or more new rows of data to a table. It is the fundamental command for creating new records.",
  "query_template": "INSERT INTO [table_name] ([column1], [column2], ...) VALUES ([value1], [value2], ...);",
  "components": [
    {
      "component": "INSERT INTO table_name (column1, ...)",
      "name": "Target Definition",
      "description": "Specifies the table and the columns that you will be providing data for. If you omit columns that have default values (like an auto-incrementing ID or a timestamp), PostgreSQL will automatically populate them."
    },
    {
      "component": "VALUES (value1, ...)",
      "name": "Value Clause",
      "description": "Provides the actual data to be inserted. The values must match the order and data type of the columns listed in the target definition."
    }
  ],
  "scenarios": [
    {
      "title": "Registering a New User in an Application",
      "details": {
        "setup": "A new user has just completed the sign-up form on your website. The application backend needs to create a record for them in the `users` table, which has an auto-incrementing `id` primary key and a `registration_date` column with a default value of `NOW()`.",
        "goal": "To securely insert the new user's profile information into the `users` table, letting the database handle the ID and registration timestamp automatically.",
        "outcome": "A new row is created in the `users` table containing the user's information, a unique `id`, and the current timestamp for `registration_date`."
      },
      "example": {
        "query": "INSERT INTO users (username, email, password_hash) VALUES ('alex_jones', '<EMAIL>', 'a1b2c3d4e5f6g7h8...');",
        "explanation": "This query explicitly lists the columns for which we are supplying data. By omitting the `id` and `registration_date` columns, we allow PostgreSQL to automatically apply their default values, which is best practice for generated fields."
      },
      "notes": [
        "In a real application, always use parameterized queries (prepared statements) to prevent SQL injection vulnerabilities.",
        "The returned value from this command can be the OID or, by using the `RETURNING id` clause, you can get the new user's ID back immediately."
      ]
    },
    {
      "title": "Bulk Importing New Product Inventory",
      "details": {
        "setup": "You have a small batch of new products to add to your e-commerce system. Performing a separate database insertion for each product is inefficient.",
        "goal": "To add all new products to the `products` table in a single, efficient database transaction.",
        "outcome": "Three new product records are added to the `products` table using a single `INSERT` statement."
      },
      "example": {
        "query": "INSERT INTO products (sku, name, price, stock_quantity) VALUES ('A4-32-BL', 'Blue Widget', 19.99, 250), ('B8-11-RD', 'Red Gadget', 24.50, 400), ('C1-99-GR', 'Green Gizmo', 9.95, 600);",
        "explanation": "By providing a comma-separated list of value tuples after the `VALUES` keyword, you can insert multiple rows at once. This method is vastly more performant than sending individual `INSERT` statements because it reduces network latency and transaction overhead."
      },
      "notes": [
        "While this multi-row syntax is great for dozens or hundreds of rows, the `COPY` command is the recommended tool for bulk-loading thousands or millions of rows from a file."
      ]
    }
  ]
};