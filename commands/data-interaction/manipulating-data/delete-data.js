export const command18 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Manipulating Data",
    "Delete Data"
  ],
  "description": "Permanently removes one or more rows from a table that match a specific condition. It is a critical DML (Data Manipulation Language) statement that should be used with extreme caution, as the WHERE clause is essential to prevent deleting all rows in a table.",
  "query_template": "DELETE FROM [table_name] WHERE [condition];",
  "components": [
    {
      "component": "DELETE FROM [table_name]",
      "name": "Target Table",
      "description": "Specifies the table from which rows will be removed."
    },
    {
      "component": "WHERE [condition]",
      "name": "Condition",
      "description": "Crucially specifies which rows to delete. If omitted, all rows in the table will be deleted."
    },
    {
      "component": "RETURNING * | [column_list]",
      "name": "Return Deleted Data (Optional)",
      "description": "A powerful PostgreSQL extension that returns the data from the rows that were just deleted."
    }
  ],
  "scenarios": [
    {
      "title": "Decommissioning a User Account",
      "details": {
        "setup": "A user with the ID `8675` has requested their account be deleted from your service in compliance with privacy regulations.",
        "goal": "To permanently and cleanly remove the user's record from the `users` table.",
        "outcome": "The row in the `users` table where the `id` column is `8675` is successfully deleted."
      },
      "example": {
        "query": "DELETE FROM users WHERE id = 8675;",
        "explanation": "This query targets the exact row corresponding to the user's ID. The `WHERE id = 8675` clause is critical to ensure only that specific user is removed, leaving all other user data intact."
      },
      "notes": [
        "Before deleting, consider database constraints. If other tables reference this user via a foreign key, the delete might fail unless cascading deletes are configured.",
        "An alternative to permanent deletion is a 'soft delete', where you set a flag like `is_active = false` instead. This preserves the data for historical or auditing purposes."
      ]
    },
    {
      "title": "Purging and Auditing Old Log Data",
      "details": {
        "setup": "Your `event_logs` table has grown to millions of rows, and company policy dictates that any logs older than 180 days should be purged to save space and improve query performance.",
        "goal": "To delete all log entries older than 180 days and simultaneously retrieve the deleted log data for archival and auditing without running a second query.",
        "outcome": "All specified old log rows are deleted, and the query returns the complete data of every row that was removed."
      },
      "example": {
        "query": "DELETE FROM event_logs WHERE timestamp < NOW() - INTERVAL '180 days' RETURNING *;",
        "explanation": "The `WHERE` clause dynamically calculates the cutoff date by subtracting 180 days from the current time. The `RETURNING *` clause is highly efficient, returning all columns of the deleted rows, which can then be directly logged or saved to an archive file by the application."
      },
      "notes": [
        "Deleting a very large number of rows at once can lock the table for a significant duration and consume a lot of resources. For massive purges, consider deleting in smaller batches within a transaction.",
        "If you need to delete *all* rows from a table, `TRUNCATE TABLE [table_name];` is much faster, but it has major differences (it cannot be rolled back easily and does not fire `DELETE` triggers)."
      ]
    }
  ]
};