export const command19 = {
  "path": [
    "Interacting with Data",
    "Exit psql"
  ],
  "command": "\\q",
  "description": "Exit the psql interactive terminal. You can also press Ctrl+D.",
  "parameters": [
    {
      "flag": "\\q",
      "name": "quit",
      "description": "Exit psql immediately."
    },
    {
      "flag": "Ctrl+D",
      "name": "EOF signal",
      "description": "Alternative way to exit psql using a keyboard shortcut."
    }
  ],
  "scenarios": [
    {
      "title": "Safely Terminating a psql Session",
      "details": {
        "setup": "You are connected to a production database using the psql interactive terminal to perform a routine check on user permissions.",
        "goal": "After completing your checks, you need to securely close the connection to the database to free up server resources and prevent accidental changes.",
        "outcome": "The connection to the PostgreSQL server is terminated, and you are safely returned to your operating system's command prompt."
      },
      "example": {
        "command": "\\q",
        "explanation": "Executing `\\q` sends the quit command directly to the psql client, instructing it to end the current session and close the database connection gracefully."
      },
      "notes": [
        "Using `\\q` or `Ctrl+D` is the standard and safest way to exit.",
        "If you have an open transaction that has not been committed or rolled back, psql will warn you before exiting. Exiting will cause the transaction to be rolled back automatically."
      ]
    }
  ]
};