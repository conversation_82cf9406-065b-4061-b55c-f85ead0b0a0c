export const command27 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Advanced SQL",
    "Common Table Expressions (CTE)"
  ],
  "description": "Use Common Table Expressions (CTEs) to create temporary, named result sets. This improves readability and simplifies complex, multi-step queries, including hierarchical or recursive ones.",
  "query_template": "WITH [cte_name] AS (\n  SELECT ...\n)\nSELECT ... FROM [cte_name];",
  "components": [
    {
      "component": "WITH [cte_name] AS (...)",
      "name": "CTE Definition",
      "description": "The clause that defines a temporary, named result set. You can define multiple CTEs in a sequence, separated by commas."
    },
    {
      "component": "SELECT ... FROM [cte_name]",
      "name": "Final Query",
      "description": "The main query that can reference the CTE(s) as if they were regular tables."
    },
    {
      "component": "WITH RECURSIVE",
      "name": "Recursive CTE",
      "description": "A powerful variant used to query hierarchical data, such as organizational charts or parts explosions."
    }
  ],
  "scenarios": [
    {
      "title": "Simplifying a Multi-Step Sales Report",
      "details": {
        "setup": "You have `orders`, `order_items`, and `customers` tables. Your marketing team needs a report of total sales, but only for customers who have registered in the last year and have made more than 5 purchases in total.",
        "goal": "To write a clear, readable query that first identifies the target customers and then calculates their total sales, without using hard-to-read nested subqueries.",
        "outcome": "A clean report showing the total sales generated from the specified customer segment."
      },
      "example": {
        "query": "WITH eligible_customers AS (\n  SELECT customer_id\n  FROM customers\n  WHERE registration_date >= '2024-01-01'\n  INTERSECT\n  SELECT customer_id\n  FROM orders\n  GROUP BY customer_id\n  HAVING COUNT(*) > 5\n)\nSELECT o.customer_id, SUM(oi.price * oi.quantity) AS total_sales\nFROM orders o\nJOIN order_items oi ON o.order_id = oi.order_id\nWHERE o.customer_id IN (SELECT customer_id FROM eligible_customers)\nGROUP BY o.customer_id;",
        "explanation": "The CTE `eligible_customers` first creates a clean, temporary list of customers who meet the complex criteria. The final `SELECT` statement then becomes much simpler, as it can just reference this list to calculate the sales figures. This separation of logic makes the query easy to debug and maintain."
      },
      "notes": [
        "You can define multiple CTEs in a single `WITH` clause: `WITH cte1 AS (...), cte2 AS (...) SELECT ...`.",
        "CTEs can reference other CTEs defined before them in the same `WITH` clause."
      ]
    },
    {
      "title": "Mapping an Organizational Reporting Structure",
      "details": {
        "setup": "You have an `employees` table with `id`, `name`, and `manager_id` columns, where `manager_id` references another employee's `id`. A manager wants to see all employees in their entire reporting chain, not just their direct reports.",
        "goal": "To generate a complete list of all employees who report up to a specific manager (e.g., the manager with ID 101) at any level of the hierarchy.",
        "outcome": "A result set containing the ID and name of every subordinate employee under the specified manager."
      },
      "example": {
        "query": "WITH RECURSIVE reporting_chain AS (\n  -- Anchor member: the starting point (the manager)\n  SELECT id, name, manager_id\n  FROM employees\n  WHERE id = 101\n\n  UNION ALL\n\n  -- Recursive member: joins employees to the previous level\n  SELECT e.id, e.name, e.manager_id\n  FROM employees e\n  INNER JOIN reporting_chain rc ON e.manager_id = rc.id\n)\nSELECT id, name FROM reporting_chain;",
        "explanation": "This `RECURSIVE` CTE starts with the manager (the 'anchor member'). It then repeatedly joins the `employees` table to itself (the 'recursive member'), finding direct reports of the previous level until the entire hierarchy below the starting manager is traversed."
      },
      "notes": [
        "A recursive CTE must have an 'anchor' (non-recursive) part and a 'recursive' part, combined with `UNION` or `UNION ALL`.",
        "Be careful to avoid infinite loops in recursive CTEs if your data has cycles (e.g., an employee is their own manager)."
      ]
    }
  ]
};