export const command29 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Advanced SQL",
    "JSON Operations"
  ],
  "description": "Demonstrates how to query, extract, and filter data from a JSONB column using specialized operators.",
  "query_template": "SELECT [data_column]->>'[field]' FROM [table] WHERE [data_column] ? '[key]';",
  "components": [
    {
      "component": "->>",
      "name": "Extract as Text Operator",
      "description": "Accesses a JSON object field by its key and returns the value as a 'text' type. Ideal for retrieving final values."
    },
    {
      "component": "->",
      "name": "Extract as JSON Operator",
      "description": "Accesses a JSON object field and returns it as a 'jsonb' type. Use this when you need to chain operators to access nested data."
    },
    {
      "component": "?",
      "name": "Key Exists Operator",
      "description": "A boolean operator used in a WHERE clause to check if a specific key exists at the top level of a JSON object. This is highly efficient and can use a GIN index."
    }
  ],
  "scenarios": [
    {
      "title": "Filtering and Extracting User Profile Data for a Report",
      "details": {
        "setup": "You have a `users` table with a `profile` column of type `jsonb`. This column stores flexible user data, including name, contact details, and a nested address object. Not all users have a complete profile.",
        "goal": "To generate a list of names and cities for all users who have an email address registered in their profile, to be used for a marketing campaign.",
        "outcome": "A tabular result is produced with two columns, 'name' and 'city', containing data only from users with an 'email' key in their `profile`."
      },
      "example": {
        "query": "SELECT profile->>'name' AS name, profile->'address'->>'city' AS city FROM users WHERE profile ? 'email';",
        "explanation": "This query effectively filters and extracts the needed data. `WHERE profile ? 'email'` efficiently selects only the rows where the 'email' key is present. The expression `profile->'address'->>'city'` demonstrates how to chain operators: first, `->'address'` extracts the address object as JSON, and then `->>'city'` extracts the city value from that nested object as text."
      },
      "notes": [
        "For performance on large tables, it is crucial to add a GIN index on the JSONB column: `CREATE INDEX idx_gin_users_profile ON users USING GIN (profile);`",
        "The `jsonb` data type is generally preferred over `json` because it stores data in a decomposed binary format, which is faster to process and supports indexing.",
        "Remember the key difference: `->` returns a JSON object, while `->>` returns the final value as text."
      ]
    },
    {
      "title": "Finding Products with Specific Attributes",
      "details": {
        "setup": "An `products` table contains an `attributes` column (type `jsonb`) that stores a wide variety of product-specific details like 'color', 'size', or 'features'.",
        "goal": "To find all products that have a 'color' attribute set to 'blue' and also have a 'warranty' section defined.",
        "outcome": "A list of product names that are available in blue and come with a warranty."
      },
      "example": {
        "query": "SELECT product_name FROM products WHERE attributes->>'color' = 'blue' AND attributes ? 'warranty';",
        "explanation": "This query combines two different JSONB conditions. `attributes->>'color' = 'blue'` performs a value comparison by first extracting the color as text. `AND attributes ? 'warranty'` ensures that the resulting products also have a warranty defined, regardless of its value."
      },
      "notes": [
        "This type of query is extremely powerful for e-commerce sites or catalogs with semi-structured product data, as it avoids the need for a rigid schema with many potentially null columns."
      ]
    }
  ]
};