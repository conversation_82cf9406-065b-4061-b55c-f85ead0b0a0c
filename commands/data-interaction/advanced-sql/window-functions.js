export const command28 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Advanced SQL",
    "Window Functions"
  ],
  "description": "Performs calculations across a set of table rows that are somehow related to the current row. This is comparable to an aggregate function but it does not group the result set into a single output row.",
  "query_template": "SELECT [columns], [window_function]() OVER (PARTITION BY [grouping_column] ORDER BY [ordering_column]) FROM [table];",
  "components": [
    {
      "component": "RANK() | ROW_NUMBER() | LAG() | ...",
      "name": "Window Function",
      "description": "The function to compute over the window (e.g., RANK, DENSE_RANK, LAG, LEAD, SUM)."
    },
    {
      "component": "OVER (...)",
      "name": "OVER Clause",
      "description": "Specifies the window of rows the function should operate on."
    },
    {
      "component": "PARTITION BY [column]",
      "name": "Partition Clause (Optional)",
      "description": "Divides the rows into partitions, or groups. The window function is applied to each partition independently."
    },
    {
      "component": "ORDER BY [column]",
      "name": "Order Clause",
      "description": "Orders the rows within each partition. This is required for order-sensitive functions like RANK() and LAG()."
    }
  ],
  "scenarios": [
    {
      "title": "Find the Top 3 Highest Earners in Each Department",
      "details": {
        "setup": "You have an `employees` table containing employee names, their departments, and their salaries. The business needs to identify the top performers, salary-wise, within every single department.",
        "goal": "To produce a ranked list of employees based on their salary, with the ranking restarting for each department.",
        "outcome": "A table showing each employee's name, department, salary, and their salary rank within that department. This allows you to easily filter for ranks 1, 2, and 3 for each department."
      },
      "example": {
        "query": "SELECT name, department, salary, RANK() OVER (PARTITION BY department ORDER BY salary DESC) as department_rank FROM employees;",
        "explanation": "The `PARTITION BY department` clause is the key: it creates a separate 'window' for each unique department. The `RANK()` function then calculates the rank based on salary (`ORDER BY salary DESC`) independently for each of these windows."
      },
      "notes": [
        "Use `RANK()` if you want ties to have the same rank (e.g., 1, 2, 2, 4). Use `DENSE_RANK()` for ties without skipping the next rank (1, 2, 2, 3). Use `ROW_NUMBER()` to assign a unique number to every row regardless of ties (1, 2, 3, 4)."
      ]
    },
    {
      "title": "Calculate Year-Over-Year Sales Growth",
      "details": {
        "setup": "You have a `monthly_sales` table with `sale_month` and `revenue` columns, tracking revenue for the past several years.",
        "goal": "To compare each month's revenue with the revenue from the same month in the previous year, in order to calculate year-over-year growth percentage.",
        "outcome": "A report that shows each month's revenue directly alongside the revenue from 12 months prior in a new `previous_year_revenue` column."
      },
      "example": {
        "query": "SELECT sale_month, revenue, LAG(revenue, 12) OVER (ORDER BY sale_month) AS previous_year_revenue FROM monthly_sales;",
        "explanation": "The `LAG(revenue, 12)` function looks back 12 rows in the window (since the data is ordered by month). This effectively retrieves the revenue from the same month in the prior year. There is no `PARTITION BY` because we are analyzing a single continuous timeline."
      },
      "notes": [
        "The first 12 rows in the result will have `NULL` for `previous_year_revenue` because there is no preceding data.",
        "You can use the `LEAD()` function in a similar way to see future values (e.g., compare this month's revenue to next month's)."
      ]
    }
  ]
};