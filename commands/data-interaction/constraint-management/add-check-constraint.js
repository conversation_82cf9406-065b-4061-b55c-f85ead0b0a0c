export const command26 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Constraint Management",
    "Add Check Constraint"
  ],
  "description": "Adds a check constraint to a table, which enforces a specific data validation rule at the database level for every row. This is a powerful tool for maintaining data integrity.",
  "query_template": "ALTER TABLE [table_name] ADD CONSTRAINT [constraint_name] CHECK ([condition]);",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "Specifies the existing table you intend to modify."
    },
    {
      "component": "ADD CONSTRAINT [constraint_name]",
      "name": "Add Constraint Clause",
      "description": "Indicates that you are adding a new, named constraint to the table."
    },
    {
      "component": "CHECK ([condition])",
      "name": "Check Condition",
      "description": "Defines the business rule as a boolean expression. If the expression evaluates to `true` or `NULL`, the row is valid; if `false`, the operation (INSERT/UPDATE) is rejected."
    }
  ],
  "scenarios": [
    {
      "title": "Ensuring Product Prices and Stock Levels are Valid",
      "details": {
        "setup": "You are managing an e-commerce database. In the `products` table, application-level bugs have occasionally allowed products to be saved with a negative `stock_count` or a `price` of zero, leading to order and inventory errors.",
        "goal": "To enforce two critical business rules at the database level: the price of any product must be greater than zero, and the stock count cannot be negative.",
        "outcome": "Two new constraints are added to the `products` table. Any future `INSERT` or `UPDATE` that violates these rules will be blocked by the database with a 'check constraint violation' error, preventing bad data from ever being saved."
      },
      "example": {
        "query": "ALTER TABLE products ADD CONSTRAINT chk_price_positive CHECK (price > 0);\nALTER TABLE products ADD CONSTRAINT chk_stock_non_negative CHECK (stock_count >= 0);",
        "explanation": "The first command adds `chk_price_positive`, ensuring that every product's price is a positive value. The second command adds `chk_stock_non_negative`, ensuring the quantity on hand is never less than zero. This guarantees data integrity regardless of how the data is entered."
      },
      "notes": [
        "When adding a constraint to a large, existing table, the database must scan every row to validate it. This can lock the table and take a significant amount of time.",
        "To add a constraint without validating existing rows (use with caution), you can use `ALTER TABLE products ADD CONSTRAINT chk_price_positive NOT VALID;`, and then validate it later with `ALTER TABLE products VALIDATE CONSTRAINT chk_price_positive;` during a maintenance window."
      ]
    }
  ]
};