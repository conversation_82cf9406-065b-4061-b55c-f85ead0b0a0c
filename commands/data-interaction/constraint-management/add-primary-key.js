export const command24 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Constraint Management",
    "Add Primary Key"
  ],
  "description": "Adds a primary key constraint to an existing table. A primary key uniquely identifies each record in a table, enforcing both uniqueness and NOT NULL constraints on the specified column(s).",
  "query_template": "ALTER TABLE [table_name] ADD CONSTRAINT [constraint_name] PRIMARY KEY ([column1], [column2], ...);",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Alter Table",
      "description": "The SQL command used to modify the definition of an existing table."
    },
    {
      "component": "ADD CONSTRAINT [constraint_name]",
      "name": "Add Constraint Clause",
      "description": "Specifies that you are adding a new, named constraint. Providing a custom name makes it easier to manage or drop the constraint later."
    },
    {
      "component": "PRIMARY KEY ([column1], ...)",
      "name": "Primary Key Definition",
      "description": "Defines the constraint type and specifies the column(s) that form the key. The values in this column (or combination of columns) must be unique and not null."
    }
  ],
  "scenarios": [
    {
      "title": "Establishing Data Integrity on an Imported User Table",
      "details": {
        "setup": "You have just imported 100,000 user records into a new `web_users` table from a CSV file. The table has an `user_id` column that should be unique, but there is currently no database-level constraint to guarantee this.",
        "goal": "To formally designate the `user_id` column as the primary key, thereby enforcing data integrity and preventing any future duplicate or null entries.",
        "outcome": "A primary key constraint named `pk_web_users` is successfully added. The database now automatically rejects any attempt to insert a user with a duplicate or null `user_id`."
      },
      "example": {
        "query": "ALTER TABLE web_users ADD CONSTRAINT pk_web_users PRIMARY KEY (user_id);",
        "explanation": "This command modifies the `web_users` table, adding a new primary key constraint named `pk_web_users`. It uses the `user_id` column to uniquely identify each row. PostgreSQL will also automatically create a unique B-tree index on `user_id` to efficiently enforce this constraint."
      },
      "notes": [
        "This command will fail if the `user_id` column currently contains any duplicate values or NULLs. You must clean the data first.",
        "To check for duplicates before running, use: `SELECT user_id, COUNT(*) FROM web_users GROUP BY user_id HAVING COUNT(*) > 1;`",
        "On very large tables, adding a primary key can be a blocking operation that takes time, as it must scan the entire table to validate the constraint."
      ]
    }
  ]
};
