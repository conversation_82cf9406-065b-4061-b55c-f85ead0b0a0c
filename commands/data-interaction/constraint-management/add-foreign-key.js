export const command25 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Constraint Management",
    "Add Foreign Key"
  ],
  "description": "Adds a foreign key constraint to a table to enforce referential integrity, ensuring that a value in one column corresponds to an existing value in another table.",
  "query_template": "ALTER TABLE [table_name] ADD CONSTRAINT [constraint_name] FOREIGN KEY ([local_column]) REFERENCES [foreign_table]([foreign_column]) [ON DELETE action] [ON UPDATE action];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "Specifies the existing table you want to modify."
    },
    {
      "component": "ADD CONSTRAINT [constraint_name]",
      "name": "Add Named Constraint",
      "description": "Clause to add a new constraint with a unique, descriptive name."
    },
    {
      "component": "FOREIGN KEY ([local_column])",
      "name": "Local Column",
      "description": "The column in the target table that will hold the reference."
    },
    {
      "component": "REFERENCES [foreign_table]([foreign_column])",
      "name": "Foreign Table Reference",
      "description": "The table and primary key column that the local column must reference."
    },
    {
      "component": "ON DELETE [action]",
      "name": "On Delete Action",
      "description": "(Optional) Defines what happens to this row if the referenced row is deleted. Common actions are `NO ACTION`, `RESTRICT`, `CASCADE`, `SET NULL`."
    }
  ],
  "scenarios": [
    {
      "title": "Ensuring Order-to-Customer Integrity in an E-commerce System",
      "details": {
        "setup": "You have an `orders` table and a `customers` table. The `orders` table has a `customer_id` column, but there is nothing stopping an application bug from inserting an order with a `customer_id` that doesn't exist in the `customers` table, leading to orphaned data.",
        "goal": "To create a database-level rule that guarantees every `customer_id` in the `orders` table refers to a valid `id` in the `customers` table.",
        "outcome": "The `fk_orders_customer` constraint is created. The database will now automatically reject any `INSERT` into `orders` if the `customer_id` does not exist in `customers`, ensuring data integrity."
      },
      "example": {
        "query": "ALTER TABLE orders ADD CONSTRAINT fk_orders_customer FOREIGN KEY (customer_id) REFERENCES customers(id);",
        "explanation": "This query modifies the `orders` table to add a foreign key constraint named `fk_orders_customer`. It links the `customer_id` column in the `orders` table to the `id` column in the `customers` table, enforcing a parent-child relationship."
      },
      "notes": [
        "Adding a foreign key constraint requires a brief lock on both tables to validate existing data. On very large tables, this can cause a temporary delay.",
        "It's a best practice to name your constraints clearly (e.g., `fk_table_reftable`) to make debugging easier."
      ]
    },
    {
      "title": "Automating Data Cleanup with Cascading Deletes",
      "details": {
        "setup": "An application has a `users` table and a `user_posts` table that contains all posts made by a user. When a user requests to delete their account, all of their posts must also be deleted to comply with data privacy rules.",
        "goal": "To configure the database to automatically delete all of a user's posts whenever the corresponding user record is deleted, without requiring extra application logic.",
        "outcome": "The foreign key is created with the `ON DELETE CASCADE` option. Now, if you execute `DELETE FROM users WHERE id = 123;`, PostgreSQL will automatically find and delete all rows in `user_posts` where `user_id` was 123."
      },
      "example": {
        "query": "ALTER TABLE user_posts ADD CONSTRAINT fk_posts_user FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;",
        "explanation": "The key part of this query is `ON DELETE CASCADE`. This clause instructs PostgreSQL to automatically delete the referencing rows (the posts) when the referenced row (the user) is deleted, simplifying application logic and ensuring no orphaned data is left behind."
      },
      "notes": [
        "WARNING: `ON DELETE CASCADE` is extremely powerful and should be used with caution. An accidental deletion in the parent table can trigger a large-scale deletion of data in related tables.",
        "Alternative options include `ON DELETE SET NULL` (which would set `user_id` to NULL in the posts table) or `ON DELETE RESTRICT` (which would prevent a user from being deleted if they still have posts)."
      ]
    }
  ]
};