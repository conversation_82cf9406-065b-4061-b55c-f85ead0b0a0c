export const command31 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Stored Procedures and Functions",
    "Create Trigger"
  ],
  "description": "Creates a trigger, which is a special function that automatically executes whenever a specific event (like an INSERT, UPDATE, or DELETE) occurs on a table. This is commonly used for auditing, data validation, and maintaining data integrity.",
  "query_template": "CREATE OR REPLACE FUNCTION [function_name]()\nRETURNS TRIGGER AS $$\nBEGIN\n    -- Logic to be executed\n    NEW.[column_to_update] = NOW();\n    RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\nCREATE TRIGGER [trigger_name]\n    BEFORE UPDATE ON [table_name]\n    FOR EACH ROW\n    EXECUTE FUNCTION [function_name]();",
  "components": [
    {
      "component": "CREATE OR REPLACE FUNCTION",
      "name": "Trigger Function",
      "description": "Defines the PL/pgSQL function that contains the logic the trigger will execute. It must return the `TRIGGER` type."
    },
    {
      "component": "NEW",
      "name": "NEW Record Variable",
      "description": "A special variable available inside trigger functions that contains the new row data for INSERT/UPDATE operations."
    },
    {
      "component": "CREATE TRIGGER",
      "name": "Trigger Definition",
      "description": "Binds the trigger function to a specific table, event, and timing."
    },
    {
      "component": "BEFORE UPDATE",
      "name": "Event Timing",
      "description": "Specifies when the trigger should fire. Options include BEFORE or AFTER an INSERT, UPDATE, or DELETE operation."
    },
    {
      "component": "FOR EACH ROW",
      "name": "Trigger Granularity",
      "description": "Specifies that the trigger should execute once for every single row affected by the command. The alternative is `FOR EACH STATEMENT`."
    }
  ],
  "scenarios": [
    {
      "title": "Automatically Auditing Row Modifications",
      "details": {
        "setup": "You have a `products` table with columns like `name`, `price`, and `stock_quantity`. You also have an `updated_at` column that is intended to store the timestamp of the last modification to any given product.",
        "goal": "To ensure the `updated_at` column is automatically and accurately updated every time a product's details are changed, without relying on the application code to set the timestamp.",
        "outcome": "A trigger is created. Now, any `UPDATE` statement on a row in the `products` table will cause its `updated_at` column to be set to the current transaction's timestamp."
      },
      "example": {
        "query": "CREATE OR REPLACE FUNCTION fn_set_product_updated_at()\nRETURNS TRIGGER AS $$\nBEGIN\n    -- Set the updated_at field of the row being changed to the current time\n    NEW.updated_at = NOW();\n    RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\nCREATE TRIGGER trg_products_updated_at\n    BEFORE UPDATE ON products\n    FOR EACH ROW\n    EXECUTE FUNCTION fn_set_product_updated_at();",
        "explanation": "This solution consists of two parts. The `fn_set_product_updated_at` function defines the action: modify the `updated_at` column of the incoming row (`NEW`). The `trg_products_updated_at` trigger then attaches this function to the `products` table, instructing it to run `BEFORE` any `UPDATE` is committed for `EACH ROW`."
      },
      "notes": [
        "Using a `BEFORE` trigger is efficient because the modification happens to the row before it's written to disk.",
        "This is a very common and robust pattern for maintaining 'last modified' timestamps.",
        "To test it, run an update like: `UPDATE products SET price = 19.99 WHERE id = 1;` and then check the `updated_at` value for that product."
      ]
    }
  ]
};