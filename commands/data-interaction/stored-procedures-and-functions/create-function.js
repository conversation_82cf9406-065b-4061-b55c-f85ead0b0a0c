export const command30 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Stored Procedures and Functions",
    "Create Function"
  ],
  "description": "Creates or replaces a stored function to encapsulate reusable business logic, improving modularity and maintainability of your database operations.",
  "query_template": "CREATE OR REPLACE FUNCTION function_name(arguments)\nRETURNS return_type AS $$\nBEGIN\n    -- Your logic here\n    RETURN result;\nEND;\n$$ LANGUAGE plpgsql;",
  "components": [
    {
      "component": "CREATE OR REPLACE FUNCTION",
      "name": "Function Definition",
      "description": "Creates a new function or replaces an existing one with the same name and arguments, which is useful for development."
    },
    {
      "component": "RETURNS return_type",
      "name": "Return Type",
      "description": "Specifies the data type of the value that the function will return (e.g., NUMERIC, VARCHAR, INTEGER)."
    },
    {
      "component": "LANGUAGE plpgsql",
      "name": "Language Specifier",
      "description": "Defines the language the function is written in. `plpgsql` is PostgreSQL's native procedural language, but others like `sql`, `python` (plpython3u), or `javascript` (plv8) can also be used."
    },
    {
      "component": "$$ ... $$",
      "name": "Function Body (Dollar Quoting)",
      "description": "The block of code that contains the function's logic. Dollar quoting ($$) is a PostgreSQL feature used to write the function body without needing to escape single quotes."
    }
  ],
  "scenarios": [
    {
      "title": "Encapsulating Business Logic for Order Total Calculation",
      "details": {
        "setup": "You are building an e-commerce application. The `order_items` table contains `product_id`, `quantity`, and `unit_price`. You frequently need to calculate the total price for an order, including a 7.5% sales tax.",
        "goal": "To create a single, reliable function named `calculate_order_total` that takes an `order_id` and returns the final price, including tax. This avoids duplicating the tax calculation logic across multiple parts of the application and reports.",
        "outcome": "A function is created that can be easily called to get the final price for any order, ensuring the tax calculation is always consistent."
      },
      "example": {
        "query": "CREATE OR REPLACE FUNCTION calculate_order_total(p_order_id INTEGER)\nRETURNS NUMERIC(10, 2) AS $$\nDECLARE\n    subtotal NUMERIC := 0;\n    sales_tax NUMERIC := 0.075;\nBEGIN\n    -- Calculate the sum of all items for the given order\n    SELECT SUM(unit_price * quantity) INTO subtotal\n    FROM order_items\n    WHERE order_id = p_order_id;\n\n    -- Return the subtotal plus tax, rounded to 2 decimal places\n    RETURN ROUND(subtotal * (1 + sales_tax), 2);\nEND;\n$$ LANGUAGE plpgsql;",
        "explanation": "This function first declares variables for the subtotal and the sales tax rate. It then queries the `order_items` table to calculate the subtotal for the specified `p_order_id`. Finally, it applies the tax and returns a clean, rounded numeric value. Using a function ensures this critical business rule is defined in exactly one place."
      },
      "notes": [
        "Prefixing parameters (e.g., `p_order_id`) is a good practice to avoid name conflicts with table columns.",
        "This function can now be used in other queries, for example: `SELECT order_id, customer_id, calculate_order_total(order_id) AS total FROM orders WHERE order_date > '2025-01-01';`"
      ]
    }
  ]
};