export const command12 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Delete a Table"
  ],
  "description": "Permanently and irreversibly deletes a table, including all of its data, indexes, rules, triggers, and constraints. This action cannot be undone.",
  "query_template": "DROP TABLE [IF EXISTS] table_name [CASCADE | RESTRICT];",
  "components": [
    {
      "component": "DROP TABLE",
      "name": "Command",
      "description": "The SQL statement to delete a table."
    },
    {
      "component": "IF EXISTS",
      "name": "Conditional Clause (Optional)",
      "description": "Prevents an error from being thrown if the table does not exist. The command will simply issue a notice and complete."
    },
    {
      "component": "CASCADE",
      "name": "Dependency Option (Optional)",
      "description": "Automatically drops any objects that depend on the table (e.g., views, foreign key constraints). Use with extreme caution."
    },
    {
      "component": "RESTRICT",
      "name": "Dependency Option (Default)",
      "description": "Prevents the table from being dropped if any other objects depend on it. This is the default and safest behavior."
    }
  ],
  "scenarios": [
    {
      "title": "Removing an Obsolete Table During Development",
      "details": {
        "setup": "During a feature development, a table named `temp_feature_flags` was created for testing. The feature is now complete, and the table is no longer needed.",
        "goal": "To clean up the database schema by permanently removing the `temp_feature_flags` table.",
        "outcome": "The table `temp_feature_flags` and all its data are deleted from the database."
      },
      "example": {
        "query": "DROP TABLE temp_feature_flags;",
        "explanation": "This is the most straightforward use of the command. It targets a single table that has no other database objects depending on it."
      },
      "notes": [
        "Before running this in production, always double-check that the table is truly obsolete and that you have a recent backup."
      ]
    },
    {
      "title": "Safely Cleaning Up Tables in an Automated Script",
      "details": {
        "setup": "You are writing a deployment script that needs to clear out old staging tables before creating new ones. The script might be run on a fresh database where the table `staging_user_data` doesn't exist yet.",
        "goal": "To ensure the script runs successfully without failing, whether or not the table already exists.",
        "outcome": "If `staging_user_data` exists, it is dropped. If it does not, the script continues without error."
      },
      "example": {
        "query": "DROP TABLE IF EXISTS staging_user_data;",
        "explanation": "The `IF EXISTS` clause makes the script idempotent (runnable multiple times with the same result). It's a crucial guard clause for automated database maintenance and deployment scripts."
      }
    },
    {
      "title": "Retiring a Feature and its Dependent Objects",
      "details": {
        "setup": "An old feature with a central table named `legacy_analytics` is being removed. A view called `monthly_analytics_view` depends on this table. Attempting `DROP TABLE legacy_analytics;` fails with an error because the view depends on it.",
        "goal": "To remove the `legacy_analytics` table and automatically remove the dependent `monthly_analytics_view` at the same time.",
        "outcome": "Both the `legacy_analytics` table and the `monthly_analytics_view` are successfully dropped."
      },
      "example": {
        "query": "DROP TABLE legacy_analytics CASCADE;",
        "explanation": "`CASCADE` is a powerful but dangerous option. It traverses the dependency tree, dropping the target table and every object that references it. It should only be used when you are certain you want to remove the entire hierarchy."
      },
      "notes": [
        "WARNING: Misusing `CASCADE` can lead to accidental deletion of critical database objects. Always identify dependencies first if you are unsure."
      ]
    }
  ]
};