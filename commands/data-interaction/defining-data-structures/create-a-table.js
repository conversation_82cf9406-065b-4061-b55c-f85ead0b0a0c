export const command7 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Create a Table"
  ],
  "description": "Defines the structure of a new table, specifying its columns, data types, and constraints to ensure data integrity.",
  "query_template": "CREATE TABLE table_name (\n  column1 datatype [constraints],\n  column2 datatype [constraints],\n  ...\n);",
  "components": [
    {
      "component": "CREATE TABLE table_name (...)",
      "name": "Table Definition",
      "description": "The main clause that initiates the creation of a new table with a specified name."
    },
    {
      "component": "column_name data_type",
      "name": "Column Definition",
      "description": "Specifies the name of a column and the type of data it will store (e.g., VARCHAR, INT, DATE)."
    },
    {
      "component": "SERIAL",
      "name": "Auto-Incrementing Integer",
      "description": "A convenient shorthand for creating an integer column that automatically fills with an incrementing number for each new row. Often used for primary keys."
    },
    {
      "component": "PRIMARY KEY",
      "name": "Primary Key Constraint",
      "description": "Designates a column as the table's primary key, ensuring each row is uniquely identifiable. This constraint enforces both UNIQUE and NOT NULL."
    },
    {
      "component": "UNIQUE",
      "name": "Unique Constraint",
      "description": "Ensures that every value in this column (or group of columns) is unique across all rows in the table."
    },
    {
      "component": "DEFAULT value",
      "name": "Default Value Constraint",
      "description": "Automatically assigns a specified value to a column if no value is provided during an INSERT operation."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up an 'employees' Table for a New HR System",
      "details": {
        "setup": "Your company is building a new Human Resources application and needs a foundational database table to store employee information.",
        "goal": "To create a robust `employees` table that ensures data integrity. Each employee needs a unique ID, their name and email must be stored, duplicate emails must be prevented, and the hiring date should be recorded automatically.",
        "outcome": "The `employees` table is successfully created in the database with the specified columns and constraints, ready for the application to insert and manage employee data."
      },
      "example": {
        "query": "CREATE TABLE employees (\n  id SERIAL PRIMARY KEY,\n  first_name VARCHAR(50) NOT NULL,\n  last_name VARCHAR(50) NOT NULL,\n  email VARCHAR(100) UNIQUE,\n  hire_date DATE DEFAULT CURRENT_DATE\n);",
        "explanation": "This query creates the `employees` table. `id SERIAL PRIMARY KEY` provides a unique, auto-generating ID for each employee. `first_name` and `last_name` are set to `NOT NULL` to ensure they are always provided. `email VARCHAR(100) UNIQUE` stores the email and prevents duplicates. `hire_date DATE DEFAULT CURRENT_DATE` automatically sets the employee's hire date to the current date upon insertion."
      },
      "notes": [
        "In modern PostgreSQL (version 10+), it's often recommended to use `IDENTITY` columns instead of `SERIAL`, e.g., `id INT GENERATED ALWAYS AS IDENTITY PRIMARY KEY`.",
        "Choosing the right data types and constraints at the creation stage is crucial for preventing bad data from entering your database.",
        "The `VARCHAR(n)` length should be chosen carefully to be large enough for all expected data but not excessively large."
      ]
    }
  ]
};