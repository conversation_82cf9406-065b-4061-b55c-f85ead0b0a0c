export const command10 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Alter a Table",
    "Rename a Column"
  ],
  "description": "Changes the name of an existing column in a table. This is a common metadata operation used for database refactoring and improving schema clarity.",
  "query_template": "ALTER TABLE [table_name] RENAME COLUMN [old_column_name] TO [new_column_name];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "Specifies the table that contains the column you wish to modify."
    },
    {
      "component": "RENAME COLUMN [old_column_name] TO [new_column_name]",
      "name": "Rename Action",
      "description": "The specific action to change the name of a column from its current name to a new one."
    }
  ],
  "scenarios": [
    {
      "title": "Refactoring a Legacy Database Schema for Clarity",
      "details": {
        "setup": "You are working on a legacy application with a `users` table that has a column named `email`. The development team has decided to standardize all email-related fields to `email_address` for consistency across the entire database.",
        "goal": "To rename the `email` column in the `users` table to `email_address` without any data loss, aligning it with the new schema standards.",
        "outcome": "The column is successfully renamed to `email_address`. All existing data remains intact, and any queries referencing the new column name now work correctly."
      },
      "example": {
        "query": "ALTER TABLE users RENAME COLUMN email TO email_address;",
        "explanation": "This command performs a direct, metadata-only change on the `users` table. It renames the column `email` to `email_address`. The operation is atomic and very fast because it doesn't require rewriting table data."
      },
      "notes": [
        "**Crucial Warning:** Renaming a column will break all application code, views, functions, triggers, and foreign key constraints that reference the old column name. Plan to update all dependencies immediately after running this command.",
        "PostgreSQL automatically updates indexes and constraints that are defined on the column.",
        "This operation requires an `ACCESS EXCLUSIVE` lock on the table, which will block all other read/write operations until the rename is complete. While fast, it should be done during a maintenance window on a high-traffic table."
      ]
    }
  ]
};