export const command9 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Alter a Table",
    "Remove a Column"
  ],
  "description": "Permanently removes a column, and all its data, from an existing table. This is an irreversible action.",
  "query_template": "ALTER TABLE [table_name] DROP COLUMN [column_name];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "Specifies the table you intend to modify."
    },
    {
      "component": "DROP COLUMN [column_name]",
      "name": "Action Clause",
      "description": "Specifies the action to perform: permanently delete the named column."
    }
  ],
  "scenarios": [
    {
      "title": "Refactoring a Legacy Table by Removing an Obsolete Column",
      "details": {
        "setup": "An application has been updated, and the `employees` table contains a `phone_number` column that is no longer used. The data is outdated and the column is just taking up space, complicating data models.",
        "goal": "To permanently remove the unused `phone_number` column from the `employees` table to simplify the schema and reclaim storage.",
        "outcome": "The `employees` table schema is updated, the `phone_number` column is gone, and the storage it occupied is marked for reuse by the database."
      },
      "example": {
        "query": "ALTER TABLE employees DROP COLUMN phone_number;",
        "explanation": "This command directly modifies the `employees` table structure by executing the `DROP COLUMN` action. It is the most direct way to remove a column that has no dependencies."
      },
      "notes": [
        "Warning: This action is irreversible. All data within the `phone_number` column will be permanently lost. Always perform a backup before running this in production.",
        "This command requires an `ACCESS EXCLUSIVE` lock on the table, which blocks all reads and writes. On large, high-traffic tables, this can cause significant downtime. Plan to run it during a maintenance window."
      ]
    },
    {
      "title": "Removing a Column That Is Used by a View",
      "details": {
        "setup": "You have a `products` table with a `unit_cost` column. There is also a view named `product_profit_view` that uses `unit_cost` in its calculations. Attempting to drop the column with a simple `DROP COLUMN` command fails with an error because the view depends on it.",
        "goal": "To remove the `unit_cost` column and simultaneously remove any database objects that depend on it.",
        "outcome": "Both the `unit_cost` column from the `products` table and the `product_profit_view` are successfully dropped in a single operation."
      },
      "example": {
        "query": "ALTER TABLE products DROP COLUMN unit_cost CASCADE;",
        "explanation": "The `CASCADE` keyword is appended to the command to instruct PostgreSQL to automatically drop any dependent objects. In this case, it removes the view that was preventing the column from being dropped."
      },
      "notes": [
        "Use `CASCADE` with extreme caution. It can have far-reaching effects. Before using it, it's wise to manually identify all dependent objects to avoid accidental data or object loss.",
        "The default behavior is `RESTRICT`, which prevents you from dropping a column if other objects depend on it. This is a safety feature."
      ]
    }
  ]
};