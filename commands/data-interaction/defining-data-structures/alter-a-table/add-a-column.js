export const command8 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Alter a Table",
    "Add a Column"
  ],
  "description": "Adds a new column to an existing table, allowing you to store additional data for each record. You can specify the column name, data type, and optional constraints.",
  "query_template": "ALTER TABLE [table_name] ADD COLUMN [column_name] [data_type] [constraints];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Alter Table Clause",
      "description": "Specifies the existing table you want to modify."
    },
    {
      "component": "ADD COLUMN [column_name]",
      "name": "Add Column Clause",
      "description": "Specifies the name for the new column to be added."
    },
    {
      "component": "[data_type]",
      "name": "Data Type",
      "description": "Defines the type of data the column will store (e.g., VARCHAR(50), INT, BOOLEAN, TIMESTAMP)."
    },
    {
      "component": "[constraints]",
      "name": "Column Constraints (Optional)",
      "description": "Defines rules for the column's data, such as `NOT NULL` or `DEFAULT 'some_value'`."
    }
  ],
  "scenarios": [
    {
      "title": "Adding a User's Phone Number to a Profile Table",
      "details": {
        "setup": "Your application has a `users` table with `id`, `username`, and `email`. As part of a new feature, you now need to collect user phone numbers for two-factor authentication.",
        "goal": "To add a new `phone_number` column to the `users` table to store this information. The column should be able to hold string values.",
        "outcome": "The `users` table schema is successfully updated with the new `phone_number` column. All existing user rows will have a `NULL` value in this new column, ready to be populated."
      },
      "example": {
        "query": "ALTER TABLE users ADD COLUMN phone_number VARCHAR(25);",
        "explanation": "This query modifies the `users` table by adding a column named `phone_number` with a data type of `VARCHAR(25)`. This type is suitable for storing international phone numbers and special characters."
      },
      "notes": [
        "For very large tables, adding a column without a `DEFAULT` is a fast, metadata-only change in PostgreSQL.",
        "After running this, remember to update your application's data models and UI to include the new phone number field."
      ]
    },
    {
      "title": "Adding an 'Is Active' Flag with a Default Value",
      "details": {
        "setup": "You have a `products` table and you want to be able to 'soft delete' or deactivate products without actually removing them from the database. All existing products should be considered active by default.",
        "goal": "To add an `is_active` column to the `products` table. This column should never be empty (`NOT NULL`) and should default to `true` for all existing and new records.",
        "outcome": "The `products` table is updated with an `is_active` boolean column. All existing product records are automatically set to `is_active = true`, and any new product inserted without specifying the flag will also default to `true`."
      },
      "example": {
        "query": "ALTER TABLE products ADD COLUMN is_active BOOLEAN NOT NULL DEFAULT TRUE;",
        "explanation": "This query adds the `is_active` column. The `BOOLEAN` type is perfect for true/false flags. The `NOT NULL` constraint ensures data integrity, and `DEFAULT TRUE` automatically populates the column for all existing and future rows, simplifying data management."
      },
      "notes": [
        "In modern PostgreSQL versions (11+), adding a column with a `DEFAULT` is very fast and does not require a full table rewrite, making it safe for large production tables.",
        "This is a common and highly effective pattern for managing the state of records in a database."
      ]
    }
  ]
};