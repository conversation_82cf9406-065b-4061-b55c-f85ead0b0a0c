export const command11 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Defining Data Structures",
    "Alter a Table",
    "Change Column Data Type"
  ],
  "description": "Modifies the data type of an existing column in a table. This is a common schema migration task but requires caution in production environments as it can lock the table.",
  "query_template": "ALTER TABLE [table_name] ALTER COLUMN [column_name] TYPE [new_data_type];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "Specifies the table you intend to modify."
    },
    {
      "component": "ALTER COLUMN [column_name]",
      "name": "Target Column",
      "description": "Specifies the exact column within the table whose data type you want to change."
    },
    {
      "component": "TYPE [new_data_type]",
      "name": "New Data Type",
      "description": "Defines the new data type for the column (e.g., VARCHAR(100), INTEGER, TIMESTAMPTZ)."
    }
  ],
  "scenarios": [
    {
      "title": "Schema Migration: Accommodating <PERSON><PERSON> <PERSON>ail Addresses",
      "details": {
        "setup": "Your `users` table was initially created with an `email` column of type `VARCHAR(50)`. Your company has started acquiring users with longer, more complex email addresses that are being truncated or rejected by the database, causing login failures.",
        "goal": "To increase the character limit of the `email` column to `150` to support all valid email formats without losing any existing user data.",
        "outcome": "The `email` column's data type is successfully changed to `VARCHAR(150)`, allowing the application to store longer email addresses and resolving the user login issues."
      },
      "example": {
        "query": "ALTER TABLE users ALTER COLUMN email TYPE VARCHAR(150);",
        "explanation": "This query modifies the `users` table schema directly. It tells PostgreSQL to change the definition of the `email` column to a variable-length string that can hold up to 150 characters."
      },
      "notes": [
        "Warning: This command acquires an `ACCESS EXCLUSIVE` lock on the table, which blocks all reads and writes until the operation completes. On large tables, this can cause significant downtime.",
        "Always perform this operation during a scheduled maintenance window on production systems.",
        "If changing to an incompatible data type (e.g., TEXT to INTEGER), the command will fail if any existing data cannot be implicitly cast. You may need to use the `USING` clause (e.g., `...TYPE INTEGER USING email::INTEGER`) for explicit conversion."
      ]
    }
  ]
};