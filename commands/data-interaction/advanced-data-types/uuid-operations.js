export const command33 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Advanced Data Types",
    "UUID Operations",
    "Create Table with UUID Primary Key"
  ],
  "description": "Defines a table using the UUID (Universally Unique Identifier) data type for its primary key. UUIDs are excellent for generating globally unique identifiers without requiring a centralized sequence, making them ideal for distributed systems.",
  "query_template": "CREATE TABLE [table_name] (id UUID PRIMARY KEY DEFAULT [uuid_function](), ...);",
  "components": [
    {
      "component": "UUID",
      "name": "UUID Data Type",
      "description": "A 16-byte data type designed to store globally unique identifiers."
    },
    {
      "component": "DEFAULT gen_random_uuid()",
      "name": "Default UUID Generator (Modern)",
      "description": "The built-in function (PostgreSQL 13+) to generate a version 4 random UUID. This is the recommended modern approach."
    },
    {
      "component": "DEFAULT uuid_generate_v4()",
      "name": "Default UUID Generator (Legacy)",
      "description": "The classic function for generating a version 4 UUID. Requires activating the 'uuid-ossp' extension first."
    }
  ],
  "scenarios": [
    {
      "title": "Primary Keys for a Distributed Microservices Application",
      "details": {
        "setup": "You are designing a system with multiple services (e.g., an 'orders' service and a 'shipping' service) that run independently. Both services need to create records in a central database, and you must prevent primary key collisions.",
        "goal": "To define a `products` table where the primary key can be generated by any service instance anywhere in the world without conflict.",
        "outcome": "A `products` table is created where the `id` column is a UUID, automatically assigned a unique value upon insertion."
      },
      "example": {
        "query": "CREATE TABLE products (\n  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),\n  name TEXT NOT NULL,\n  sku VARCHAR(50) UNIQUE\n);",
        "explanation": "Using `UUID` as the `PRIMARY KEY` with `gen_random_uuid()` as the default ensures every new product gets a globally unique ID at the moment of creation. This allows different microservices to add products concurrently without any risk of ID collision."
      },
      "notes": [
        "This approach is standard for PostgreSQL v13 and newer.",
        "UUIDs are larger than standard integers, which can slightly increase storage and index size. This is a common trade-off for the benefit of distributed uniqueness."
      ]
    },
    {
      "title": "Implementing UUIDs in a Legacy System (PostgreSQL 12 and Older)",
      "details": {
        "setup": "You are working on a PostgreSQL 11 database and need to create a table for user sessions. The built-in `gen_random_uuid()` function is not available.",
        "goal": "To enable UUID generation by installing the necessary extension and then create a `sessions` table that uses UUIDs for the session ID.",
        "outcome": "The `uuid-ossp` extension is activated for the database, and the `sessions` table is created successfully, using `uuid_generate_v4()` to create unique session IDs."
      },
      "example": {
        "query": "-- Step 1: Enable the extension (run once per database as a superuser)\nCREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";\n\n-- Step 2: Create the table using the extension's function\nCREATE TABLE sessions (\n  session_id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),\n  user_id INT NOT NULL REFERENCES users(id),\n  created_at TIMESTAMPTZ DEFAULT now(),\n  expires_at TIMESTAMPTZ\n);",
        "explanation": "First, the `CREATE EXTENSION` command loads the `uuid-ossp` module. The `CREATE TABLE` statement then uses `uuid_generate_v4()` from that module as the default value generator for the primary key. This achieves the same goal as the modern approach."
      },
      "notes": [
        "The `uuid-ossp` extension is included with PostgreSQL but must be explicitly enabled.",
        "If you later upgrade your PostgreSQL server, you can consider migrating the default function to `gen_random_uuid()`."
      ]
    }
  ]
};