export const command32 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Advanced Data Types",
    "Array Operations",
    "Find Rows Where an Array Contains Elements"
  ],
  "description": "Finds rows where an array column contains all elements from another specified array. This is extremely useful for implementing features like tag-based filtering or skill-matching.",
  "query_template": "SELECT [columns] FROM [table] WHERE [array_column] @> ARRAY['value1', 'value2'];",
  "components": [
    {
      "component": "@>",
      "name": "Contains Operator",
      "description": "A boolean operator that returns true if the array on the left contains every element of the array on the right. The order of elements and duplicates do not matter."
    },
    {
      "component": "ARRAY['...']",
      "name": "Array Constructor",
      "description": "The standard SQL syntax for defining an array of values. The elements must be of the same data type."
    }
  ],
  "scenarios": [
    {
      "title": "Filtering Candidates Based on a Required Skill Set",
      "details": {
        "setup": "You have a `candidates` table with a `skills` column of type `text[]`, which stores a list of each candidate's technical skills. The table contains thousands of entries.",
        "goal": "You need to find all candidates who possess a specific set of core skills—'PostgreSQL' and 'Python'—for a new data engineering role.",
        "outcome": "A list of candidate names is returned, showing only those who have both 'PostgreSQL' and 'Python' listed in their skills array."
      },
      "example": {
        "query": "SELECT candidate_name, skills FROM candidates WHERE skills @> ARRAY['PostgreSQL', 'Python'];",
        "explanation": "This query uses the `@>` operator to check if each candidate's `skills` array contains all the elements specified in the `ARRAY['PostgreSQL', 'Python']`. It efficiently filters the table to find an exact match for the required skill set."
      },
      "notes": [
        "For optimal performance on large tables, you should create a GIN (Generalized Inverted Index) on the array column: `CREATE INDEX idx_candidates_skills ON candidates USING GIN (skills);`",
        "To find candidates with *at least one* of the skills (not all), use the 'overlap' operator (`&&`) instead: `WHERE skills && ARRAY['PostgreSQL', 'Python']`.",
        "The `@>` operator is a key feature for working with denormalized data structures and is highly efficient when indexed properly."
      ]
    }
  ]
};