import { command0 } from './inspecting-your-database/list-tables.js';
import { command1 } from './inspecting-your-database/describe-a-table.js';
import { command2 } from './inspecting-your-database/list-schemas.js';
import { command3 } from './inspecting-your-database/list-views.js';
import { command4 } from './inspecting-your-database/list-functions.js';
import { command5 } from './inspecting-your-database/list-users-and-roles.js';
import { command6 } from './inspecting-your-database/get-help-on-psql-commands.js';
import { command7 } from './defining-data-structures/create-a-table.js';
import { command8 } from './defining-data-structures/alter-a-table/add-a-column.js';
import { command9 } from './defining-data-structures/alter-a-table/remove-a-column.js';
import { command10 } from './defining-data-structures/alter-a-table/rename-a-column.js';
import { command11 } from './defining-data-structures/alter-a-table/change-column-data-type.js';
import { command12 } from './defining-data-structures/delete-a-table.js';
import { command13 } from './manipulating-data/insert-data.js';
import { command14 } from './manipulating-data/query-data/select-all-columns.js';
import { command15 } from './manipulating-data/query-data/select-specific-columns.js';
import { command16 } from './manipulating-data/query-data/select-with-a-filter.js';
import { command17 } from './manipulating-data/update-data.js';
import { command18 } from './manipulating-data/delete-data.js';
import { command19 } from './exit-psql.js';
import { command20 } from './index-management/create-index.js';
import { command21 } from './index-management/list-indexes.js';
import { command22 } from './index-management/drop-index.js';
import { command23 } from './index-management/reindex.js';
import { command24 } from './constraint-management/add-primary-key.js';
import { command25 } from './constraint-management/add-foreign-key.js';
import { command26 } from './constraint-management/add-check-constraint.js';
import { command27 } from './advanced-sql/common-table-expressions-cte.js';
import { command28 } from './advanced-sql/window-functions.js';
import { command29 } from './advanced-sql/json-operations.js';
import { command30 } from './stored-procedures-and-functions/create-function.js';
import { command31 } from './stored-procedures-and-functions/create-trigger.js';
import { command32 } from './advanced-data-types/array-operations.js';
import { command33 } from './advanced-data-types/uuid-operations.js';

export const dataInteractionCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15,
  command16,
  command17,
  command18,
  command19,
  command20,
  command21,
  command22,
  command23,
  command24,
  command25,
  command26,
  command27,
  command28,
  command29,
  command30,
  command31,
  command32,
  command33
];

export function validateDataInteractionCommands() {
  return dataInteractionCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Interacting with Data";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
