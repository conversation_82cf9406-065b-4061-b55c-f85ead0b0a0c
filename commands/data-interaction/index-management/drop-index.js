export const command22 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Index Management",
    "Drop Index"
  ],
  "description": "Removes an existing index from a table. This is done to remove unused indexes, change indexing strategy, or before a large data load. The CONCURRENTLY option is critical for production environments to avoid blocking writes.",
  "query_template": "DROP INDEX [CONCURRENTLY] [IF EXISTS] [index_name] [CASCADE | RESTRICT];",
  "components": [
    {
      "component": "DROP INDEX",
      "name": "Drop Index Command",
      "description": "The fundamental SQL statement used to remove an index."
    },
    {
      "component": "CONCURRENTLY",
      "name": "Concurrent Option",
      "description": "A crucial option that drops the index without locking the table from write operations (INSERT, UPDATE, DELETE). It is highly recommended for production systems."
    },
    {
      "component": "IF EXISTS",
      "name": "If Exists Clause",
      "description": "Prevents the command from failing with an error if the index doesn't already exist. This is very useful in automated scripts."
    },
    {
      "component": "[index_name]",
      "name": "Index Name",
      "description": "The name of the index you want to remove."
    },
    {
      "component": "CASCADE",
      "name": "Cascade Option",
      "description": "Automatically drops any objects that depend on the index. This should be used with extreme caution."
    }
  ],
  "scenarios": [
    {
      "title": "Safely Removing an Unused Index from a Live Production Table",
      "details": {
        "setup": "Your application has a busy `users` table with an index named `idx_users_last_login`. After monitoring database usage, you've confirmed this index is no longer used by any queries and is only adding overhead to write operations.",
        "goal": "To remove the unused index to improve write performance, without locking the `users` table or causing application downtime.",
        "outcome": "The index is successfully removed. Users continue to use the application without interruption, and the performance of `INSERT` and `UPDATE` statements on the `users` table is marginally improved."
      },
      "example": {
        "query": "DROP INDEX CONCURRENTLY idx_users_last_login;",
        "explanation": "The `CONCURRENTLY` option is essential for this scenario. It ensures that writers are not blocked while the index is being removed, making it the standard and safe method for dropping indexes on active, production tables."
      },
      "notes": [
        "`DROP INDEX CONCURRENTLY` cannot be run inside a transaction block (`BEGIN`/`COMMIT`).",
        "This operation may take longer to complete than a standard `DROP INDEX` as it involves more steps to ensure concurrent access."
      ]
    },
    {
      "title": "Creating a Repeatable Database Migration Script",
      "details": {
        "setup": "You are writing an automated deployment script that refactors an existing feature. The script needs to remove an old index, `idx_employee_email`, before creating a new, more complex one. This script will run on development, staging, and production servers, and the old index may have already been removed on some.",
        "goal": "To ensure the deployment script runs reliably and doesn't fail with an 'index does not exist' error, which would halt the entire deployment process.",
        "outcome": "The script executes successfully on all environments. If `idx_employee_email` exists, it is dropped. If it does not, the script simply continues to the next step without error."
      },
      "example": {
        "query": "DROP INDEX IF EXISTS idx_employee_email;",
        "explanation": "Using `IF EXISTS` makes the script idempotent, meaning it can be run multiple times without changing the outcome or causing errors after the first run. This is a best practice for writing robust and repeatable database migration scripts."
      },
      "notes": [
        "For a production deployment script, you would likely combine both options: `DROP INDEX CONCURRENTLY IF EXISTS idx_employee_email;`"
      ]
    }
  ]
};