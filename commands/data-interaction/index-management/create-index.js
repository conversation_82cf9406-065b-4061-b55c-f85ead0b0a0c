export const command20 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Index Management",
    "Create Index"
  ],
  "description": "Creates an index on one or more columns of a table to dramatically improve the speed of data retrieval operations. Indexes are essential for optimizing query performance.",
  "query_template": "CREATE [UNIQUE] INDEX [CONCURRENTLY] [index_name] ON [table_name] USING [method] ([column1], [column2], ...);",
  "components": [
    {
      "component": "CREATE INDEX [index_name]",
      "name": "Index Definition",
      "description": "Command to create a new index with a specified name."
    },
    {
      "component": "ON [table_name]",
      "name": "Target Table",
      "description": "Specifies the table on which the index will be created."
    },
    {
      "component": "([column1], ...)",
      "name": "Indexed Column(s)",
      "description": "The column or columns that the index will cover."
    },
    {
      "component": "UNIQUE",
      "name": "Unique Constraint",
      "description": "Optional keyword that ensures no two rows can have the same value in the indexed column(s)."
    },
    {
      "component": "CONCURRENTLY",
      "name": "Concurrent Build",
      "description": "Optional keyword that creates the index without locking the table against writes, preventing downtime on production systems."
    },
    {
      "component": "USING [method]",
      "name": "Index Method",
      "description": "Optional clause to specify an index type, such as `btree` (default), `hash`, `gist`, or `gin`."
    }
  ],
  "scenarios": [
    {
      "title": "Accelerating User Lookups in an Application",
      "details": {
        "setup": "An `accounts` table has grown to millions of rows. Queries that look up users by their email address, such as `SELECT * FROM accounts WHERE email = '...';`, are becoming slow and impacting application performance.",
        "goal": "To significantly speed up queries that filter users by their email address.",
        "outcome": "Queries filtering by email are now almost instantaneous, as the database can use the index to find the matching row directly instead of scanning the entire table."
      },
      "example": {
        "query": "CREATE INDEX idx_accounts_email ON accounts (email);",
        "explanation": "This command creates a standard B-tree index on the `email` column. B-tree indexes are highly efficient for equality (`=`) and range (`<`, `>`) lookups, making them perfect for `WHERE` clauses like this."
      },
      "notes": [
        "PostgreSQL uses the B-tree index type by default, so `USING btree` is optional here.",
        "Use `EXPLAIN ANALYZE` on your `SELECT` query before and after creating the index to see the performance improvement from 'Seq Scan' to 'Index Scan'."
      ]
    },
    {
      "title": "Adding an Index to a Live Production Table Without Downtime",
      "details": {
        "setup": "A large `orders` table is actively receiving writes 24/7. You need to add an index on the `product_id` to improve a reporting dashboard, but you cannot afford to lock the table and block incoming orders.",
        "goal": "To create the index without any write-locking on the `orders` table, ensuring zero interruption to the application.",
        "outcome": "The index is successfully built in the background. The `orders` table remains fully available for reads and writes throughout the entire process."
      },
      "example": {
        "query": "CREATE INDEX CONCURRENTLY idx_orders_product_id ON orders (product_id);",
        "explanation": "The `CONCURRENTLY` option instructs PostgreSQL to build the index without taking a lock that would block `INSERT`, `UPDATE`, or `DELETE` operations. This process is slower and uses more CPU/IO, but it is essential for making changes to high-traffic production tables."
      },
      "notes": [
        "A `CREATE INDEX CONCURRENTLY` command cannot be run inside a transaction block (`BEGIN`/`COMMIT`).",
        "If this operation fails, it may leave behind an 'invalid' index that needs to be dropped manually before you can try again."
      ]
    },
    {
      "title": "Enforcing a 'No Duplicate Usernames' Business Rule",
      "details": {
        "setup": "An application's `users` table has a `username` column. The business logic dictates that every username must be unique, but this is only enforced at the application level, which can lead to race conditions and duplicate entries.",
        "goal": "To enforce username uniqueness at the database level, guaranteeing data integrity.",
        "outcome": "A unique index is created. Any `INSERT` or `UPDATE` statement that attempts to create a duplicate username will now fail with a `duplicate key value violates unique constraint` error."
      },
      "example": {
        "query": "CREATE UNIQUE INDEX idx_users_username_unique ON users (LOWER(username));",
        "explanation": "The `UNIQUE` keyword creates an index that also functions as a data integrity constraint. By indexing `LOWER(username)`, we ensure the uniqueness check is case-insensitive (e.g., 'John' and 'john' are treated as the same)."
      },
      "notes": [
        "A primary key on a table automatically creates a unique index.",
        "Using function-based indexes like `LOWER(username)` is a powerful way to enforce more complex business rules."
      ]
    }
  ]
};