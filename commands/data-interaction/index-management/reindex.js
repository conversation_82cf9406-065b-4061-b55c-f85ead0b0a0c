export const command23 = {
  "type": "query",
  "path": [
    "Interacting with Data",
    "Index Management",
    "Reindex"
  ],
  "description": "Rebuilds an index to restore its performance by removing bloat or to repair potential corruption. This is often necessary for indexes on tables with heavy, long-running write activity (UPDATEs and DELETEs).",
  "query_template": "REINDEX [OPTION] [TARGET] [NAME];",
  "components": [
    {
      "component": "REINDEX INDEX index_name",
      "name": "Rebuild a Specific Index",
      "description": "Rebuilds a single named index. This is the most common use case."
    },
    {
      "component": "REINDEX TABLE table_name",
      "name": "Rebuild Indexes on a Table",
      "description": "Rebuilds all indexes for a specific table."
    },
    {
      "component": "REINDEX DATABASE database_name",
      "name": "Rebuild Indexes in a Database",
      "description": "Rebuilds all indexes within a specified database. This is a powerful but potentially very long-running operation."
    },
    {
      "component": "OPTION: CONCURRENTLY",
      "name": "Concurrent Reindex",
      "description": "Rebuilds the index without taking a lock that blocks writes to the table. This is the preferred method for production environments but requires more resources. (Available since PostgreSQL 12)."
    },
    {
      "component": "OPTION: VERBOSE",
      "name": "Verbose Output",
      "description": "Prints progress reports during the reindexing process."
    }
  ],
  "scenarios": [
    {
      "title": "Zero-Downtime Index Optimization on a Production Table",
      "details": {
        "setup": "Your application's `users` table has an index `idx_users_last_login`. After millions of user updates, you notice that queries filtering by `last_login` are becoming slower due to index bloat.",
        "goal": "To rebuild the bloated index to restore its performance without locking the `users` table, which would cause application errors and downtime.",
        "outcome": "A new, optimized version of the index is built in the background. Once complete, it transparently replaces the old index, and query performance is restored with zero impact on application availability."
      },
      "example": {
        "query": "REINDEX INDEX CONCURRENTLY idx_users_last_login;",
        "explanation": "This command specifically targets the `idx_users_last_login` index. The crucial `CONCURRENTLY` keyword ensures that `UPDATE` and `INSERT` operations on the `users` table can continue uninterrupted while the index is being rebuilt."
      },
      "notes": [
        "A concurrent reindex takes longer and consumes more CPU/IO resources than a standard reindex.",
        "If the `REINDEX CONCURRENTLY` command fails, it may leave behind an invalid index that you will need to manually drop.",
        "`REINDEX CONCURRENTLY` is available in PostgreSQL 12 and newer. For older versions, the alternative is to use `CREATE INDEX CONCURRENTLY` and then drop the old index."
      ]
    },
    {
      "title": "Post-Maintenance Cleanup for a Heavily Modified Table",
      "details": {
        "setup": "During a scheduled maintenance window, you have just performed a bulk update on millions of rows in your `inventory` table. You now want to ensure all of its indexes are optimized before returning the application to service.",
        "goal": "To quickly rebuild all indexes on the `inventory` table at once while the application is offline for maintenance.",
        "outcome": "All indexes on the `inventory` table are rebuilt to a compact, performant state, ready for production traffic."
      },
      "example": {
        "query": "REINDEX TABLE VERBOSE inventory;",
        "explanation": "Using `REINDEX TABLE` is more efficient than running separate commands for each index on the `inventory` table. The `VERBOSE` option provides valuable feedback on the progress of the operation, which is useful for long-running maintenance tasks."
      },
      "notes": [
        "This command will acquire an `ACCESS EXCLUSIVE` lock on the `inventory` table, blocking all reads and writes until it completes. It must only be used during a planned outage.",
        "This is often faster and simpler than a series of `CONCURRENTLY` operations if you can afford the downtime."
      ]
    }
  ]
};