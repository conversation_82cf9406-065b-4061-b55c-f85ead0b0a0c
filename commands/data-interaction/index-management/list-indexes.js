export const command21 = {
  "path": [
    "Interacting with Data",
    "Index Management",
    "List Indexes"
  ],
  "command": "\\di[+] [pattern]",
  "description": "Lists all indexes in the current database. This command can also be filtered to show indexes for a specific table or that match a pattern.",
  "parameters": [
    {
      "flag": "\\di",
      "name": "List Indexes",
      "description": "Displays a basic list of all indexes, including the schema, name, type, and associated table."
    },
    {
      "flag": "\\di+",
      "name": "Detailed List",
      "description": "Provides an extended view with additional details, such as the index size on disk and its description."
    },
    {
      "flag": "\\di <table_name>",
      "name": "Table Indexes",
      "description": "Lists only the indexes associated with a specific table."
    },
    {
      "flag": "\\di <pattern>",
      "name": "Pattern Match",
      "description": "Lists indexes whose names match a specific pattern (e.g., 'idx_*_user')."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Indexes on a Table for Query Optimization",
      "details": {
        "setup": "You are a database developer tasked with optimizing a slow query that filters records from the 'customers' table based on the 'email' column.",
        "goal": "To quickly check if an index already exists on the 'email' column of the 'customers' table before deciding whether to create a new one.",
        "outcome": "You get a concise list of all indexes for the 'customers' table, allowing you to confirm if the required index is present."
      },
      "example": {
        "command": "\\di customers",
        "explanation": "By specifying the table name 'customers', this command filters the output to show only its indexes. This is a fast and direct way to verify the indexing strategy for a particular table without needing to search through a list of all database indexes."
      },
      "notes": [
        "If you find no suitable index, your next step would likely be `CREATE INDEX ON customers (email);`.",
        "Use `EXPLAIN ANALYZE` on your slow query to see if PostgreSQL is actually using the index you've identified."
      ]
    },
    {
      "title": "Performing a Database-Wide Index Audit",
      "details": {
        "setup": "As a database administrator, you are performing a routine health check on a production database. Part of this process is to review the size and number of indexes to identify potential bloat or unused indexes.",
        "goal": "To get a comprehensive overview of every index in the database, including its size on disk, to identify the largest indexes that may warrant further investigation.",
        "outcome": "A detailed list of all indexes is displayed, sorted by name, with a 'Size' column that helps you immediately spot the most significant ones."
      },
      "example": {
        "command": "\\di+",
        "explanation": "The `+` modifier is crucial here as it adds the 'Size' column to the output. This allows you to identify which indexes are consuming the most disk space, which is a key factor in maintenance and performance tuning."
      },
      "notes": [
        "An unusually large index relative to its table size could be a sign of index bloat and might benefit from being rebuilt with `REINDEX`.",
        "This command, combined with usage statistics from `pg_stat_all_indexes`, can help you find indexes that are rarely used and could potentially be dropped."
      ]
    }
  ]
}