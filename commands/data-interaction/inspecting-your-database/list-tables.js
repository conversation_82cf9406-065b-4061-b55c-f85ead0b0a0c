export const command0 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "List Tables"
  ],
  "command": "\\dt",
  "description": "Lists all tables in the current schema of the database. For a more detailed view including table size, owner, and description, use the '+' modifier (\\dt+).",
  "scenarios": [
    {
      "title": "Initial Exploration of a New Database",
      "details": {
        "setup": "As a developer, you have just connected to a project's database for the first time. You have no prior knowledge of its structure.",
        "goal": "To get a quick overview of the primary data entities by listing all the tables available in the default 'public' schema.",
        "outcome": "A clean list of table names is displayed, providing a clear map of the database's structure to begin your work."
      },
      "example": {
        "command": "\\dt",
        "explanation": "This command instantly queries the system catalogs to show all tables in the current schema. It's the most common first step when familiarizing yourself with a database."
      },
      "notes": [
        "This command will only show tables in the current schema. To list tables from a different schema, you can specify it like so: `\\dt analytics.*`"
      ]
    },
    {
      "title": "Quickly Assessing Table Sizes",
      "details": {
        "setup": "You are a database administrator performing a routine health check. You've noticed that disk space usage is increasing and you want to identify which tables are the largest.",
        "goal": "To list all tables along with their on-disk size to quickly spot any tables that are consuming an excessive amount of space.",
        "outcome": "A detailed list appears, showing table names, owners, and sizes. You immediately see that the 'audit_log' table is 25 GB, identifying it as the primary consumer of disk space."
      },
      "example": {
        "command": "\\dt+",
        "explanation": "The `+` modifier adds several columns to the output, including a human-readable 'Size'. This makes it an invaluable diagnostic tool for assessing your database without writing complex queries against system tables."
      },
      "notes": [
        "The size shown by `\\dt+` represents the main table data. It does not include the size of any associated indexes. Use `\\dti+` to see index sizes."
      ]
    }
  ]
};