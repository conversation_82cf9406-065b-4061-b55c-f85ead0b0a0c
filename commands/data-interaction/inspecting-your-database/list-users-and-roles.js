export const command5 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "List Users and Roles"
  ],
  "command": "\\du",
  "description": "Displays a list of all roles (users and groups), along with their assigned attributes (e.g., Superuser, Create DB) and memberships. This is a psql meta-command.",
  "scenarios": [
    {
      "title": "Auditing User Roles and Privileges",
      "details": {
        "setup": "As a database administrator, you are performing a routine security check to ensure that roles have appropriate permissions and to identify any accounts with excessive privileges.",
        "goal": "To get a quick and comprehensive overview of all existing roles, their specific attributes (like superuser status or ability to create databases), and their group memberships.",
        "outcome": "The command outputs a clear, tabular list of all roles, allowing you to instantly review their permissions and verify compliance with your security policies."
      },
      "example": {
        "command": "\\du",
        "explanation": "Executing this command within a `psql` session connects to the server's system catalogs and formats the information about roles into a human-readable table. This is much faster than manually querying the `pg_roles` catalog."
      },
      "notes": [
        "Pay close attention to the 'Attributes' column to spot roles with 'Superuser' privileges.",
        "For even more detail, including the description for each role, use `\\du+`.",
        "This command must be run from within the PostgreSQL interactive terminal (psql)."
      ]
    }
  ]
}