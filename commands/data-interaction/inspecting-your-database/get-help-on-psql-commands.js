export const command6 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "Get Help on psql Commands"
  ],
  "command": "\\?",
  "description": "Shows a comprehensive list of all psql meta-commands and their functions. A vital in-terminal reference.",
  "parameters": [], // This command has no parameters
  "scenarios": [
    {
      "title": "Finding the Right `psql` Command Without Leaving the Terminal",
      "details": {
        "setup": "You are connected to a PostgreSQL database using the `psql` interactive terminal. You need to inspect the structure of a table named 'users' but have forgotten the specific meta-command to do so.",
        "goal": "To quickly look up the list of available `psql` meta-commands to find the correct one for describing database objects, without interrupting your workflow by switching to a web browser.",
        "outcome": "A complete list of all `\\` commands is displayed in your terminal. By scanning the list, you identify `\\d [NAME]` as the command to 'describe table, view, sequence, or index'."
      },
      "example": {
        "command": "\\?",
        "explanation": "Executing `\\?` provides a comprehensive list of all meta-commands and their purposes. It serves as an essential, built-in reference, allowing you to find the command you need (`\\d users`) efficiently within the `psql` environment."
      },
      "notes": [
        "This command is for `psql` meta-commands only (those starting with `\\`).",
        "For help on a specific SQL command (e.g., `ALTER TABLE`), use the `\\h` command, like `\\h ALTER TABLE`."
      ]
    }
  ]
};