export const command3 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "List Views"
  ],
  "command": "\\dv",
  "description": "List all views in the current database, showing their schema, name, and owner.",
  "scenarios": [
    {
      "title": "Auditing Available Data Views for Reporting",
      "details": {
        "setup": "You are a data analyst who has just joined a project with a mature PostgreSQL database. You need to create a new financial report and suspect that a pre-existing view might already aggregate the necessary sales data.",
        "goal": "To quickly list all available views in the 'analytics_db' database to find a suitable one for your report without querying system catalogs manually.",
        "outcome": "A list of all views is displayed, and you identify `public.v_quarterly_sales_report` as the perfect starting point for your task."
      },
      "example": {
        "command": "\\dv",
        "explanation": "Executing `\\dv` within a `psql` session connected to your database provides a direct and immediate list of all views. This is much faster and more convenient than writing a `SELECT` query against the `information_schema.views` or `pg_catalog.pg_views` tables."
      },
      "notes": [
        "To see views that match a specific pattern, you can add it after the command, e.g., `\\dv sales_*`.",
        "To get more detailed information about a specific view, including its underlying SQL definition, use `\\d+ <view_name>`."
      ]
    }
  ]
};