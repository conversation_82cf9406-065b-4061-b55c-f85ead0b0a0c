export const command2 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "List Schemas"
  ],
  "command": "\\dn",
  "description": "List all schemas in the current database. A schema is a namespace that contains database objects like tables, views, and functions, allowing for better organization and management.",
  "scenarios": [
    {
      "title": "Exploring a New Data Warehouse Structure",
      "details": {
        "setup": "You are a data analyst connecting to your company's data warehouse for the first time. You know that data is separated into different logical areas, but you are unsure of their exact names.",
        "goal": "To get a high-level overview of the database's organization by identifying all available schemas before you start querying tables.",
        "outcome": "A list of all schemas is displayed, revealing the names like `raw_data`, `staging`, and `analytics`, which helps you understand the data flow and where to find specific datasets."
      },
      "example": {
        "command": "\\dn",
        "explanation": "Executing this command in `psql` immediately returns a list of schema names and their respective owners. It is the quickest and most direct way to orient yourself within a database's structure."
      },
      "notes": [
        "The `public` schema is the default schema and will appear in most databases.",
        "To get more detailed information, including access privileges and descriptions, use the `\\dn+` command.",
        "The standard SQL equivalent is `SELECT nspname FROM pg_namespace;`."
      ]
    }
  ]
}