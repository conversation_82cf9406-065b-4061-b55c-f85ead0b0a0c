export const command4 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "List Functions"
  ],
  "command": "\\df",
  "description": "Lists all available functions in the current database, showing their schema, name, result data type, and argument types.",
  "scenarios": [
    {
      "title": "Auditing Custom Business Logic in a Database",
      "details": {
        "setup": "You are a new developer joining a team that manages a mature financial application. The database contains years of accumulated business logic implemented as PostgreSQL functions.",
        "goal": "To get a high-level overview of all custom functions, their names, and what arguments they accept, to understand the existing codebase and identify a specific function for calculating interest.",
        "outcome": "A complete list of functions is displayed, allowing you to quickly identify potentially relevant functions like `calculate_interest(numeric, integer)` for further investigation."
      },
      "example": {
        "command": "\\df public.*",
        "explanation": "Using `\\df` with the `public.*` pattern filters the list to show only functions within the 'public' schema, which is a common place for custom application logic. This helps hide system-level functions and narrow down the search."
      },
      "notes": [
        "To inspect the source code of a specific function you find, use `\\sf function_name`.",
        "Add a `+` to the command (`\\df+`) to get additional details, including the function's volatility (e.g., VOLATILE, STABLE, IMMUTABLE) and the security definition (e.g., INVOKER, DEFINER)."
      ]
    }
  ]
}