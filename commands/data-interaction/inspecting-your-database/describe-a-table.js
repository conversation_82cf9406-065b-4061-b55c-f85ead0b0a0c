export const command1 = {
  "path": [
    "Interacting with Data",
    "Inspecting Your Database",
    "Describe a Table"
  ],
  "command": "\\d <table_name>",
  "description": "Shows the columns, data types, indexes, constraints, and triggers for a specific table. This is a psql meta-command.",
  "parameters": [
    {
      "flag": "<table_name>",
      "name": "Table Name",
      "description": "The name of the table you want to inspect."
    }
  ],
  "scenarios": [
    {
      "title": "Onboarding a New Developer to an Existing Project",
      "details": {
        "setup": "A developer is joining your team and needs to work on a feature involving user authentication. They are unfamiliar with the database schema and need to understand the structure of the `users` table.",
        "goal": "To quickly view the columns (like `id`, `email`, `password_hash`), their data types, primary keys, and any constraints (e.g., `email` must be unique) before writing any code.",
        "outcome": "The developer receives a clear, formatted summary of the `users` table structure directly in their terminal, allowing them to proceed with confidence."
      },
      "example": {
        "command": "\\d users",
        "explanation": "This command instantly fetches and displays the schema for the `users` table. The output shows column names, data types (e.g., `integer`, `varchar`), and details about the primary key and any unique constraints, providing all the necessary context for the task."
      },
      "notes": [
        "For even more detailed information, including table storage settings and descriptions, use the `\\d+ users` command."
      ]
    }
  ]
}