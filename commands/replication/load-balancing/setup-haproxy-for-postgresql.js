export const command10 = {
  "type": "config",
  "path": [
    "Replication and High Availability",
    "Load Balancing",
    "Setup HAProxy for PostgreSQL"
  ],
  "description": "Provides a configuration for HAProxy to create a highly available PostgreSQL cluster. This setup enables read/write splitting by directing write operations to a primary server and load balancing read queries across multiple standby (replica) servers.",
  "config_template": `# haproxy.cfg
frontend pg_writes
    bind *:5000
    default_backend pg_primary_backend

frontend pg_reads
    bind *:5001
    default_backend pg_replicas_backend

backend pg_primary_backend
    mode tcp
    option pgsql-check user <check_user>
    server primary <primary_ip>:<port> check

backend pg_replicas_backend
    mode tcp
    balance roundrobin
    option pgsql-check user <check_user>
    server replica1 <replica1_ip>:<port> check
    server replica2 <replica2_ip>:<port> check
  `,
  "directives": [
    {
      "directive": "frontend",
      "name": "Frontend Block",
      "description": "Defines a listening address and port that applications connect to. We use two frontends: one for write traffic and one for read traffic."
    },
    {
      "directive": "backend",
      "name": "Backend Block",
      "description": "Defines a pool of servers to which the frontend forwards traffic. One backend points only to the primary, while the other load balances across all replicas."
    },
    {
      "directive": "mode tcp",
      "name": "TCP Mode",
      "description": "Essential for PostgreSQL, as it operates at the TCP level, not HTTP."
    },
    {
      "directive": "option pgsql-check",
      "name": "PostgreSQL Health Check",
      "description": "Performs a simple connection and authentication check to ensure the PostgreSQL server is responsive before routing traffic to it."
    },
    {
      "directive": "balance roundrobin",
      "name": "Load Balancing Algorithm",
      "description": "Distributes incoming read requests evenly across the available replica servers in a cyclical fashion."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing Read/Write Splitting for a High-Traffic Web Application",
      "details": {
        "setup": "A growing web application uses a single PostgreSQL server for all database operations. During peak hours, the high volume of read queries (e.g., fetching articles, comments, user profiles) slows down critical write operations (e.g., new signups, posting content). You have already set up two streaming replicas.",
        "goal": "To significantly improve performance by offloading all read queries to the replicas, reserving the primary server's resources exclusively for write operations.",
        "outcome": "The application is configured to use two database connection strings. All writes go to the HAProxy write port, and all reads go to the read port. This results in faster response times and better scalability."
      },
      "example": {
        "configuration": `# /etc/haproxy/haproxy.cfg

# Frontend for write traffic to the primary
frontend pg_writes
    bind *:5000
    default_backend pg_primary_backend

# Frontend for read traffic to the replicas
frontend pg_reads
    bind *:5001
    default_backend pg_replicas_backend

# Backend pool containing only the primary server
backend pg_primary_backend
    mode tcp
    option pgsql-check user haproxy_check
    server primary 10.0.1.10:5432 check

# Backend pool for load balancing read replicas
backend pg_replicas_backend
    mode tcp
    balance roundrobin
    option pgsql-check user haproxy_check
    server replica1 10.0.1.11:5432 check
    server replica2 10.0.1.12:5432 check
`,
        "explanation": "This configuration creates two entry points. The application connects to port 5000 for all `INSERT/UPDATE/DELETE` statements, which HAProxy forwards directly to the primary server (10.0.1.10). For `SELECT` statements, the application connects to port 5001, and HAProxy distributes the load between replica1 (10.0.1.11) and replica2 (10.0.1.12). The `pgsql-check` continuously monitors the health of all servers."
      },
      "notes": [
        "Your application code or data access layer must be updated to use the appropriate port (5000 for writes, 5001 for reads).",
        "You must create a user (e.g., `haproxy_check`) in PostgreSQL for the health check to work. This user only needs connection permission.",
        "After modifying `haproxy.cfg`, you must reload the HAProxy service for changes to take effect (e.g., `sudo systemctl reload haproxy`)."
      ]
    }
  ]
};