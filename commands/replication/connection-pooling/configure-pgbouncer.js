export const command9 = {
  "type": "command",
  "path": [
    "Replication and High Availability",
    "Connection Pooling",
    "Configure pgBouncer"
  ],
  // A generic action, as the file path is platform-dependent
  "command": "edit pgbouncer.ini",
  "description": "Configure the pgBouncer connection pooler by editing its primary configuration file (pgbouncer.ini) to manage database connections efficiently, reduce overhead, and improve application scalability.",
  "platformDependent": true,
  // These are now framed as "Key Settings" within the file, not command-line flags.
  "parameters": [
    {
      "flag": "[databases]",
      "name": "Database Definitions",
      "description": "Defines the connection strings for the upstream PostgreSQL databases."
    },
    {
      "flag": "listen_addr",
      "name": "Listen Address",
      "description": "IP address pgBouncer listens on for client connections. Use '*' for all interfaces."
    },
    {
      "flag": "listen_port",
      "name": "Listen Port",
      "description": "Port pgBouncer listens on (e.g., 6432). Your application will connect to this port."
    },
    {
      "flag": "pool_mode",
      "name": "Pooling Mode",
      "description": "The pooling strategy: 'session' (least aggressive), 'transaction' (most common), or 'statement' (most aggressive)."
    },
    {
      "flag": "auth_type",
      "name": "Authentication Type",
      "description": "Method for authenticating clients. `md5` or `scram-sha-256` are common and secure choices."
    },
    {
      "flag": "auth_file",
      "name": "Authentication File",
      "description": "Path to the file that stores user credentials for pgBouncer to use (`userlist.txt`)."
    }
  ],
  "scenarios": [
    {
      "title": "Scaling a Web App by Pooling Connections",
      "details": {
        "setup": "A growing web application with hundreds of concurrent users is creating a new database connection for each request. This is causing high memory consumption and slow connection times on the main PostgreSQL server, limiting scalability.",
        "goal": "To introduce pgBouncer as an intermediary to pool connections. The objective is to handle up to 1000 client connections while maintaining a small, stable pool of no more than 50 actual connections to the PostgreSQL server.",
        "outcome": "The application now points to pgBouncer. The database server's connection count remains low and stable, response times improve, and the system can handle significantly more user traffic without exhausting server resources."
      },
      "example": {
        // The 'variants' array provides platform-specific instructions
        "variants": [
          {
            "platform": "Linux (apt/yum)",
            "command": "sudo nano /etc/pgbouncer/pgbouncer.ini\n\n# --- Add or modify these lines ---\n[databases]\nwebapp_db = host=127.0.0.1 port=5432 dbname=prod_db\n\n[pgbouncer]\nlisten_addr = *\nlisten_port = 6432\npool_mode = transaction\nauth_type = md5\nauth_file = /etc/pgbouncer/userlist.txt\nmax_client_conn = 1000\ndefault_pool_size = 50\n\n# --- After saving, restart the service ---\nsudo systemctl restart pgbouncer",
            "explanation": "On most Linux distributions, the configuration file is located at `/etc/pgbouncer/`. This configuration sets up a pool for the `prod_db` database, listens on all interfaces on port 6432, and uses transaction pooling. After editing, the `systemctl` command is used to apply the changes."
          },
          {
            "platform": "Windows",
            "command": "# Edit C:\\Program Files\\pgBouncer\\etc\\pgbouncer.ini\n\n# --- Add or modify these lines ---\n[databases]\nwebapp_db = host=127.0.0.1 port=5432 dbname=prod_db\n\n[pgbouncer]\nlisten_addr = *\nlisten_port = 6432\npool_mode = transaction\nauth_type = md5\nauth_file = C:\\Program Files\\pgBouncer\\etc\\userlist.txt\nmax_client_conn = 1000\ndefault_pool_size = 50\n\n# --- After saving, restart the service from Command Prompt as Admin ---\nnet stop pgbouncer && net start pgbouncer",
            "explanation": "On Windows, the configuration is typically in the installation directory. The settings are identical, but file paths use backslashes. The pgBouncer service is restarted using the `net stop` and `net start` commands."
          }
        ]
      },
      "notes": [
        "Transaction pooling (`pool_mode = transaction`) is the most common and effective mode for web applications that use short-lived transactions.",
        "Remember to configure the `userlist.txt` authentication file separately with the necessary user credentials.",
        "Ensure the server's firewall allows connections to the `listen_port` (e.g., 6432) from your application servers."
      ]
    }
  ]
};