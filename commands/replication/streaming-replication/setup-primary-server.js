export const command0 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Streaming Replication",
    "Setup Primary Server"
  ],
  "description": "Performs the initial and most critical configuration on a primary server to prepare it for streaming replication. This involves setting the Write-Ahead Log (WAL) level and other key parameters to allow standby servers to connect and receive data.",
  "query_template": "ALTER SYSTEM SET [parameter] = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM SET wal_level = 'replica';",
      "name": "WAL Level",
      "description": "Sets the Write-Ahead Log level. It must be at least 'replica' (or 'logical' for logical replication) to write enough information to the WAL to support replication."
    },
    {
      "component": "ALTER SYSTEM SET max_wal_senders = 5;",
      "name": "Max WAL Senders",
      "description": "Defines the maximum number of concurrent replication connections. This number should be at least the number of standby servers you plan to connect."
    },
    {
      "component": "ALTER SYSTEM SET wal_keep_size = '512MB';",
      "name": "WAL Keep Size (Postgres 13+)",
      "description": "Specifies the minimum amount of WAL files to retain in the `pg_wal` directory. This helps prevent a standby server that is temporarily disconnected from falling too far behind."
    },
    {
      "component": "ALTER SYSTEM SET archive_mode = 'on';",
      "name": "Archive Mode",
      "description": "Enables WAL archiving, a more robust mechanism for ensuring WAL files are available for a standby, especially if it is offline for an extended period."
    }
  ],
  "scenarios": [
    {
      "title": "Preparing a Primary Database for a New Hot Standby Replica",
      "details": {
        "setup": "Your company has a single production PostgreSQL database. To improve availability and offload reporting queries, you need to add a read-only standby server that stays in sync with the primary.",
        "goal": "To modify the primary server's configuration to enable it to stream its transaction logs (WAL) to the new standby server.",
        "outcome": "The primary server is correctly configured. After a restart, it is ready to accept replication connections from a standby."
      },
      "example": {
        "query": "-- Run these SQL commands on the primary server:\n\nALTER SYSTEM SET wal_level = 'replica';\nALTER SYSTEM SET max_wal_senders = '5';\nALTER SYSTEM SET wal_keep_size = '512MB';\n\n-- Optional but highly recommended for production:\nALTER SYSTEM SET archive_mode = 'on';\nALTER SYSTEM SET archive_command = 'cp %p /var/lib/postgresql/archives/%f';",
        "explanation": "This block of commands configures the essential replication settings. `wal_level = 'replica'` enables the necessary WAL information. `max_wal_senders` reserves connection slots for replicas. `wal_keep_size` provides a buffer for replicas that may disconnect temporarily. The optional `archive_mode` and `archive_command` provide a robust fallback by copying WAL files to a safe location."
      },
      "notes": [
        "IMPORTANT: A full server restart is required for the `wal_level` change to take effect. Use `pg_ctl restart` or your service manager (e.g., `sudo systemctl restart postgresql`).",
        "You must also create a dedicated user for replication: `CREATE ROLE replicator WITH REPLICATION LOGIN PASSWORD 'strong_password';`",
        "Finally, you must edit the `pg_hba.conf` file on the primary server to allow the `replicator` user to connect from the standby server's IP address."
      ]
    }
  ]
};