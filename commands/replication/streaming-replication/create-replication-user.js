export const command1 = {
  // --- Differentiator for application logic ---
  "type": "query",

  "path": [
    "Replication and High Availability",
    "Streaming Replication",
    "Create Replication User"
  ],
  "description": "Creates a dedicated, low-privilege user account specifically for streaming replication, which allows a standby server to connect to the primary.",

  // --- Query-specific fields ---
  "query_template": "CREATE USER [username] WITH REPLICATION LOGIN CONNECTION LIMIT [number] PASSWORD '[secure_password]';",
  "components": [
    {
      "component": "CREATE USER [username]",
      "name": "User Creation",
      "description": "The fundamental SQL statement to define a new database user (role)."
    },
    {
      "component": "WITH REPLICATION",
      "name": "Replication Privilege",
      "description": "A crucial, special-purpose privilege that grants the ability to initiate streaming replication and manage replication slots. It does not grant permission to read or write data."
    },
    {
      "component": "LOGIN",
      "name": "Login Privilege",
      "description": "Allows this user to establish a connection with the database server."
    },
    {
      "component": "CONNECTION LIMIT [number]",
      "name": "Connection Limit",
      "description": "An optional but recommended security measure to specify the maximum number of concurrent connections this user can make."
    },
    {
      "component": "PASSWORD '[secure_password]'",
      "name": "Authentication",
      "description": "Sets the password for the user. In production, this should be a strong, unique password."
    }
  ],

  // --- Scenario-based examples ---
  "scenarios": [
    {
      "title": "Setting Up a Dedicated User for a New Standby Server",
      "details": {
        "setup": "You are configuring a new physical standby server to create a high-availability cluster. For the standby to connect and stream data from the primary, it needs a secure and dedicated user account.",
        "goal": "To create a user that has just enough privilege to perform replication and nothing more, following the principle of least privilege.",
        "outcome": "A new user named 'replicator' is created. This user can connect in replication mode but cannot access (SELECT, INSERT, etc.) any database tables, ensuring a secure replication channel."
      },
      "example": {
        "query": "CREATE USER replicator WITH REPLICATION LOGIN CONNECTION LIMIT 3 PASSWORD 'Str0ngP@ssw0rd-for-R3pl1cat!on';",
        "explanation": "This query creates a user named `replicator` specifically for replication. The `REPLICATION` role grants the necessary permissions to stream data. The `CONNECTION LIMIT 3` is a safeguard to prevent this user from consuming too many connection slots. A strong password is set for secure authentication between the primary and standby servers."
      },
      "notes": [
        "The credentials for this user must be added to the standby server's `primary_conninfo` setting (in `postgresql.conf` or `standby.signal`).",
        "After creating the user, you must configure the primary server's `pg_hba.conf` file to allow this user to connect from the standby server's IP address.",
        "For production environments, consider using more advanced authentication methods like SCRAM or certificates instead of password-based authentication."
      ]
    }
  ]
};