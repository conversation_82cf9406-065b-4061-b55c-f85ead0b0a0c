export const command3 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Streaming Replication",
    "Check Replication Status"
  ],
  "description": "Monitors the status and health of all connected standby (replica) servers from the primary server. This view is essential for diagnosing replication lag and ensuring high availability.",
  "query_template": "SELECT [columns] FROM pg_stat_replication;",
  "components": [
    {
      "component": "client_addr",
      "name": "Client Address",
      "description": "The IP address of the connected standby server."
    },
    {
      "component": "state",
      "name": "Replication State",
      "description": "The current state of the replica connection. Key states are 'startup', 'catchup', and 'streaming' (the healthy, active state)."
    },
    {
      "component": "sync_state",
      "name": "Sync State",
      "description": "For synchronous replication, this shows the replica's state ('sync', 'async', 'quorum'). For asynchronous replication, it will be 'async'."
    },
    {
      "component": "sent_lsn",
      "name": "Sent LSN",
      "description": "The last Write-Ahead Log (WAL) location sent from the primary to this standby."
    },
    {
      "component": "replay_lsn",
      "name": "Replay LSN",
      "description": "The last WAL location replayed (applied) on the standby. The difference between this and `sent_lsn` is a measure of replication lag."
    },
    {
      "component": "replay_lag",
      "name": "Replay Lag",
      "description": "The time elapsed since the last WAL record was replayed on the standby. A direct and easy-to-understand measure of data lag. (Available in PostgreSQL 10+)"
    }
  ],
  "scenarios": [
    {
      "title": "Daily Health Check of a Read Replica",
      "details": {
        "setup": "You are the administrator of a production database with a primary server and a read replica used for offloading analytics queries. You need to perform a routine check to ensure the replica is online and not falling behind.",
        "goal": "To confirm that the read replica is actively streaming from the primary and that the data lag is within an acceptable threshold (e.g., under 1 second).",
        "outcome": "The query returns a row for the replica showing its state as 'streaming' and a `replay_lag` of less than one second, confirming the replication is healthy."
      },
      "example": {
        "query": "SELECT application_name, client_addr, state, sync_state, replay_lag FROM pg_stat_replication;",
        "explanation": "This query provides a concise overview of replication health. The `application_name` (e.g., 'my_replica_service') confirms which replica you are looking at, `state` should be 'streaming', and `replay_lag` gives a human-readable measure of how up-to-date the replica is. This is the go-to command for a quick status check."
      },
      "notes": [
        "This query must be run on the primary server; it will return an empty result if run on a replica.",
        "A `NULL` or very small value for `replay_lag` is ideal.",
        "If a replica is missing from the output, it means it is not connected to the primary."
      ]
    },
    {
      "title": "Investigating Performance Issues on a Standby Server",
      "details": {
        "setup": "Users are reporting that data written to the primary database is taking several minutes to appear on a hot standby server, causing issues for reporting tools.",
        "goal": "To quantify the exact amount of replication lag in bytes to understand the severity of the delay.",
        "outcome": "The query calculates the lag in megabytes, revealing a significant delay that needs to be addressed (e.g., network latency or I/O bottleneck on the standby).",
      },
      "example": {
        "query": "SELECT client_addr, pg_wal_lsn_diff(pg_current_wal_lsn(), replay_lsn) AS lag_in_bytes FROM pg_stat_replication;",
        "explanation": "This query uses the `pg_wal_lsn_diff` function to calculate the exact difference, in bytes, between the primary's current WAL position and the last position replayed by the standby. It provides a more precise measure of lag than time-based metrics, which is crucial for deep-dive troubleshooting."
      },
      "notes": [
        "A large `lag_in_bytes` value that is consistently growing indicates the replica cannot keep up with the primary's write load.",
        "This could be due to slow disk I/O on the replica, insufficient network bandwidth, or long-running queries on the replica blocking WAL application."
      ]
    }
  ]
};