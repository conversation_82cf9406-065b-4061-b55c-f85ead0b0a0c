export const command2 = {
  "type": "command",
  "platformDependent": true,
  "path": [
    "Replication and High Availability",
    "Streaming Replication",
    "Setup Standby Server"
  ],
  "command": "pg_basebackup -h <primary_host> -D <data_directory> -U <replication_user> -P -R -X stream",
  "description": "Clones a primary PostgreSQL server's data directory to a new standby server. This is the foundational step for setting up streaming replication, creating a byte-for-byte copy of the database while ensuring all write-ahead logs (WAL) are included.",
  "parameters": [
    {
      "flag": "-h",
      "name": "host",
      "description": "The hostname or IP address of the primary (source) server."
    },
    {
      "flag": "-D",
      "name": "directory",
      "description": "The target data directory on the standby (destination) server. This directory must be empty or not exist."
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "A dedicated PostgreSQL user with REPLICATION privileges on the primary server."
    },
    {
      "flag": "-P",
      "name": "progress",
      "description": "Displays a progress report during the backup process."
    },
    {
      "flag": "-R",
      "name": "write-recovery-conf",
      "description": "Crucially, this creates the necessary connection configuration (`postgresql.auto.conf` and a `standby.signal` file) in the target directory, making the new instance a standby."
    },
    {
      "flag": "-X stream",
      "name": "wal-method=stream",
      "description": "Streams the write-ahead log (WAL) files in parallel with the data backup, ensuring the standby is fully up-to-date and can connect immediately."
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Prompts for the replication user's password. It is often better to use a `.pgpass` file in production."
    }
  ],
  "scenarios": [
    {
      "title": "Initializing a Hot Standby Server for High Availability",
      "details": {
        "setup": "You have a primary production database server at IP `*********`. You have just provisioned a new machine (`*********`) that will act as a hot standby (read replica). The PostgreSQL service on the new machine is currently stopped.",
        "goal": "To securely and efficiently clone the entire database cluster from the primary to the standby machine, preparing it to start up and begin streaming replication automatically.",
        "outcome": "A complete copy of the primary's data directory is created on the standby machine, along with the required `standby.signal` and connection settings in `postgresql.auto.conf`, ready for the standby PostgreSQL service to be started."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux",
            "command": "pg_basebackup -h ********* -D /var/lib/postgresql/15/main -U replicator -P -R -X stream",
            "explanation": "This command is executed on the standby server (`*********`). It connects to the primary (`-h *********`) as the `replicator` user and copies the entire data directory to the standard Linux path (`-D /var/lib/postgresql/15/main`). The `-R` flag is essential, as it configures the new cluster to be a standby."
          },
          {
            "platform": "Windows",
            "command": "pg_basebackup -h ********* -D \"C:\\Program Files\\PostgreSQL\\15\\data\" -U replicator -P -R -X stream",
            "explanation": "This command, run on the Windows standby server, performs the same action but targets the default Windows data directory. The path is quoted due to the space in 'Program Files'. The `-R` flag works identically, creating the necessary standby configuration files."
          }
        ]
      },
      "notes": [
        "Before running this, ensure the PostgreSQL service on the standby machine is stopped and the data directory is empty.",
        "You must configure the primary server's `pg_hba.conf` file to allow the `replicator` user to connect from the standby's IP address.",
        "The `replicator` user must be created on the primary server with the `REPLICATION` role: `CREATE USER replicator WITH REPLICATION PASSWORD 'your_password';`",
        "After `pg_basebackup` completes successfully, you can start the PostgreSQL service on the standby server, and it will begin replicating."
      ]
    }
  ]
};