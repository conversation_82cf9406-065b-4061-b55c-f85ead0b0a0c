export const command4 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Logical Replication",
    "Create Publication"
  ],
  "description": "Creates a publication on the primary database server, defining a set of changes from specified tables that will be made available to subscribers. This is the first step in setting up logical replication.",
  "query_template": "CREATE PUBLICATION [publication_name] FOR [TABLE table1, table2 | ALL TABLES] [WITH (options)];",
  "components": [
    {
      "component": "CREATE PUBLICATION [publication_name]",
      "name": "Define Publication",
      "description": "The command to create a new, named publication."
    },
    {
      "component": "FOR ALL TABLES",
      "name": "Replicate All Tables",
      "description": "A convenient option to include all existing and future tables in the publication."
    },
    {
      "component": "FOR TABLE [table1], [table2], ...",
      "name": "Replicate Specific Tables",
      "description": "Allows you to select only specific tables to be replicated, giving you granular control."
    },
    {
      "component": "WITH (publish = 'insert,update,delete,truncate')",
      "name": "Publish Operations",
      "description": "An optional clause to specify which DML operations (e.g., only 'insert' and 'update') should be replicated."
    }
  ],
  "scenarios": [
    {
      "title": "Selective Replication of Customer and Order Data to a Reporting Database",
      "details": {
        "setup": "You manage a busy e-commerce application with a primary OLTP database. You need to feed a separate reporting database with near real-time data for analytics, but you only need the `customers` and `orders` tables. Replicating the entire database would be inefficient.",
        "goal": "To create a publication named `analytics_publication` on the primary database that only includes changes (inserts, updates, and deletes) from the `customers` and `orders` tables.",
        "outcome": "A publication is successfully created on the primary server, ready to stream changes from the specified tables to any authorized subscriber."
      },
      "example": {
        "query": "CREATE PUBLICATION analytics_publication FOR TABLE customers, orders;",
        "explanation": "This command creates a publication specifically for the `customers` and `orders` tables. Any DML operations (INSERT, UPDATE, DELETE) on these two tables will now be captured and prepared for replication to a subscriber. It is more efficient than `FOR ALL TABLES` as it limits the replication scope."
      },
      "notes": [
        "This is only the publisher side of the setup. You must create a corresponding `SUBSCRIPTION` on the reporting database to start receiving data.",
        "All tables included in a publication must have a `PRIMARY KEY` or have their `REPLICA IDENTITY` set to `FULL` to ensure updates and deletes can be replicated correctly.",
        "The user executing this command must have the `CREATE` privilege in the database."
      ]
    }
  ]
};