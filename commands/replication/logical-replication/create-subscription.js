export const command5 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Logical Replication",
    "Create Subscription"
  ],
  "description": "Creates a new logical replication subscription on a subscriber node. This command initiates the process of receiving and applying changes from a publisher for one or more specified publications.",
  "query_template": "CREATE SUBSCRIPTION [subscription_name] CONNECTION '[connection_string]' PUBLICATION [publication_name];",
  "components": [
    {
      "component": "CREATE SUBSCRIPTION [subscription_name]",
      "name": "Subscription Definition",
      "description": "Defines and names the new subscription. This name must be unique within the database."
    },
    {
      "component": "CONNECTION '[...]' ",
      "name": "Publisher Connection String",
      "description": "Specifies the libpq connection string needed to connect to the publishing (primary) database."
    },
    {
      "component": "PUBLICATION [publication_name]",
      "name": "Target Publication",
      "description": "Specifies which publication on the publisher this subscription will track. You can subscribe to multiple publications."
    },
    {
      "component": "WITH (copy_data = true, ...)",
      "name": "Subscription Options",
      "description": "Optional parameters to control the subscription's behavior, such as whether to copy existing data (`copy_data`) or to disable the subscription on creation (`enabled = false`)."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a Real-Time Analytics and Reporting Database",
      "details": {
        "setup": "Your company has a high-traffic online transaction processing (OLTP) database. You need to run complex, long-running analytical queries for a business intelligence (BI) dashboard, but running them on the primary database slows it down.",
        "goal": "To create a separate, secondary database (`analytics_db`) that receives a real-time stream of data changes from the primary `production_db`, allowing the BI tools to query it without impacting production.",
        "outcome": "A subscription is successfully created on `analytics_db`. It connects to the `production_db`, synchronizes the initial table data, and begins applying all subsequent `INSERT`, `UPDATE`, and `DELETE` operations as they happen."
      },
      "example": {
        "query": "CREATE SUBSCRIPTION analytics_sub CONNECTION 'host=prod-db.example.com port=5432 dbname=production_db user=replicator password=secure_password' PUBLICATION bi_publication;",
        "explanation": "This query creates a subscription named `analytics_sub` on the reporting server. It connects to the production database at `prod-db.example.com` using the dedicated `replicator` user. It subscribes to a publication named `bi_publication`, which the publisher has already configured to share the necessary tables (e.g., `orders`, `customers`)."
      },
      "notes": [
        "Before running this, ensure the schema (e.g., table definitions) of the replicated tables already exists on the subscriber database.",
        "The user specified in the connection string (e.g., `replicator`) must have the `REPLICATION` attribute on the publisher.",
        "Ensure network firewalls allow the subscriber server to connect to the publisher server on the specified port (e.g., 5432)."
      ]
    }
  ]
};