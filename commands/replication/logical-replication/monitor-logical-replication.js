export const command6 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Logical Replication",
    "Monitor Logical Replication"
  ],
  "description": "Monitors the status and lag of a logical replication subscription by querying the `pg_stat_subscription` view on the subscriber database.",
  "query_template": "SELECT [columns] FROM pg_stat_subscription;",
  "components": [
    {
      "component": "pg_stat_subscription",
      "name": "Statistics View",
      "description": "A system view that shows one row for each subscription worker, providing real-time statistics about the replication process."
    },
    {
      "component": "received_lsn",
      "name": "Received LSN",
      "description": "The Log Sequence Number (LSN) of the last transaction data received from the publisher."
    },
    {
      "component": "latest_end_lsn",
      "name": "Latest End LSN",
      "description": "The LSN of the last transaction committed on the publisher and reported to the subscriber."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Data Freshness on a Read-Only Reporting Server",
      "details": {
        "setup": "You have a primary production database and a secondary server for analytics and reporting. You've set up logical replication to copy data from the `orders` and `customers` tables to the reporting server using a subscription named `reporting_sub`.",
        "goal": "To confirm that the replication is running smoothly and to measure the amount of data lag between the primary and the reporting server.",
        "outcome": "The query returns the status of the `reporting_sub` subscription, showing a minimal lag in bytes, which confirms the reporting data is nearly real-time."
      },
      "example": {
        "query": "SELECT subname, received_lsn, latest_end_lsn, pg_wal_lsn_diff(latest_end_lsn, received_lsn) AS replication_lag_bytes FROM pg_stat_subscription WHERE subname = 'reporting_sub';",
        "explanation": "This query targets the specific `reporting_sub` subscription. It uses the `pg_wal_lsn_diff()` function to calculate the exact difference in bytes between the last LSN committed on the publisher (`latest_end_lsn`) and the last LSN received by the subscriber (`received_lsn`). This provides a precise measurement of replication lag."
      },
      "notes": [
        "A `replication_lag_bytes` value that is consistently zero or very low indicates a healthy replication.",
        "If the lag is large or growing, it could signal issues with network bandwidth, high load on the subscriber, or a misconfiguration.",
        "If the query returns no rows for your subscription, it may mean the subscription worker is not running. Check the subscriber's PostgreSQL logs for errors."
      ]
    }
  ]
};