export const command7 = {
  "type": "command",
  "path": [
    "Replication and High Availability",
    "Failover and Recovery",
    "Promote Standby Server"
  ],
  "command": "pg_ctl promote -D <data_directory>",
  "description": "Promotes a standby server to become the new primary. This action ends the server's recovery mode and begins allowing read-write transactions, making it the new source of truth for the database cluster.",
  "parameters": [
    {
      "flag": "-D",
      "name": "data_directory",
      "description": "Specifies the file system location of the database cluster's data directory."
    }
  ],
  "scenarios": [
    {
      "title": "Manual Failover After a Primary Server Failure",
      "details": {
        "setup": "You are managing a streaming replication setup. The primary server at `************` has experienced a critical hardware failure and is offline. A hot standby server at `************` is up-to-date and ready to take over.",
        "goal": "To quickly promote the standby server to become the new primary, minimizing downtime and allowing applications to reconnect and resume operations.",
        "outcome": "The standby server is successfully promoted. It is now the primary, accepting both read and write connections. The replication slot is no longer active, and the server's timeline has advanced."
      },
      "example": {
        "variants": [
          {
            "platform": "Shell (pg_ctl)",
            "command": "pg_ctl promote -D /var/lib/postgresql/15/main",
            "explanation": "Using `pg_ctl` is the most common and robust method, ideal for use in automated failover scripts or when you have direct shell access to the standby server. You must specify the data directory for the PostgreSQL instance."
          },
          {
            "platform": "SQL (psql)",
            "command": "SELECT pg_promote();",
            "explanation": "This SQL function is a convenient alternative if you are already connected to the standby server with a superuser account (e.g., via `psql`). It achieves the same result as the `pg_ctl` command. It will return `true` on success."
          }
        ]
      },
      "notes": [
        "Promoting a standby is irreversible. Once promoted, it cannot go back to being a standby for the old primary.",
        "After promotion, the old primary server must be rebuilt (e.g., using `pg_rewind`) before it can be used as a standby for the newly promoted primary.",
        "If you have other standby servers, they must be reconfigured to replicate from the new primary.",
        "For fully automated failover, consider using tools like Patroni, which manage the promotion process automatically."
      ]
    }
  ]
};