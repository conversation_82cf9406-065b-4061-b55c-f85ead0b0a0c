export const command8 = {
  "type": "query",
  "path": [
    "Replication and High Availability",
    "Failover and Recovery",
    "Check Recovery Status"
  ],
  "description": "Checks if a PostgreSQL instance is a primary or a standby in recovery, and provides key details about its replication state.",
  "query_template": "SELECT pg_is_in_recovery(), pg_last_wal_receive_lsn(), pg_last_wal_replay_lsn();",
  "components": [
    {
      "component": "pg_is_in_recovery()",
      "name": "Recovery Status Check",
      "description": "A built-in function that returns `true` if the server is a standby in recovery mode, and `false` if it is a primary."
    },
    {
      "component": "pg_last_wal_receive_lsn()",
      "name": "Last Received WAL Location",
      "description": "On a standby, this shows the Log Sequence Number (LSN) of the last transaction log data received from the primary."
    },
    {
      "component": "pg_last_wal_replay_lsn()",
      "name": "Last Replayed WAL Location",
      "description": "On a standby, this shows the LSN of the last transaction log data that has been applied (replayed) to the database."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Standby Server Health Before a Manual Failover",
      "details": {
        "setup": "You are managing a high-availability setup with one primary and one streaming replication standby server. You need to perform scheduled maintenance on the primary, which requires a manual failover to the standby.",
        "goal": "Before promoting the standby, you must confirm that it is running, in recovery mode, and has a minimal replication lag to ensure no data loss during the switch.",
        "outcome": "The query returns a clear status report, confirming the standby is active and fully synchronized, giving you the confidence to proceed with the failover."
      },
      "example": {
        "query": "SELECT \n  pg_is_in_recovery() AS is_standby,\n  pg_last_wal_receive_lsn() AS received_lsn,\n  pg_last_wal_replay_lsn() AS replayed_lsn;",
        "explanation": "This consolidated query provides a complete health check in one command. `is_standby` should be `true`. The critical part is comparing `received_lsn` and `replayed_lsn`. If they are identical, it means the standby has applied all the data it has received and the replication lag is zero."
      },
      "notes": [
        "If you run this query on a primary server, `pg_is_in_recovery()` will return `false`, and the other two functions will return `NULL`.",
        "The difference between the `received_lsn` and `replayed_lsn` values can be used to calculate the replication lag in terms of data volume.",
        "For more detailed real-time monitoring of replication lag in newer PostgreSQL versions, you can also query the `pg_stat_replication` view on the primary or use the `pg_wal_lsn_diff()` function."
      ]
    }
  ]
};