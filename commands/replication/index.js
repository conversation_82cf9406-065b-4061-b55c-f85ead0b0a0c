import { command0 } from './streaming-replication/setup-primary-server.js';
import { command1 } from './streaming-replication/create-replication-user.js';
import { command2 } from './streaming-replication/setup-standby-server.js';
import { command3 } from './streaming-replication/check-replication-status.js';
import { command4 } from './logical-replication/create-publication.js';
import { command5 } from './logical-replication/create-subscription.js';
import { command6 } from './logical-replication/monitor-logical-replication.js';
import { command7 } from './failover-and-recovery/promote-standby-server.js';
import { command8 } from './failover-and-recovery/check-recovery-status.js';
import { command9 } from './connection-pooling/configure-pgbouncer.js';
import { command10 } from './load-balancing/setup-haproxy-for-postgresql.js';

export const replicationCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10
];

export function validateReplicationCommands() {
  return replicationCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Replication and High Availability";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
