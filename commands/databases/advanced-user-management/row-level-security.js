export const command21 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Advanced User Management",
    "Row Level Security"
  ],
  "description": "Enables Row-Level Security (RLS) on a table. This is the foundational step for creating policies that control which specific rows a user is allowed to view, update, or delete.",
  "query_template": "ALTER TABLE [table_name] [action] ROW LEVEL SECURITY;",
  "components": [
    {
      "component": "ENABLE ROW LEVEL SECURITY",
      "name": "Enable RLS",
      "description": "Activates the RLS mechanism for the specified table. By itself, this blocks all access for regular users until a policy is created."
    },
    {
      "component": "DISABLE ROW LEVEL SECURITY",
      "name": "Disable RLS",
      "description": "Deactivates the RLS mechanism, removing all row-level access restrictions from the table."
    },
    {
      "component": "FORCE ROW LEVEL SECURITY",
      "name": "Force RLS",
      "description": "Applies RLS policies even to the table owner. By default, table owners bypass RLS. This provides an extra layer of security."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing Multi-Tenancy in a SaaS Application",
      "details": {
        "setup": "You are building a multi-tenant SaaS platform where multiple client organizations share the same database. A single `invoices` table contains billing information for all clients, identified by a `client_id` column.",
        "goal": "To ensure that users from one organization can ONLY see the invoices belonging to their own organization. A user from 'Client A' must never be able to see data from 'Client B'.",
        "outcome": "RLS is activated, and a security policy is in place. When a user queries the `invoices` table, PostgreSQL automatically and transparently filters the results to show only the rows matching their `client_id`."
      },
      "example": {
        "query": "--- Step 1: Enable Row-Level Security on the table\nALTER TABLE invoices ENABLE ROW LEVEL SECURITY;\n\n--- Step 2: Create a policy that defines the access rule\nCREATE POLICY tenant_invoice_isolation\nON invoices\nFOR ALL\nUSING ( client_id = current_setting('app.current_client_id')::integer );",
        "explanation": "First, we enable RLS on the `invoices` table. This blocks all access by default. Second, we create a policy named `tenant_invoice_isolation`. This policy tells PostgreSQL that for any query (`FOR ALL`), a row is only visible (`USING`) if its `client_id` matches a session variable named `app.current_client_id`. Your application would be responsible for setting this variable after a user logs in."
      },
      "notes": [
        "Your application must set the session variable for the policy to work. For example: `SET app.current_client_id = '101';` after a user from client 101 authenticates.",
        "By default, superusers and the table owner are exempt from RLS. Use `FORCE ROW LEVEL SECURITY` to apply policies to them as well.",
        "You can create separate policies for `SELECT`, `INSERT`, `UPDATE`, and `DELETE` for more granular control."
      ]
    }
  ]
};