export const command22 = {
  "type": "query",

  "path": [
    "Managing Databases and Roles",
    "Advanced User Management",
    "Create RLS Policy"
  ],
  "description": "Creates a Row-Level Security (RLS) policy to enforce fine-grained access control, ensuring that users can only interact with rows that meet specific conditions.",

  "query_template": "CREATE POLICY [policy_name] ON [table_name] FOR [command] TO [role] USING ([condition]) WITH CHECK ([condition]);",
  "components": [
    {
      "component": "CREATE POLICY [policy_name]",
      "name": "Policy Definition",
      "description": "Defines a new RLS policy with a unique name."
    },
    {
      "component": "ON [table_name]",
      "name": "Target Table",
      "description": "Specifies the table that this policy will protect."
    },
    {
      "component": "FOR [command]",
      "name": "Applicable Commands",
      "description": "Determines which commands the policy applies to. Can be ALL, SELECT, INSERT, UPDATE, or DELETE."
    },
    {
      "component": "TO [role]",
      "name": "Target Role(s)",
      "description": "Specifies which database role(s) this policy applies to. Can be a specific role or PUBLIC."
    },
    {
      "component": "USING ([condition])",
      "name": "Visibility Condition",
      "description": "The core of the policy. A row is visible or accessible only if this condition returns true."
    },
    {
      "component": "WITH CHECK ([condition])",
      "name": "Modification Condition",
      "description": "An optional clause that is enforced for INSERT and UPDATE operations. New or updated rows must satisfy this condition."
    }
  ],
  "scenarios": [
    {
      "title": "Enforcing Data Isolation in a Multi-Tenant SaaS Application",
      "details": {
        "setup": "You have a single `invoices` table for all customers, distinguished by an `organization_id` column. Application users connect to the database via a common role called `app_user`. Without RLS, a bug in the application code could potentially expose one organization's invoices to another.",
        "goal": "To create a database-level rule that guarantees an application user can only ever see, modify, or create invoices belonging to their own organization.",
        "outcome": "A security policy is created and enabled on the `invoices` table. Now, any query from `app_user` on that table will have an automatic, non-bypassable `WHERE organization_id = ...` clause appended by the database."
      },
      "example": {
        "query": [
          "// Set the organization_id for the current session. The application backend would do this upon user login.",
          "SET app.current_organization_id = 101;",
          "",
          "// Create the policy that checks this session variable.",
          "CREATE POLICY org_isolation_policy ON invoices",
          "FOR ALL",
          "TO app_user",
          "USING (organization_id = current_setting('app.current_organization_id')::integer)",
          "WITH CHECK (organization_id = current_setting('app.current_organization_id')::integer);",
          "",
          "// IMPORTANT: Policies are not active until you enable RLS on the table.",
          "ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;"
        ],
        "explanation": "This policy uses a session variable (`app.current_organization_id`) to identify the current user's organization. The `USING` clause ensures that only rows matching this ID are returned in a `SELECT`. The `WITH CHECK` clause ensures that any `INSERT` or `UPDATE` operation cannot assign the invoice to a different organization."
      },
      "notes": [
        "Before a policy has any effect, you MUST enable RLS on the table using `ALTER TABLE [table_name] ENABLE ROW LEVEL SECURITY;`.",
        "The table owner and superusers are typically exempt from RLS policies.",
        "Using `current_setting()` is a secure way to pass user-specific data to the RLS policy from your application backend."
      ]
    }
  ]
};