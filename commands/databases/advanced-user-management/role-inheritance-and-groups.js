export const command20 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Advanced User Management",
    "Create and Populate a Group Role"
  ],
  "description": "Creates a group role and assigns existing roles (users) as members. This simplifies privilege management by allowing you to grant permissions to the group instead of to each individual.",
  "query_template": "CREATE ROLE [group_name];\nGRANT [group_name] TO [member1], [member2];",
  "components": [
    {
      "component": "CREATE ROLE [group_name]",
      "name": "Define Group Role",
      "description": "This statement creates a new role. For groups, it's best practice to create them with the NOLOGIN attribute, as they are intended to be containers for privileges, not for logging in directly."
    },
    {
      "component": "GRANT [group_name] TO [members]",
      "name": "Assign Members",
      "description": "This statement adds one or more existing roles (users) as members of the new group role. These members will inherit the privileges granted to the group."
    }
  ],
  "scenarios": [
    {
      "title": "Streamlining Permissions for a New Development Team",
      "details": {
        "setup": "A new development team is starting, and you need to provide three developers (`alice`, `bob`, `carol`) with the ability to connect to the `dev_db` database and create new objects in the `api` schema.",
        "goal": "To create a single `developers` group role to manage these permissions collectively, avoiding the need to grant the same privileges to each user individually.",
        "outcome": "The `developers` group role is created and populated. Now, any permissions granted to `developers` are automatically available to `alice`, `bob`, and `carol`."
      },
      "example": {
        "query": "-- Step 1: Create the group role. NOLOGIN is used as this role is for privilege management, not direct login.\nCREATE ROLE developers NOLOGIN;\n\n-- Step 2: Add the developer user roles to the new group.\nGRANT developers TO alice, bob, carol;",
        "explanation": "This workflow first establishes the `developers` role as a privilege container. Then, it makes the individual user roles members of this group. Because roles `INHERIT` privileges by default, any permissions granted to `developers` will be immediately accessible by its members."
      },
      "notes": [
        "This is a core concept of Role-Based Access Control (RBAC) and is fundamental to managing permissions in a scalable way.",
        "As a next step, you could grant schema permissions to the group: `GRANT CREATE, USAGE ON SCHEMA api TO developers;`",
        "To remove a user from a group, use the `REVOKE` command, for example: `REVOKE developers FROM carol;`"
      ]
    }
  ]
};