export const command5 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "Delete a Database (Command Line)"
  ],
  "command": "dropdb [options] <database_name>",
  "description": "Deletes an existing PostgreSQL database using the dropdb command-line utility. This command is a wrapper for the SQL statement 'DROP DATABASE' and is typically run from your operating system's shell or command prompt.",
  "parameters": [
    {
      "flag": "-U, --username=<username>",
      "name": "Username",
      "description": "Specifies the PostgreSQL user to connect as. The user must be a superuser or the owner of the target database."
    },
    {
      "flag": "-h, --host=<hostname>",
      "name": "Host",
      "description": "The hostname of the machine on which the PostgreSQL server is running."
    },
    {
      "flag": "-p, --port=<port>",
      "name": "Port",
      "description": "The TCP port the server is listening on for connections."
    },
    {
      "flag": "-i, --interactive",
      "name": "Interactive Prompt",
      "description": "Issues a verification prompt before deleting the database to prevent accidental removal."
    },
    {
      "flag": "--if-exists",
      "name": "If Exists",
      "description": "Prevents an error from being thrown if the database does not exist. A notice is issued instead."
    },
    {
      "flag": "-e, --echo",
      "name": "Echo Command",
      "description": "Shows the underlying SQL command that dropdb generates and sends to the server."
    }
  ],
  "scenarios": [
    {
      "title": "Decommissioning a Staging Environment Database",
      "details": {
        "setup": "A project has reached the end of its lifecycle, and its dedicated staging database, 'project_x_staging', is no longer required on the development server. Keeping it consumes disk space and adds unnecessary clutter.",
        "goal": "To safely and permanently remove the 'project_x_staging' database from the PostgreSQL server to free up resources and maintain a clean server environment.",
        "outcome": "The 'project_x_staging' database is completely deleted. An attempt to connect to it will now fail, confirming its removal."
      },
      "example": {
        "command": "dropdb -U postgres -h dev-server.internal -p 5432 -i --if-exists project_x_staging",
        "explanation": "This command connects to the host 'dev-server.internal' as the 'postgres' user. The '--if-exists' flag ensures the script won't fail if the database was already removed. Most importantly, the '-i' (interactive) flag provides a critical safety check by asking for confirmation before permanently deleting the database."
      },
      "notes": [
        "This is a destructive and irreversible operation. Always verify the database name before proceeding.",
        "You cannot be connected to the database you are attempting to drop. The utility automatically connects to a maintenance database (like 'postgres') to perform the operation.",
        "If other users are connected to the target database, the command will fail unless the FORCE option is used."
      ]
    }
  ]
}