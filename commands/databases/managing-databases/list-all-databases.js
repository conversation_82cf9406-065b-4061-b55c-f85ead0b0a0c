export const command0 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "List All Databases"
  ],
  "command": "\\l",
  "description": "Lists all databases in the PostgreSQL cluster. The '\\l+' variant provides more detailed information, including database sizes.",
  "parameters": [
    {
      "flag": "\\l",
      "name": "List Databases",
      "description": "Provides a basic list of databases including their name, owner, and encoding."
    },
    {
      "flag": "\\l+",
      "name": "Detailed List",
      "description": "Provides an extended list that includes size, tablespace, and description."
    },
    {
      "flag": "\\l [pattern]",
      "name": "Pattern Match",
      "description": "Lists databases whose names match a specific pattern (e.g., \\l prod_*)."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Database Creation After Initial Setup",
      "details": {
        "setup": "You are a developer who has just run an automated deployment script to set up a new environment. The script was supposed to create three databases: 'users_db', 'products_db', and 'orders_db'.",
        "goal": "To quickly confirm that all three databases were created successfully and are owned by the correct role before proceeding with data migration.",
        "outcome": "The command displays a list of all databases in the cluster, allowing you to instantly verify the presence and ownership of 'users_db', 'products_db', and 'orders_db'."
      },
      "example": {
        "command": "\\l",
        "explanation": "Running the '\\l' command within psql provides an immediate, easy-to-read table of all databases, which is the fastest way to confirm the existence and basic properties of your newly created databases."
      },
      "notes": [
        "You will typically see default databases listed, such as 'postgres', 'template0', and 'template1'.",
        "To check for databases related to a specific project, you can use a pattern, like '\\l *_db'."
      ]
    },
    {
      "title": "Assessing Database Disk Usage for Capacity Planning",
      "details": {
        "setup": "You are a database administrator responsible for server maintenance. You need to conduct a quarterly review of disk space consumption to plan for storage upgrades and identify unexpectedly large databases.",
        "goal": "To get a detailed overview of every database on the server, with a primary focus on their individual sizes on disk.",
        "outcome": "The command outputs a comprehensive list of databases, including a human-readable size for each one (e.g., '15 GB'). This allows you to create a storage report and make informed decisions about resource allocation."
      },
      "example": {
        "command": "\\l+",
        "explanation": "The '+' variant is crucial here as it adds the 'Size' column to the output. This provides the exact information needed for capacity planning without having to run more complex queries against system catalogs."
      },
      "notes": [
        "The reported size is the total size of all objects within that database.",
        "For very large databases, this command might take a few moments to execute as it calculates the total size."
      ]
    }
  ]
}