export const command1 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "Create a Database (SQL)"
  ],
  "description": "Creates a new PostgreSQL database. This command allows for detailed configuration of the database's owner, character encoding, connection limits, and more upon creation.",
  "query_template": "CREATE DATABASE [database_name] [WITH [option] ...];",
  "components": [
    {
      "component": "DATABASE [name]",
      "name": "Database Name",
      "description": "The unique name for the new database."
    },
    {
      "component": "OWNER [role_name]",
      "name": "Owner",
      "description": "Assigns a specific role (user) as the owner of the new database."
    },
    {
      "component": "TEMPLATE [template_name]",
      "name": "Template",
      "description": "Specifies the template database from which to create the new one. Defaults to 'template1'."
    },
    {
      "component": "ENCODING [encoding]",
      "name": "Encoding",
      "description": "Sets the character set encoding for the database, such as 'UTF8'."
    },
    {
      "component": "TABLESPACE [tablespace_name]",
      "name": "Tablespace",
      "description": "Specifies the default tablespace where objects for this database will be stored."
    },
    {
      "component": "CONNECTION LIMIT [limit]",
      "name": "Connection Limit",
      "description": "Sets the maximum number of concurrent connections allowed to this database."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a Database for a New Web Application",
      "details": {
        "setup": "You are starting a new project and need a dedicated database for it. A specific user role, 'app_admin', has already been created to manage this application's data.",
        "goal": "To create a standard, UTF-8 encoded database named 'webapp_prod' and assign ownership to the 'app_admin' role for proper permission management.",
        "outcome": "The 'webapp_prod' database is created and is owned by 'app_admin', who now has full administrative rights over it."
      },
      "example": {
        "query": "CREATE DATABASE webapp_prod OWNER app_admin ENCODING 'UTF8';",
        "explanation": "This query creates the database and immediately assigns ownership to the `app_admin` role. This is a best practice for security, ensuring the application operates with its own dedicated permissions. Specifying `ENCODING 'UTF8'` is standard for modern applications that need to support a wide range of characters."
      },
      "notes": [
        "This command must be run by a superuser or a role with the `CREATEDB` privilege.",
        "You cannot be connected to the database you are trying to create. Connect to the default 'postgres' database or another existing database to run it."
      ]
    },
    {
      "title": "Creating a Pristine Database for Automated Testing",
      "details": {
        "setup": "Your continuous integration (CI) pipeline needs to run tests against a completely clean database, free from any custom objects or functions that may have been added to the default `template1` database.",
        "goal": "To create a new database named `test_suite_db` that is a perfect, unaltered copy of the original system database template.",
        "outcome": "A new database, `test_suite_db`, is created. It is guaranteed to be clean and contain only the standard objects predefined by your version of PostgreSQL."
      },
      "example": {
        "query": "CREATE DATABASE test_suite_db TEMPLATE template0;",
        "explanation": "Using `TEMPLATE template0` ensures the new database is a pristine copy. Unlike `template1`, `template0` is protected from modifications and serves as a factory-default template. This is crucial for creating a consistent and isolated environment for running automated tests."
      },
      "notes": [
        "To use `template0`, you must also specify a different encoding or locale if they are not the database cluster's default.",
        "Because `template0` is a read-only template, connections to it are not allowed."
      ]
    }
  ]
};