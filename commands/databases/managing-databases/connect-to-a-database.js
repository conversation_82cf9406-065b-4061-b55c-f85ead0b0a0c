export const command3 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "Connect to a Database"
  ],
  "command": "\\c [database_name] [user_name]",
  "description": "Switch the connection to a new database, optionally as a different user, within an active psql session. This is a psql meta-command.",
  "parameters": [
    {
      "flag": "[database_name]",
      "name": "Database Name",
      "description": "The name of the database you want to connect to."
    },
    {
      "flag": "[user_name]",
      "name": "User Name (Optional)",
      "description": "The user to connect as. If omitted, the current user is used."
    }
  ],
  "scenarios": [
    {
      "title": "Seamlessly Switch Between Development and Staging Databases",
      "details": {
        "setup": "You are a developer logged into psql on a shared server, currently connected to your personal development database, 'dev_user_db'. You have just pushed a new feature and now need to connect to the 'staging_db' as the 'qa_user' to verify its deployment.",
        "goal": "To switch your active connection from 'dev_user_db' to 'staging_db' as a different user without having to exit and restart your psql session.",
        "outcome": "Your psql session is now connected to the 'staging_db' as 'qa_user', and the prompt updates to reflect this change (e.g., 'staging_db=>'). You can immediately start running queries to validate the feature in the staging environment."
      },
      "example": {
        "command": "\\c staging_db qa_user",
        "explanation": "The `\\c` (or `\\connect`) meta-command allows you to change your database context instantly. By providing the database name ('staging_db') and the username ('qa_user'), you establish a new session under those credentials. This is far more efficient than logging out and back in, saving time and preserving your session history."
      },
      "notes": [
        "If you do not specify a user, psql will attempt to connect to the new database with the same user you are currently using.",
        "You can use the `\\l` command at any time to see a list of all available databases on the server.",
        "The connection will fail if the user (e.g., 'qa_user') does not have the necessary CONNECT privilege on the target database."
      ]
    }
  ]
}