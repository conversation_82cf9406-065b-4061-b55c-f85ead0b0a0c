export const command4 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "Create a Database (Command Line)"
  ],
  "command": "createdb [options] <database_name>",
  "description": "Creates a new PostgreSQL database directly from the operating system's command line. This utility is a wrapper around the SQL command CREATE DATABASE.",
  "parameters": [
    {
      "flag": "<database_name>",
      "name": "database name",
      "description": "The name of the database to create."
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect as (default: current OS user)."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Specifies the database server's hostname or IP address (default: local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Specifies the port the database server is listening on (default: 5432)."
    },
    {
      "flag": "-O",
      "name": "owner",
      "description": "Specifies the role name to be the owner of the new database."
    },
    {
      "flag": "-T",
      "name": "template",
      "description": "Specifies the template database from which to build the new one."
    },
    {
      "flag": "-E",
      "name": "encoding",
      "description": "Specifies the character set encoding for the new database (e.g., UTF8)."
    },
    {
      "flag": "-D",
      "name": "tablespace",
      "description": "Specifies the default tablespace for the database."
    },
    {
      "flag": "-l",
      "name": "locale",
      "description": "Specifies the locale for the new database (e.g., en_US.UTF-8)."
    }
  ],
  "scenarios": [
    {
      "title": "Creating a Local Database for a New Development Project",
      "details": {
        "setup": "A developer is starting a new web application on their local machine and needs a dedicated database for development and testing.",
        "goal": "To quickly create a new, standard database named 'webapp_dev' that is owned by the current logged-in user.",
        "outcome": "The 'webapp_dev' database is successfully created and is immediately available for connection and schema migration."
      },
      "example": {
        "command": "createdb webapp_dev",
        "explanation": "This is the most direct way to create a database. It assumes your current operating system username corresponds to a PostgreSQL role with the CREATEDB privilege. The new database will be owned by this same role."
      },
      "notes": [
        "If you encounter a 'role does not exist' error, you may need to either create a PostgreSQL role with the same name as your OS user or specify an existing role with the '-U' flag.",
        "This command must be run from your terminal, not inside a psql session."
      ]
    },
    {
      "title": "Setting Up a Production Database with a Specific Owner and Encoding",
      "details": {
        "setup": "A database administrator (DBA) is provisioning a new database for a production application that will serve international customers. For security, the database must be owned by a dedicated application role, not a superuser.",
        "goal": "To create a database named 'prod_analytics' with UTF8 encoding, owned by the 'analytics_user' role, by connecting as the 'postgres' superuser.",
        "outcome": "A new database 'prod_analytics' is created with robust character support and correct ownership, ready for the application's schema."
      },
      "example": {
        "command": "createdb -U postgres -O analytics_user -E UTF8 prod_analytics",
        "explanation": "This command connects as the 'postgres' superuser (-U postgres) to perform the administrative task. It explicitly sets the owner to 'analytics_user' (-O analytics_user) and specifies UTF8 encoding (-E UTF8) to ensure proper handling of international text data."
      },
      "notes": [
        "The role specified with the '-O' flag ('analytics_user' in this case) must already exist before running this command. You can create it using 'CREATE ROLE analytics_user;'.",
        "Using a dedicated, non-superuser owner for application databases is a critical security best practice."
      ]
    }
  ]
};