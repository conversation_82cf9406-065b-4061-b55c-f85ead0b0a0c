export const command2 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Databases",
    "Delete a Database (SQL)"
  ],
  "description": "Permanently deletes a database and all of its contents, including tables, indexes, and data. This action is irreversible. You must be connected to a different database (e.g., 'postgres') to execute this command against a target database.",
  "query_template": "DROP DATABASE [IF EXISTS] [database_name] [WITH (FORCE)];",
  "components": [
    {
      "component": "DROP DATABASE",
      "name": "Statement",
      "description": "The SQL statement to initiate the deletion of a database."
    },
    {
      "component": "IF EXISTS",
      "name": "Conditional Clause",
      "description": "An optional clause that prevents an error from being thrown if the database does not exist. This is very useful in scripts."
    },
    {
      "component": "WITH (FORCE)",
      "name": "Forced Drop Option",
      "description": "An optional clause (available in PostgreSQL 13+) that terminates any existing connections to the target database before dropping it."
    }
  ],
  "scenarios": [
    {
      "title": "Cleaning Up an Obsolete Development Database",
      "details": {
        "setup": "A developer was working on a feature in a dedicated database named `feature_x_test`. The feature has now been merged into the main application, and this temporary database is no longer needed.",
        "goal": "To permanently remove the `feature_x_test` database to free up disk space and keep the database server organized.",
        "outcome": "The `feature_x_test` database and all its data are completely removed from the server."
      },
      "example": {
        "query": "DROP DATABASE feature_x_test;",
        "explanation": "This command directly targets and deletes the specified database. You must run this from another database, such as the default `postgres` database. The command will fail if anyone is currently connected to `feature_x_test`."
      },
      "notes": [
        "This is an irreversible operation. Ensure you have a backup if there is any chance the data might be needed later.",
        "To run this, connect to a different database first, for example: `\\c postgres` in `psql`."
      ]
    },
    {
      "title": "Automating Environment Cleanup with a Script",
      "details": {
        "setup": "You are writing a shell script to reset a testing environment. The script needs to delete the database `ci_test_db` at the start of its run. The script might fail if a previous run failed and the database doesn't exist.",
        "goal": "To make the cleanup script resilient by ensuring it doesn't stop with an error if the `ci_test_db` database is already gone.",
        "outcome": "The script successfully removes the `ci_test_db` if it exists, and continues without error if it doesn't."
      },
      "example": {
        "query": "DROP DATABASE IF EXISTS ci_test_db;",
        "explanation": "The `IF EXISTS` clause is key for idempotent scripts. It tells PostgreSQL to only proceed with the deletion if the database is found, otherwise do nothing and report success."
      },
      "notes": [
        "Using `IF EXISTS` is a best practice for any automated or repeatable database maintenance scripts."
      ]
    },
    {
        "title": "Forcibly Dropping a Database with Lingering Connections",
        "details": {
          "setup": "You are trying to drop the `stale_app_db`, but the command fails with an error: 'database is being accessed by other users'. This is often caused by an application server holding idle connections that you can't easily terminate.",
          "goal": "To drop the database immediately by forcing all connections to it to be terminated first.",
          "outcome": "All connections to `stale_app_db` are severed, and the database is subsequently dropped."
        },
        "example": {
          "query": "DROP DATABASE stale_app_db WITH (FORCE);",
          "explanation": "Available in PostgreSQL 13+, the `WITH (FORCE)` option is a powerful shortcut that first runs `pg_terminate_backend` on all processes connected to the target database before proceeding with the drop."
        },
        "notes": [
          "Use this with caution, especially in production, as it will abruptly end active sessions.",
          "For versions before PostgreSQL 13, you would need to manually query the `pg_stat_activity` table and use the `pg_terminate_backend(pid)` function for each connection."
        ]
      }
  ]
};