import { command0 } from './managing-databases/list-all-databases.js';
import { command1 } from './managing-databases/create-a-database-sql.js';
import { command2 } from './managing-databases/delete-a-database-sql.js';
import { command3 } from './managing-databases/connect-to-a-database.js';
import { command4 } from './managing-databases/create-a-database-command-line.js';
import { command5 } from './managing-databases/delete-a-database-command-line.js';
import { command6 } from './managing-users-roles/list-users-roles.js';
import { command7 } from './managing-users-roles/create-a-user-role-sql.js';
import { command8 } from './managing-users-roles/create-a-user-with-specific-privileges.js';
import { command9 } from './managing-users-roles/alter-a-user-role.js';
import { command10 } from './managing-users-roles/delete-a-user-role.js';
import { command11 } from './managing-users-roles/create-a-user-command-line.js';
import { command12 } from './managing-users-roles/delete-a-user-command-line.js';
import { command13 } from './managing-privileges/grant-privileges.js';
import { command14 } from './managing-privileges/grant-all-privileges-on-a-table.js';
import { command15 } from './managing-privileges/grant-usage-on-a-schema.js';
import { command16 } from './managing-privileges/grant-select-on-all-tables-in-schema.js';
import { command17 } from './managing-privileges/revoke-privileges.js';
import { command18 } from './advanced-database-management/alter-database-settings.js';
import { command19 } from './advanced-database-management/database-size-and-statistics.js';
import { command20 } from './advanced-user-management/role-inheritance-and-groups.js';
import { command21 } from './advanced-user-management/row-level-security.js';
import { command22 } from './advanced-user-management/create-rls-policy.js';
import { command23 } from './tablespace-management/list-tablespaces.js';
import { command24 } from './tablespace-management/create-tablespace.js';
import { command25 } from './tablespace-management/move-table-to-tablespace.js';
import { command26 } from './connection-management/show-current-connections.js';
import { command27 } from './connection-management/kill-connection.js';

export const databaseCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15,
  command16,
  command17,
  command18,
  command19,
  command20,
  command21,
  command22,
  command23,
  command24,
  command25,
  command26,
  command27
];

export function validateDatabaseCommands() {
  return databaseCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Managing Databases and Roles";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
