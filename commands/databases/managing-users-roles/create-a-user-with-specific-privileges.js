export const command8 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Create a User with Specific Privileges"
  ],
  "description": "Creates a new user (role) with a password and grants them specific system-level privileges, such as the ability to create databases or other roles.",
  "query_template": "CREATE USER [username] WITH PASSWORD '[password]' [privileges];",
  "components": [
    {
      "component": "CREATE USER [username]",
      "name": "User Creation",
      "description": "Defines the name for the new user (technically a 'role' in PostgreSQL)."
    },
    {
      "component": "WITH PASSWORD '[password]'",
      "name": "Authentication",
      "description": "Assigns a password to the user, ensuring they must authenticate to connect."
    },
    {
      "component": "CREATEDB | NOCREATEDB",
      "name": "Database Creation Privilege",
      "description": "Specifies whether the user is allowed to create new databases."
    },
    {
      "component": "SUPERUSER | NOSUPERUSER",
      "name": "Superuser Status",
      "description": "Grants full superuser privileges. This should be used with extreme caution."
    }
  ],
  "scenarios": [
    {
      "title": "Onboarding a New Developer with Limited Privileges",
      "details": {
        "setup": "A new developer, <PERSON>, has joined your team. She needs access to the development PostgreSQL server to create her own sandbox databases for testing features.",
        "goal": "To create a user account for <PERSON> that is password-protected and allows her to create databases, but does not grant her full superuser permissions, adhering to the principle of least privilege.",
        "outcome": "The user 'alice' is created. She can connect to the server with her password and successfully run `CREATE DATABASE alice_sandbox;`, but she cannot modify system settings or access data she hasn't been granted permissions for."
      },
      "example": {
        "query": "CREATE USER alice WITH PASSWORD 'a_very_strong_and_secure_password' CREATEDB;",
        "explanation": "This query creates the user 'alice', requires her to use a password for login (`WITH PASSWORD`), and grants her exactly the permission she needs (`CREATEDB`) without assigning dangerous `SUPERUSER` rights. This is a secure way to provide necessary access."
      },
      "notes": [
        "In modern PostgreSQL, `CREATE USER` is an alias for `CREATE ROLE`. The only difference is that `CREATE USER` assumes the `LOGIN` privilege by default, which is almost always what you want for a user account.",
        "For production environments, always grant the most specific privileges necessary. Avoid using `SUPERUSER` for application or regular user accounts.",
        "You can grant other privileges in the same statement, such as `CREATEROLE` (ability to create other roles) or `REPLICATION`."
      ]
    }
  ]
};