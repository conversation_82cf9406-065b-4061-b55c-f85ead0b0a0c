export const command11 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Create a User (Command Line)"
  ],
  "command": "createuser --interactive",
  "description": "Create a new user using the command-line utility with an interactive prompt. Use --pwprompt to be prompted for a password.",
  "parameters": [
    {
      "flag": "--interactive",
      "name": "interactive",
      "description": "Prompt for user name and attributes interactively"
    },
    {
      "flag": "--pwprompt",
      "name": "password prompt",
      "description": "Prompt for a password for the new user"
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user to connect as (must have CREATEROLE privilege)"
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (default: local socket)"
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)"
    },
    {
      "flag": "-s",
      "name": "superuser",
      "description": "Make the new user a superuser"
    },
    {
      "flag": "-S",
      "name": "no-superuser",
      "description": "Make the new user not a superuser (default)"
    },
    {
      "flag": "-d",
      "name": "createdb",
      "description": "Allow the new user to create databases"
    },
    {
      "flag": "-D",
      "name": "no-createdb",
      "description": "Do not allow the new user to create databases (default)"
    },
    {
      "flag": "-r",
      "name": "createrole",
      "description": "Allow the new user to create roles"
    },
    {
      "flag": "-R",
      "name": "no-createrole",
      "description": "Do not allow the new user to create roles (default)"
    }
  ],
  "scenarios": [
    {
      "title": "Onboarding a New Read-Only Application User",
      "details": {
        "setup": "A new reporting and analytics service needs to connect to your production database. For security, this service must have its own dedicated user that can only read data, not modify it.",
        "goal": "To create a new user named `reporting_svc` and securely set its password, without granting any default privileges beyond the ability to connect.",
        "outcome": "A new role `reporting_svc` is created with a password. This user can now be granted specific `SELECT` permissions on the necessary tables and schemas."
      },
      "example": {
        "command": "createuser --interactive --pwprompt",
        "explanation": "Using `--interactive` guides you through the process, ensuring you don't accidentally grant unnecessary privileges. You will be prompted to enter the username (`reporting_svc`) and then asked about superuser, database creation, and role creation rights (answer 'n' to all). The `--pwprompt` flag ensures the user is created with a secure password from the start."
      },
      "notes": [
        "This command only creates the user; it does not grant permissions to any database objects. You must follow up with `GRANT CONNECT ON DATABASE ...` and `GRANT SELECT ON TABLE ...` commands.",
        "This command must be run on the command line of the database server by a PostgreSQL role with `CREATEROLE` privileges (like the default `postgres` user)."
      ]
    },
    {
      "title": "Setting Up a Developer with Database Creation Privileges",
      "details": {
        "setup": "A new developer, Jane, has joined the team. She needs access to the shared development server and the ability to create her own sandbox databases for testing, but she should not have superuser access.",
        "goal": "To create a user `jane_dev` that can log in and create new databases.",
        "outcome": "The user `jane_dev` is created. She can now connect with `psql` and run commands like `CREATE DATABASE janes_project_db;`."
      },
      "example": {
        "command": "createuser --interactive --pwprompt --createdb jane_dev",
        "explanation": "This command specifies the username directly. During the interactive session, the question 'Shall the new role be allowed to create databases?' will now default to 'yes' because of the `--createdb` flag. This grants the specific privilege needed without assigning full superuser permissions, adhering to the principle of least privilege."
      },
      "notes": [
        "A user with `CREATEDB` privilege can create, alter, and drop their own databases. They cannot access databases owned by other users unless explicitly granted permission.",
        "This is a much safer alternative to granting `SUPERUSER` status to developers."
      ]
    }
  ]
}