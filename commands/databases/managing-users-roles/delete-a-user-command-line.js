export const command12 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Delete a User (Command Line)"
  ],
  "command": "dropuser [connection-options] <rolename>",
  "description": "Deletes an existing PostgreSQL role using a command-line utility. This is a wrapper around the SQL command `DROP ROLE`.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "<rolename>",
      "name": "Role Name",
      "description": "The name of the role to be deleted."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Specifies the host name of the machine on which the server is running."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Specifies the TCP port on which the server is listening for connections."
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "The user to connect as (not the user to be dropped)."
    },
    {
      "flag": "-i",
      "name": "interactive",
      "description": "Prompt for confirmation before actually deleting the role."
    }
  ],
  "scenarios": [
    {
      "title": "Off-boarding a Developer and Revoking Database Access",
      "details": {
        "setup": "A developer, 'alex', has left the company. For security compliance, their individual database access role must be removed from the production server immediately. The role 'alex' does not own any database objects.",
        "goal": "To securely and completely delete the PostgreSQL role for 'alex', ensuring it can no longer be used to access the database system.",
        "outcome": "The role 'alex' is successfully deleted from the PostgreSQL cluster. Any subsequent login attempts with this role will be rejected."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux / macOS (as postgres user)",
            "command": "sudo -u postgres dropuser alex",
            "explanation": "This command uses `sudo` to run the `dropuser` utility as the default `postgres` superuser, a common and secure practice on Linux-based systems. This avoids needing to enter a password for the administrative user."
          },
          {
            "platform": "Windows / Universal",
            "command": "dropuser -U postgres -h db.example.com -p 5432 alex",
            "explanation": "This is a universal method that works on any platform. It specifies the administrative user (`-U postgres`), the server hostname (`-h`), and port (`-p`) to connect to. It will securely prompt for the 'postgres' user's password to authorize the deletion."
          }
        ]
      },
      "notes": [
        "This command will fail if the user still owns any database objects (e.g., tables, schemas). You must first reassign or drop their owned objects.",
        "To reassign ownership, connect to the database and run: `REASSIGN OWNED BY alex TO new_owner;`",
        "You cannot drop a role that is currently connected to the database. Ensure all active sessions for the user are terminated first."
      ]
    }
  ]
};