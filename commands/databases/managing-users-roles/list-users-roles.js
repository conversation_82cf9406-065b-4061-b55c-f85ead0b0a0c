export const command6 = {
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "List Users/Roles"
  ],
  "command": "\\du [pattern]",
  "description": "Lists all roles in the PostgreSQL cluster. Can be filtered with an optional pattern. Use \\du+ for more detailed information, including role descriptions.",
  "scenarios": [
    {
      "title": "Performing a Regular Security Audit of User Roles",
      "details": {
        "setup": "As a database administrator, you are required to perform a quarterly security audit to ensure that only authorized users have access and that permissions are correctly assigned.",
        "goal": "Generate a comprehensive list of all roles, their attributes (e.g., Superuser, Login), and their group memberships to identify any unauthorized or overly-privileged accounts.",
        "outcome": "A detailed list of all roles is displayed, allowing you to review each one against security policies and identify accounts that need to be disabled, removed, or have their permissions adjusted."
      },
      "example": {
        "command": "\\du+",
        "explanation": "Using the `+` modifier provides a more detailed view, including the description for each role. This is crucial during an audit to understand the purpose of a role, especially for non-human or service accounts."
      },
      "notes": [
        "Pay close attention to the 'Attributes' column to quickly spot roles with 'Superuser', 'Create role', or 'Create DB' privileges.",
        "Regularly auditing roles is a fundamental security practice to prevent 'privilege creep,' where users accumulate permissions over time.",
        "For scripted audits, you can get similar information by querying the `pg_roles` system catalog directly."
      ]
    },
    {
      "title": "Troubleshooting a User's Inability to Log In",
      "details": {
        "setup": "A new developer, 'sara', reports that she is receiving an authentication error and cannot connect to the database, even though she has the correct password.",
        "goal": "Quickly verify that the user role 'sara' exists and confirm whether it has the necessary LOGIN privilege.",
        "outcome": "The command output shows that the role 'sara' exists but is missing the 'Login' attribute, identifying the root cause of the problem."
      },
      "example": {
        "command": "\\du sara",
        "explanation": "By specifying the role name `sara` as a pattern, the list is filtered to show only that role. This is a highly efficient way to check a specific user's status on a server with hundreds of roles."
      },
      "notes": [
        "If a role is missing the `LOGIN` privilege, you can grant it using the command: `ALTER ROLE sara LOGIN;`.",
        "This command must be run from within the `psql` interactive terminal.",
        "If the role has the `Login` attribute but the user still can't connect, the next step would be to check the `pg_hba.conf` file for authentication rules."
      ]
    }
  ]
};