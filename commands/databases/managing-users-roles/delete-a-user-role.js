export const command10 = {
  // --- Using the 'query' type ---
  "type": "query",

  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Delete a User/Role"
  ],
  "description": "Permanently removes a user (role) from the PostgreSQL database cluster. This action cannot be undone.",

  // --- Query-specific fields ---
  "query_template": "DROP USER [username];",
  "components": [
    {
      "component": "DROP USER",
      "name": "Drop User Command",
      "description": "The SQL command to delete a role. `DROP ROLE` is an alias for `DROP USER`."
    },
    {
      "component": "IF EXISTS",
      "name": "Conditional Deletion (Optional)",
      "description": "Add `IF EXISTS` (e.g., `DROP USER IF EXISTS [username];`) to prevent an error if the user does not exist. This is useful in scripts."
    }
  ],

  "scenarios": [
    {
      "title": "Securely Offboarding a Departed Employee",
      "details": {
        "setup": "A developer, <PERSON>, with the username `jdoe` has left the company. Her database user account must be removed immediately to comply with security policies.",
        "goal": "To completely and permanently revoke all access for the `jdoe` user by deleting the role.",
        "outcome": "The `jdoe` role is removed from the system, and any attempt to log in with those credentials will fail."
      },
      "example": {
        "query": "DROP USER jdoe;",
        "explanation": "This command permanently deletes the user `jdoe` from the PostgreSQL cluster. This is the final step in the offboarding process after ensuring the user's objects have been dealt with."
      },
      "notes": [
        "CRITICAL: This command will fail if the user still owns any database objects (like tables, views, or schemas) or has privileges on objects.",
        "See the next scenario for the correct way to handle object ownership before dropping a user."
      ]
    },
    {
      "title": "Handling Object Ownership Before Deleting a User",
      "details": {
        "setup": "You tried to run `DROP USER jdoe;`, but received an error: 'role \"jdoe\" cannot be dropped because some objects depend on it'. This is a safety feature to prevent orphaned objects.",
        "goal": "To reassign ownership of all objects owned by `jdoe` to another user (e.g., a generic `admin_role`) so that `jdoe` can be safely deleted.",
        "outcome": "All of Jane's former objects are now owned by `admin_role`, and the `jdoe` user is successfully deleted."
      },
      "example": {
        // This is a sequence of commands, which is highly practical
        "query": "REASSIGN OWNED BY jdoe TO admin_role;\nDROP USER jdoe;",
        "explanation": "First, `REASSIGN OWNED` transfers ownership of all database objects from `jdoe` to `admin_role`. Once `jdoe` no longer owns anything, the `DROP USER` command can execute successfully."
      },
      "notes": [
        "You must run `REASSIGN OWNED` inside every database where the user might own objects.",
        "Alternatively, you can use `DROP OWNED BY jdoe;` to delete all objects owned by the user, but this is destructive and should be used with extreme caution."
      ]
    }
  ]
};