export const command9 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Alter a User/Role"
  ],
  "description": "Modifies the attributes of a PostgreSQL user/role. This is a fundamental command for managing permissions, passwords, and connection rights.",
  "query_template": "ALTER USER <user_name> WITH [OPTION];",
  "components": [
    {
      "component": "ALTER USER user_name",
      "name": "Target User",
      "description": "Specifies the user account you want to modify."
    },
    {
      "component": "WITH [OPTION]",
      "name": "Attribute Option",
      "description": "The specific attribute to change. Common options include SUPERUSER, NOSUPERUSER, CREATEDB, NOCREATEDB, LOGIN, NOLOGIN, and PASSWORD."
    }
  ],
  "scenarios": [
    {
      "title": "Granting Superuser Rights for a Critical Administrative Task",
      "details": {
        "setup": "A trusted database administrator, 'admin_jane', needs to perform a system-wide update that requires the highest level of privileges, such as modifying system catalogs.",
        "goal": "To temporarily elevate 'admin_jane' to a superuser to allow her to complete the task.",
        "outcome": "The user 'admin_jane' now has superuser privileges and can perform any action within the database cluster."
      },
      "example": {
        "query": "ALTER USER admin_jane WITH SUPERUSER;",
        "explanation": "This query grants the 'SUPERUSER' attribute to the 'admin_jane' role. This is a very powerful privilege and should only be granted when absolutely necessary."
      },
      "notes": [
        "It is a critical security best practice to revoke superuser privileges as soon as the task is complete using `ALTER USER admin_jane WITH NOSUPERUSER;`."
      ]
    },
    {
      "title": "Resetting a Password for an Application Service Account",
      "details": {
        "setup": "As part of a security audit, you are required to rotate the password for the 'reporting_app' user, which is used by a business intelligence tool to connect to the database.",
        "goal": "To update the password for the 'reporting_app' user to a new, secure value without any downtime.",
        "outcome": "The password for 'reporting_app' is updated, and the BI tool can be reconfigured with the new credentials to restore its connection."
      },
      "example": {
        "query": "ALTER USER reporting_app WITH PASSWORD 'n3w_s3cur3_p@ssw0rd!';",
        "explanation": "The `WITH PASSWORD` clause sets a new password for the specified user. The password must be enclosed in single quotes."
      },
      "notes": [
        "For applications, consider using connection methods that don't require hardcoding passwords, such as certificate authentication or external authentication providers."
      ]
    },
    {
      "title": "Disabling a User Account Without Deleting It",
      "details": {
        "setup": "An employee, John, has left the company. You must immediately revoke his database access, but you need to preserve his user account and its owned objects for auditing.",
        "goal": "To prevent the user 'john_doe' from being able to log into the database.",
        "outcome": "The 'john_doe' user account still exists, but any new login attempts from this user will be rejected."
      },
      "example": {
        "query": "ALTER USER john_doe WITH NOLOGIN;",
        "explanation": "The `NOLOGIN` attribute is a fast and reversible way to disable an account. The role is preserved, but it cannot be used to initiate a database session."
      },
      "notes": [
        "To re-enable login for the user later, you can run `ALTER USER john_doe WITH LOGIN;`."
      ]
    }
  ]
};