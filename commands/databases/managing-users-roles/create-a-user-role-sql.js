export const command7 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Users/Roles",
    "Create a User/Role (SQL)"
  ],
  "description": "Creates a new role, which can be a user or a group. The `CREATE USER` command is an alias for `CREATE ROLE` but it automatically grants the `LOGIN` privilege.",
  "query_template": "CREATE USER username WITH [OPTION ...];",
  "components": [
    {
      "component": "CREATE USER username",
      "name": "User Creation",
      "description": "Defines the name of the new role. Usernames are subject to standard SQL identifier rules."
    },
    {
      "component": "WITH PASSWORD 'password'",
      "name": "Password Authentication",
      "description": "Assigns a password to the user. It is highly recommended to use a long and complex password."
    },
    {
      "component": "WITH [NO]SUPERUSER",
      "name": "Superuser Status",
      "description": "Determines if the user has superuser privileges, which bypass all permission checks. This should be granted with extreme caution."
    },
    {
      "component": "WITH [NO]CREATEDB",
      "name": "Database Creation Privilege",
      "description": "Allows the user to create new databases."
    },
    {
      "component": "WITH [NO]CREATEROLE",
      "name": "Role Creation Privilege",
      "description": "Allows the user to create, alter, and drop other roles."
    }
  ],
  "scenarios": [
    {
      "title": "Creating a Limited-Privilege User for a Web Application",
      "details": {
        "setup": "You are deploying a new Node.js application that needs to connect to the 'webapp_prod' database. For security, this user must only have the ability to connect and perform basic data operations.",
        "goal": "To create a dedicated user named 'webapp_user' that can log in but has no other privileges by default. Permissions on specific tables will be granted separately.",
        "outcome": "The 'webapp_user' role is created and can be used in your application's connection string, but it cannot perform any actions until permissions are explicitly granted."
      },
      "example": {
        "query": "CREATE USER webapp_user WITH PASSWORD 'a_very_strong_and_complex_password_123!';",
        "explanation": "This command creates a user with a password and the ability to log in (`LOGIN` is the default for `CREATE USER`). It adheres to the principle of least privilege. The next step would be to grant specific permissions, e.g., `GRANT SELECT, INSERT, UPDATE ON articles TO webapp_user;`."
      },
      "notes": [
        "Never use this user for database administration tasks.",
        "Store the password securely in a secrets manager or environment variable, not in your application's source code."
      ]
    },
    {
      "title": "Creating a Database Administrator Role",
      "details": {
        "setup": "A new developer, Jane, is joining the team. Her responsibilities include managing database schemas and creating roles for new services.",
        "goal": "To create a user for Jane that allows her to create new databases and manage other roles, but without granting full superuser access.",
        "outcome": "The user 'jane_doe' is created and can now manage databases and non-superuser roles."
      },
      "example": {
        "query": "CREATE ROLE jane_doe WITH LOGIN PASSWORD 'another_secure_password' CREATEDB CREATEROLE;",
        "explanation": "Here we use `CREATE ROLE` with `LOGIN` to show it's equivalent to `CREATE USER`. By explicitly granting `CREATEDB` (can create databases) and `CREATEROLE` (can manage other roles), we give Jane the administrative privileges she needs for her job without the unnecessary risks of `SUPERUSER`."
      },
      "notes": [
        "A role with `CREATEROLE` cannot drop superuser roles.",
        "Regularly audit roles with high privileges to ensure they are still necessary."
      ]
    }
  ]
};