export const command13 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Privileges",
    "Grant Privileges"
  ],
  "description": "Grants specific permissions (e.g., connect, select, insert) on a database object (like a database, table, or schema) to a user or role. It is the fundamental command for managing database security.",
  "query_template": "GRANT [privilege] ON [object_type] [object_name] TO [role_name];",
  "components": [
    {
      "component": "GRANT [privilege]",
      "name": "Privilege",
      "description": "The permission to be granted. Common privileges include `CONNECT`, `SELECT`, `INSERT`, `UPDATE`, `DELETE`, `USAGE`, and `ALL PRIVILEGES`."
    },
    {
      "component": "ON [object_type] [object_name]",
      "name": "Target Object",
      "description": "The database object the permission applies to. Examples: `DATABASE mydatabase`, `TABLE users`, `SCHEMA public`."
    },
    {
      "component": "TO [role_name]",
      "name": "Recipient",
      "description": "The user or role that will receive the specified privilege."
    }
  ],
  "scenarios": [
    {
      "title": "Granting a New User Basic Database Connection Access",
      "details": {
        "setup": "A new user, `app_user`, has been created for your web application. When the application tries to connect to the `prod_db` database with this user, it fails with a 'permission denied for database prod_db' error.",
        "goal": "To allow the `app_user` to establish a connection to the `prod_db` database without granting any data access permissions yet.",
        "outcome": "The `app_user` can now successfully connect to the `prod_db` database. However, they still cannot read or write data until further permissions are granted on specific tables."
      },
      "example": {
        "query": "GRANT CONNECT ON DATABASE prod_db TO app_user;",
        "explanation": "This query grants only the essential `CONNECT` privilege on a specific database to the `app_user` role. It is a critical first step for setting up user access, following the principle of least privilege."
      },
      "notes": [
        "This command does not grant permission to read or write data. `SELECT`, `INSERT`, `UPDATE`, etc., must be granted separately on tables or schemas."
      ]
    },
    {
      "title": "Setting Up Read-Only Access for a Reporting Tool",
      "details": {
        "setup": "The analytics team needs to connect a business intelligence (BI) tool to the database to build dashboards. Their user, `bi_reporter`, must be able to read all customer and sales data but must never be allowed to modify it.",
        "goal": "To grant read-only (`SELECT`) access on the `customers` and `invoices` tables to the `bi_reporter` user.",
        "outcome": "The `bi_reporter` user can now run `SELECT` queries against the `customers` and `invoices` tables but will receive a 'permission denied' error if they attempt to `INSERT`, `UPDATE`, or `DELETE` any records."
      },
      "example": {
        "query": "GRANT SELECT ON TABLE customers, invoices TO bi_reporter;",
        "explanation": "The `GRANT SELECT` statement provides read-only access. By specifying `ON TABLE` and listing the exact tables, you precisely limit the scope of the user's permissions, which is a core security best practice."
      },
      "notes": [
        "For convenience, you can grant permissions on all tables within a schema using: `GRANT SELECT ON ALL TABLES IN SCHEMA public TO bi_reporter;`."
      ]
    }
  ]
};