export const command14 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Privileges",
    "Grant All Privileges on a Table"
  ],
  "description": "Grants a user or role all standard privileges (SELECT, INSERT, UPDATE, DELETE, etc.) on a specific table. This is a powerful command and should be used with care.",
  "query_template": "GRANT ALL PRIVILEGES ON TABLE [table_name] TO [role_name];",
  "components": [
    {
      "component": "GRANT ALL PRIVILEGES",
      "name": "Grant All Action",
      "description": "Specifies that you are granting all available permissions for the given object type."
    },
    {
      "component": "ON TABLE [table_name]",
      "name": "Target Object",
      "description": "The specific table to which the privileges will apply."
    },
    {
      "component": "TO [role_name]",
      "name": "Recipient Role",
      "description": "The user or role that will receive the privileges."
    }
  ],
  "scenarios": [
    {
      "title": "Configuring Full Access for an Application Service Account",
      "details": {
        "setup": "A new web application is being deployed, and it requires a dedicated database user named `webapp_svc` to manage all data within the `customer_orders` table.",
        "goal": "To provide the `webapp_svc` user with complete control to read, write, update, and delete records in the `customer_orders` table, without granting it wider superuser permissions.",
        "outcome": "The `webapp_svc` user can perform all data manipulation operations on the `customer_orders` table, but cannot alter its structure or access other tables in the database."
      },
      "example": {
        "query": "GRANT ALL PRIVILEGES ON TABLE customer_orders TO webapp_svc;",
        "explanation": "This query grants all standard permissions for the `customer_orders` table to the `webapp_svc` role. This is the most direct way to give a service account full operational control over a table it is responsible for managing."
      },
      "notes": [
        "Best Practice: Adhere to the Principle of Least Privilege. If a user only needs to read data, use `GRANT SELECT` instead of `GRANT ALL`.",
        "This command does not grant ownership or the ability to `DROP` or `ALTER` the table.",
        "For permissions to apply automatically to future tables created in a schema, you must use `ALTER DEFAULT PRIVILEGES`."
      ]
    },
    {
      "title": "Granting Specific Privileges Instead of All",
      "details": {
        "setup": "You have a reporting tool with a dedicated user, `reporter_role`, that only needs to read data from the `sales_data` table.",
        "goal": "To grant only the necessary read-only permission to `reporter_role` to prevent accidental data modification.",
        "outcome": "The `reporter_role` can successfully run `SELECT` queries on the `sales_data` table but will receive a 'permission denied' error if it attempts to `INSERT`, `UPDATE`, or `DELETE`."
      },
      "example": {
        "query": "GRANT SELECT ON TABLE sales_data TO reporter_role;",
        "explanation": "Instead of `ALL PRIVILEGES`, this query explicitly grants only the `SELECT` permission. This is a critical security practice to limit the potential impact of a compromised account or application bug."
      },
      "notes": [
        "You can grant multiple specific privileges at once, for example: `GRANT SELECT, INSERT ON ...`."
      ]
    }
  ]
};