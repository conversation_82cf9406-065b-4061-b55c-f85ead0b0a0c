export const command16 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Privileges",
    "Grant Select on All Tables in Schema"
  ],
  "description": "Grants read-only (SELECT) privileges to a user or role for all tables that currently exist within a specified schema. This command is useful for quickly setting up read-only access for reporting or analytics users. Note that this does not apply to tables created in the future.",
  "query_template": "GRANT SELECT ON ALL TABLES IN SCHEMA [schema_name] TO [role_name];",
  "components": [
    {
      "component": "GRANT SELECT",
      "name": "Privilege Type",
      "description": "Specifies the type of permission being granted. Other common privileges include INSERT, UPDATE, and DELETE."
    },
    {
      "component": "ON ALL TABLES IN SCHEMA [schema_name]",
      "name": "Scope",
      "description": "Defines the scope of the grant, targeting all existing tables within the given schema (e.g., 'public', 'sales')."
    },
    {
      "component": "TO [role_name]",
      "name": "Recipient",
      "description": "The user or role that will receive the specified privileges."
    }
  ],
  "scenarios": [
    {
      "title": "Granting Read-Only Access to a Business Intelligence (BI) User",
      "details": {
        "setup": "You have a database with a 'sales' schema containing tables like 'orders' and 'customers'. A new data analyst has been hired, and they need to connect a BI tool (like Tableau or Power BI) to query this data for reporting. A read-only user named 'analytics_user' has been created.",
        "goal": "To give 'analytics_user' the ability to read from all tables within the 'sales' schema without granting any permissions to modify data (insert, update, or delete).",
        "outcome": "The 'analytics_user' can successfully run SELECT queries on all existing tables in the 'sales' schema. Any attempt to run an UPDATE or DELETE command will be denied by PostgreSQL."
      },
      "example": {
        "query": "GRANT SELECT ON ALL TABLES IN SCHEMA sales TO analytics_user;",
        "explanation": "This command instantly grants the 'SELECT' privilege on every table currently in the 'sales' schema to the 'analytics_user' role. It's the most efficient way to provide blanket read-only access for analytics and reporting purposes."
      },
      "notes": [
        "Crucially, this grant only applies to tables that exist at the time the command is run. It will not apply to new tables created in the 'sales' schema tomorrow.",
        "To ensure future tables also get these permissions automatically, you must use `ALTER DEFAULT PRIVILEGES`. For example: `ALTER DEFAULT PRIVILEGES IN SCHEMA sales GRANT SELECT ON TABLES TO analytics_user;`",
        "You can grant multiple privileges at once, such as `GRANT SELECT, INSERT ON ALL TABLES...` if needed."
      ]
    }
  ]
};