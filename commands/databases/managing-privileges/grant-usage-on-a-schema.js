export const command15 = {
  // --- Identifying this as a query ---
  "type": "query",

  "path": [
    "Managing Databases and Roles",
    "Managing Privileges",
    "Grant Usage on a Schema"
  ],
  "description": "Allows a user or role to access objects within a specific schema. This privilege is a prerequisite for interacting with any objects (like tables or functions) inside that schema.",

  // --- Query-specific structure ---
  "query_template": "GRANT USAGE ON SCHEMA [schema_name] TO [user_or_role];",
  "components": [
    {
      "component": "GRANT USAGE",
      "name": "Grant Usage Privilege",
      "description": "Specifies that you are granting the right to access objects within a schema, without granting rights to the objects themselves."
    },
    {
      "component": "ON SCHEMA [schema_name]",
      "name": "Target Schema",
      "description": "The name of the schema on which the privilege is being granted."
    },
    {
      "component": "TO [user_or_role]",
      "name": "Recipient",
      "description": "The user or role that will receive the privilege."
    }
  ],

  // --- Detailed Scenario ---
  "scenarios": [
    {
      "title": "Setting Up a Read-Only Role for a Reporting Team",
      "details": {
        "setup": "A new role, `reporting_analysts`, has been created for the BI team. You have granted `CONNECT` on the database and `SELECT` on all tables in the `sales_data` schema, but the analysts report they cannot see or query any of the tables.",
        "goal": "To provide the `reporting_analysts` role with the minimum required permissions to see and query the tables within the `sales_data` schema.",
        "outcome": "After executing the command, members of the `reporting_analysts` role can successfully connect to the database and run `SELECT` queries on the tables inside the `sales_data` schema."
      },
      "example": {
        "query": "GRANT USAGE ON SCHEMA sales_data TO reporting_analysts;",
        "explanation": "This query grants the 'key' to the `sales_data` schema. Without `USAGE`, the role cannot even 'see' inside the schema, which is why the previous `GRANT SELECT` on tables was ineffective. `USAGE` on the schema must be granted before table-level permissions can be used."
      },
      "notes": [
        "Best Practice: Always grant privileges to roles rather than individual users. This simplifies user management; you can just add or remove users from the role.",
        "This is a common source of confusion for new administrators. Remember the two-step process: `GRANT USAGE` on the schema first, then grant object-level privileges (like `SELECT` or `INSERT`) on the tables within it.",
        "By default, the `public` schema is accessible to all users. It's a security best practice to `REVOKE CREATE ON SCHEMA public FROM PUBLIC;` to prevent users from creating objects in the shared schema."
      ]
    }
  ]
};