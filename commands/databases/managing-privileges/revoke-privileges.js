export const command17 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Managing Privileges",
    "Revoke Privileges"
  ],
  "description": "<PERSON><PERSON>ves previously granted privileges from a role or user, enforcing security policies and the principle of least privilege.",
  "query_template": "REVOKE [privilege] ON [object_type] [object_name] FROM [role_name];",
  "components": [
    {
      "component": "REVOKE [privilege]",
      "name": "Privilege",
      "description": "The specific privilege to remove, such as SELECT, INSERT, UPDATE, DELETE, or ALL for all privileges."
    },
    {
      "component": "ON [object_name]",
      "name": "Database Object",
      "description": "The object the privilege applies to, like a TABLE, SEQUENCE, SCHEMA, or DATABASE."
    },
    {
      "component": "FROM [role_name]",
      "name": "Role/User",
      "description": "The role or user from whom the privilege is being revoked."
    },
    {
      "component": "CASCADE | RESTRICT",
      "name": "Dependency Handling",
      "description": "Optional. RESTRICT (the default) prevents revoking if other objects depend on the privilege. CASCADE revokes dependent privileges as well."
    }
  ],
  "scenarios": [
    {
      "title": "Adjusting User Permissions After a Role Change",
      "details": {
        "setup": "A junior database administrator, `junior_dba`, previously had permissions to delete data from the `customer_records` table. They have now moved to a data analyst role where they should only have read access.",
        "goal": "To enforce the principle of least privilege by removing the `DELETE` permission for `junior_dba` on the `customer_records` table, while leaving their `SELECT` permission intact.",
        "outcome": "The `junior_dba` role can no longer delete records from the `customer_records` table, reducing the risk of accidental data loss. Their ability to query the table is unaffected."
      },
      "example": {
        "query": "REVOKE DELETE ON TABLE customer_records FROM junior_dba;",
        "explanation": "This query specifically targets the `DELETE` privilege on the `customer_records` table and removes it from the `junior_dba` role. It's a precise adjustment that doesn't affect any other permissions the user might have."
      },
      "notes": [
        "To see the current permissions on a table, you can use the `\dp customer_records` command in `psql`.",
        "You can revoke multiple privileges at once: `REVOKE UPDATE, DELETE ON customer_records FROM junior_dba;`.",
        "Using `REVOKE ALL ...` is a powerful way to reset a user's permissions before granting back only what they explicitly need."
      ]
    }
  ]
};