export const command19 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Database Size and Statistics",
    "List All Database Sizes"
  ],
  "description": "Retrieves the size of all databases in the PostgreSQL cluster, formatting the output in a human-readable format (e.g., MB, GB).",
  "query_template": "SELECT datname, pg_size_pretty(pg_database_size(datname)) AS size FROM pg_database;",
  "components": [
    {
      "component": "pg_database_size(datname)",
      "name": "Size Calculation Function",
      "description": "A built-in PostgreSQL function that takes a database name as input and returns its total disk space usage in bytes."
    },
    {
      "component": "pg_size_pretty(...)",
      "name": "Formatting Function",
      "description": "A convenience function that converts a size in bytes (from `pg_database_size`) into a human-readable string with units like kB, MB, GB, etc."
    },
    {
      "component": "FROM pg_database",
      "name": "System Catalog",
      "description": "The query targets `pg_database`, a system catalog table that contains information about all available databases in the cluster."
    }
  ],
  "scenarios": [
    {
      "title": "Routine Disk Space Audit and Capacity Planning",
      "details": {
        "setup": "You are a database administrator managing a server that hosts multiple databases for different environments, including `prod_main`, `staging`, and `analytics_warehouse`.",
        "goal": "To quickly perform a weekly audit to monitor disk space consumption across all databases, identify which ones are the largest, and plan for future storage needs.",
        "outcome": "A clear, simple report is generated showing the current size of each database, allowing you to easily track growth and report on storage usage."
      },
      "example": {
        "query": "SELECT datname, pg_size_pretty(pg_database_size(datname)) AS size\nFROM pg_database\nWHERE datname NOT IN ('template0', 'template1')\nORDER BY pg_database_size(datname) DESC;",
        "explanation": "This query lists each database and its formatted size. It filters out the template databases for a cleaner report and, most importantly, orders the results from largest to smallest. This immediately brings the biggest consumers of disk space to your attention."
      },
      "notes": [
        "The reported size includes all objects within the database, such as tables, indexes, and materialized views.",
        "To investigate disk usage within a specific database (e.g., to find the largest tables), you will need to connect to that database and use functions like `pg_total_relation_size()`.",
        "Running this query requires sufficient privileges, typically as a superuser or a role with access to the `pg_database` catalog."
      ]
    }
  ]
};