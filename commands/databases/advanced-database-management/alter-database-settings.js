export const command18 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Advanced Database Management",
    "Alter Database Settings"
  ],
  "description": "Modifies the persistent, database-level properties, such as configuration parameters, name, or ownership.",
  "query_template": "ALTER DATABASE [database_name] [action];",
  "components": [
    {
      "component": "ALTER DATABASE [db_name]",
      "name": "Target Database",
      "description": "The command and the name of the database you intend to modify."
    },
    {
      "component": "SET [param] = '[value]'",
      "name": "Set Configuration",
      "description": "Overrides a system configuration parameter specifically for this database."
    },
    {
      "component": "RENAME TO [new_name]",
      "name": "Rename Database",
      "description": "Changes the name of the database."
    },
    {
      "component": "OWNER TO [new_owner]",
      "name": "Change Owner",
      "description": "Assigns a new owner (role) to the database."
    },
    {
      "component": "RESET [param]",
      "name": "Reset Configuration",
      "description": "Resets a database-specific parameter, causing it to inherit the global server default."
    }
  ],
  "scenarios": [
    {
      "title": "Standardizing Timezones for a Global Application",
      "details": {
        "setup": "You are managing a database for an application with users in multiple countries. Timestamps are being inserted with inconsistent local timezones, leading to confusion and incorrect reporting.",
        "goal": "To enforce the use of UTC for all connections to the `analytics_db`, ensuring all new timestamp data is stored consistently, regardless of the user's location.",
        "outcome": "All new sessions connecting to `analytics_db` will now default to the UTC timezone, standardizing time-based data from that point forward."
      },
      "example": {
        "query": "ALTER DATABASE analytics_db SET timezone = 'UTC';",
        "explanation": "This command sets the `timezone` parameter specifically for the `analytics_db`. It's a powerful way to enforce data consistency at the database level, overriding the server's main configuration or a user's session settings."
      },
      "notes": [
        "This setting does not affect existing data; it only applies to new connections.",
        "A client can still manually override this setting for their specific session by using `SET timezone = '...'` after connecting."
      ]
    },
    {
      "title": "Renaming a Database After a Product Rebrand",
      "details": {
        "setup": "A product named 'LegacyApp' has been rebranded to 'CurrentApp'. To maintain consistency across the infrastructure, the database `legacyapp_prod` needs to be renamed.",
        "goal": "To rename the `legacyapp_prod` database to `currentapp_prod` with no data loss. This requires a brief moment of downtime for the database itself.",
        "outcome": "The database is successfully renamed to `currentapp_prod`. All internal references and data remain intact."
      },
      "example": {
        "query": "ALTER DATABASE legacyapp_prod RENAME TO currentapp_prod;",
        "explanation": "The `RENAME TO` action provides a clean and atomic way to rename a database. This is purely a metadata change and is very fast."
      },
      "notes": [
        "You cannot be connected to the database you are trying to rename. Connect to `postgres` or another database to run this command.",
        "It's critical to terminate all other active connections to the database before renaming it. You can do this with `SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE datname = 'legacyapp_prod';`.",
        "After renaming, all application connection strings must be updated to point to the new database name."
      ]
    },
    {
      "title": "Transferring Database Ownership After a Staff Change",
      "details": {
        "setup": "The `research_data` database was created by a developer who has now left the organization. For security and administrative continuity, ownership must be transferred to a dedicated service account.",
        "goal": "To change the owner of the `research_data` database from the old developer's role ('jane_doe') to the new permanent owner ('science_service_role').",
        "outcome": "The `science_service_role` is now the owner of the database and has full administrative privileges over it, ensuring proper access management."
      },
      "example": {
        "query": "ALTER DATABASE research_data OWNER TO science_service_role;",
        "explanation": "This command transfers all ownership privileges for the database object itself to a new role. This is a fundamental task for maintaining database security and lifecycle management."
      },
      "notes": [
        "Only a superuser or the current owner of the database can execute this command.",
        "The new owner must be an existing role in the database cluster."
      ]
    }
  ]
};