export const command25 = {
  // --- Set the type to 'query' ---
  "type": "query",

  "path": [
    "Managing Databases and Roles",
    "Tablespace Management",
    "Move Table to Tablespace"
  ],
  "description": "Moves an existing table and its data to a different tablespace. This is commonly done for performance optimization or storage tiering (e.g., moving hot data to fast disks and cold data to slower, cheaper disks).",

  // --- Use query-specific fields ---
  "query_template": "ALTER TABLE [table_name] SET TABLESPACE [tablespace_name];",
  "components": [
    {
      "component": "ALTER TABLE [table_name]",
      "name": "Target Table",
      "description": "The command to modify the structure or properties of an existing table."
    },
    {
      "component": "SET TABLESPACE [tablespace_name]",
      "name": "Set Tablespace Clause",
      "description": "The specific action that relocates the table's data files to the specified destination tablespace."
    }
  ],

  "scenarios": [
    {
      "title": "Moving a High-Traffic Table to an SSD for Faster I/O",
      "details": {
        "setup": "Your database server has two types of storage: a large, standard HDD and a smaller, much faster NVMe SSD. A tablespace named `fast_storage` has been created on the SSD. Your `analytics_events` table is growing rapidly and is frequently queried, causing I/O bottlenecks on the HDD.",
        "goal": "To improve query performance for the `analytics_events` table by moving it from the default tablespace to the high-speed `fast_storage` tablespace.",
        "outcome": "The physical data files for the `analytics_events` table are relocated to the SSD. Queries against this table now execute with significantly lower latency."
      },
      "example": {
        "query": "ALTER TABLE analytics_events SET TABLESPACE fast_storage;",
        "explanation": "This command rewrites the `analytics_events` table into the `fast_storage` tablespace. PostgreSQL handles the physical file migration. This operation locks the table exclusively, so it should be performed during a low-traffic maintenance window."
      },
      "notes": [
        "This command only moves the table itself, not its indexes. You must move indexes separately with `ALTER INDEX my_index SET TABLESPACE fast_storage;`.",
        "The move can be I/O intensive and requires sufficient disk space on both the source and destination tablespaces during the operation.",
        "To move multiple tables at once, you can use `ALTER TABLE ALL IN TABLESPACE original_tablespace SET TABLESPACE new_tablespace;`."
      ]
    }
  ]
};