export const command24 = {
  "type": "query",
  "platformDependent": true,
  "path": [
    "Managing Databases and Roles",
    "Tablespace Management",
    "Create Tablespace"
  ],
  "description": "Creates a new tablespace, which allows you to define an alternative location on the file system for storing database objects like tables and indexes. This is commonly used to store high-usage objects on faster storage (like an SSD).",
  "query_template": "CREATE TABLESPACE [tablespace_name] LOCATION '[path_to_directory]';",
  "components": [
    {
      "component": "CREATE TABLESPACE [tablespace_name]",
      "name": "Tablespace Definition",
      "description": "The command and the unique name you assign to the new tablespace."
    },
    {
      "component": "LOCATION '[path]'",
      "name": "Storage Location",
      "description": "The absolute path to an existing, empty directory where the data files for this tablespace will be stored."
    },
    {
      "component": "OWNER [user_name]",
      "name": "Optional Owner",
      "description": "Assigns ownership of the tablespace to a specific user. By default, the current user becomes the owner."
    }
  ],
  "scenarios": [
    {
      "title": "Storing High-Traffic Tables on a Faster SSD",
      "details": {
        "setup": "Your database server has both a large, slower HDD for general storage and a smaller, faster NVMe SSD. A specific table, `analytics_events`, is growing rapidly and its heavy read/write activity is creating an I/O bottleneck, slowing down the entire database.",
        "goal": "To improve performance by storing the `analytics_events` table and its indexes on the fast NVMe SSD, separating its I/O from the rest of the database.",
        "outcome": "A new tablespace named `fast_ssd_storage` is created and mapped to the SSD's directory, making it available for new or existing tables and indexes."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux",
            "query": "CREATE TABLESPACE fast_ssd_storage LOCATION '/mnt/nvme/pg_data';",
            "explanation": "This command creates the tablespace and points it to a directory on a mounted NVMe drive. The directory must exist and be owned by the 'postgres' user."
          },
          {
            "platform": "Windows",
            "query": "CREATE TABLESPACE fast_ssd_storage LOCATION 'D:\\PostgreSQL\\ssd_data';",
            "explanation": "This command creates the tablespace on a Windows system, pointing to a directory on the D: drive, which is an SSD. The directory must exist, and the user running the PostgreSQL service needs full permissions."
          }
        ]
      },
      "notes": [
        "CRITICAL: The specified directory must already exist and be empty, and the PostgreSQL system user (usually 'postgres') must have ownership or write permissions.",
        "After creating the tablespace, you can assign an object to it: `CREATE TABLE new_table (...) TABLESPACE fast_ssd_storage;`",
        "To move an existing table, use: `ALTER TABLE analytics_events SET TABLESPACE fast_ssd_storage;` Note that this will lock the table and can take a long time."
      ]
    }
  ]
};