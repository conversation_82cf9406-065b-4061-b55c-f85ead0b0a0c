export const command23 = {
  "path": [
    "Managing Databases and Roles",
    "Tablespace Management",
    "List Tablespaces"
  ],
  "command": "\\db [pattern]",
  "description": "A psql meta-command to list all tablespaces in the PostgreSQL cluster. Tablespaces allow you to define alternative locations on the file system where the data files for database objects can be stored. Use \\db+ for more detailed information.",
  "parameters": [
    {
      "flag": "\\db",
      "name": "List Tablespaces",
      "description": "Shows a basic list of tablespace names, their owners, and access privileges."
    },
    {
      "flag": "\\db+",
      "name": "Detailed List",
      "description": "Provides an extended listing that includes the size, physical file system location, and any options."
    },
    {
      "flag": "[pattern]",
      "name": "Pattern Match",
      "description": "Lists only the tablespaces whose names match the specified pattern. For example, `\\db pg_*` lists all default tablespaces."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Storage Locations for Performance Optimization",
      "details": {
        "setup": "You are a database administrator for a system with high transaction volumes. To improve performance, you have created a new tablespace on a fast NVMe SSD drive to store frequently accessed indexes.",
        "goal": "Before moving the indexes, you need to verify that the new tablespace, named 'fast_indexes', was created correctly and points to the right SSD mount point.",
        "outcome": "The command output displays a detailed list of all tablespaces, confirming that 'fast_indexes' exists, is owned by the correct role, and its location is set to the intended SSD path (e.g., '/mnt/nvme/pgdata/fast_indexes')."
      },
      "example": {
        "command": "\\db+ fast_indexes",
        "explanation": "Using the `\\db+` variant with the pattern 'fast_indexes' provides all the necessary details, including the physical location and size. This allows you to confirm the setup without needing server file system access, directly from the psql client."
      },
      "notes": [
        "Tablespaces are a powerful feature for managing storage, especially in large-scale deployments with varied hardware (SSDs, HDDs, network storage).",
        "The directory path specified when creating a tablespace must exist and have the correct permissions for the PostgreSQL system user."
      ]
    }
  ]
};