export const command26 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Connection Management",
    "Show Current Connections"
  ],
  "description": "Displays real-time information about currently active connections to the database, including the user, client address, and the query being executed.",
  "query_template": "SELECT [columns] FROM pg_stat_activity WHERE state = 'active';",
  "components": [
    {
      "component": "SELECT usename, datname, client_addr, state, query",
      "name": "Selected Columns",
      "description": "Specifies which details to retrieve for each connection. Key columns include the user, database name, client IP, connection state, and the full text of the query."
    },
    {
      "component": "FROM pg_stat_activity",
      "name": "Source View",
      "description": "pg_stat_activity is a system view that provides one row for each server process, showing the current state of that connection."
    },
    {
      "component": "WHERE state = 'active'",
      "name": "State Filter",
      "description": "Filters the results to show only connections that are actively executing a query. Other common states include 'idle' (connected but not running a query) and 'idle in transaction'."
    }
  ],
  "scenarios": [
    {
      "title": "Investigating Unexpected High CPU on the Database Server",
      "details": {
        "setup": "Your monitoring dashboard alerts you that the database server's CPU utilization has been consistently high for the last 15 minutes, and application users are reporting slow response times.",
        "goal": "To quickly identify which queries are currently running and potentially causing the high CPU load.",
        "outcome": "You get a list of all active queries. By examining their text and start times, you can pinpoint the long-running or resource-intensive query responsible for the performance issue."
      },
      "example": {
        "query": "SELECT pid, usename, datname, client_addr, now() - query_start AS duration, state, query \nFROM pg_stat_activity \nWHERE state = 'active' \nORDER BY duration DESC;",
        "explanation": "This query retrieves all active connections and calculates their current execution time (`duration`). By ordering the results with the longest-running queries first (`ORDER BY duration DESC`), you can immediately focus on the most likely culprits for a server slowdown. The `pid` (process ID) is included, which is essential for any follow-up action."
      },
      "notes": [
        "To view the `query` text for all users, you must be a superuser. Non-superusers can only see the queries they themselves have run.",
        "Once you identify a problematic query by its `pid`, you can terminate it safely using `SELECT pg_cancel_backend(pid);` (to cancel the query) or `SELECT pg_terminate_backend(pid);` (to kill the connection)."
      ]
    }
  ]
};