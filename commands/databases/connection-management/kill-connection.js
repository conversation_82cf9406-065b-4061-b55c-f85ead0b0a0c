export const command27 = {
  "type": "query",
  "path": [
    "Managing Databases and Roles",
    "Connection Management",
    "Kill Connection"
  ],
  "description": "Forcefully terminates one or more active database connections based on specific criteria like the user, database, or connection state. This is a powerful administrative tool for managing problematic sessions.",
  "query_template": "SELECT pg_terminate_backend(pid) FROM pg_stat_activity WHERE [condition];",
  "components": [
    {
      "component": "pg_terminate_backend(pid)",
      "name": "Termination Function",
      "description": "The core function that takes a process ID (pid) and safely but forcefully terminates the corresponding database session. This will roll back any open transaction."
    },
    {
      "component": "FROM pg_stat_activity",
      "name": "Connection View",
      "description": "The system view that provides a real-time snapshot of all active connections (backends) to the database server, including their pid, user, state, and the currently running query."
    },
    {
      "component": "WHERE [condition]",
      "name": "Filtering Condition",
      "description": "The clause used to select which connections to terminate. Common columns to filter on include `usename`, `datname` (database name), `state` ('active', 'idle', 'idle in transaction'), or even the `query` text itself."
    }
  ],
  "scenarios": [
    {
      "title": "Stopping a Long-Running, Resource-Intensive Query",
      "details": {
        "setup": "A developer has executed a complex analytical query on the production database without a proper `LIMIT`. The query is consuming 100% of a CPU core and has been running for over an hour, slowing down the entire system.",
        "goal": "To immediately stop the single runaway query to restore normal database performance without affecting any other user sessions.",
        "outcome": "The specific problematic connection is terminated, its transaction is rolled back, server resources are freed up, and the database returns to normal operation."
      },
      "example": {
        "query": "SELECT pg_terminate_backend(12345);",
        "explanation": "First, you would identify the process ID (pid) of the runaway query by running `SELECT pid, usename, query_start, query FROM pg_stat_activity WHERE state = 'active' ORDER BY query_start ASC;`. Once you identify the pid (e.g., 12345), you execute this command to terminate that specific process."
      },
      "notes": [
        "Consider using `pg_cancel_backend(pid)` first, which is a gentler attempt to cancel the query. `pg_terminate_backend` is more forceful, similar to `kill -9`."
      ]
    },
    {
      "title": "Clearing Idle Connections Holding Critical Locks",
      "details": {
        "setup": "A deployment has failed, and several key tables are now locked, preventing new application instances from starting. Investigation shows that connections from a specific application user are in an 'idle in transaction' state, meaning they started a transaction but never finished it, and are now holding the locks.",
        "goal": "To terminate all 'idle in transaction' connections belonging to the 'app_user' to release all locks they are holding.",
        "outcome": "All specified idle connections are terminated, the locks are released, and the blocked application processes can now proceed."
      },
      "example": {
        "query": "SELECT count(*) as terminated_sessions, pg_terminate_backend(pid) FROM pg_stat_activity WHERE usename = 'app_user' AND state = 'idle in transaction' GROUP BY pg_terminate_backend(pid);",
        "explanation": "This query filters the activity log for all sessions that belong to 'app_user' and are stuck in the 'idle in transaction' state. It then passes the pid of each matching session to `pg_terminate_backend`, effectively clearing all of them at once and providing a count."
      },
      "notes": [
        "This is often a temporary fix. The root cause is likely an application bug that needs to be fixed to ensure transactions are always committed or rolled back properly."
      ]
    }
  ]
};