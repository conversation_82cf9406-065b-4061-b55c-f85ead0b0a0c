export const command7 = {
  "type": "command",
  "path": [
    "Installation and Server Management",
    "Connect to PostgreSQL"
  ],
  "command": "psql -h [hostname] -p [port] -U [username] -d [database_name]",
  "description": "Connect to a PostgreSQL database using the interactive terminal (psql). This is the primary command-line tool for interacting with PostgreSQL, allowing you to execute SQL queries, manage database objects, and run administrative tasks directly on the server.",
  "parameters": [
    {
      "flag": "psql",
      "name": "Default Connection",
      "description": "If run with no flags, attempts to connect to a database with the same name as the current operating system user."
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL username to connect as."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "Specifies the name of the database to connect to."
    },
    {
      "flag": "-h",
      "name": "hostname",
      "description": "Specifies the server's hostname or IP address. Defaults to 'localhost' or a local socket."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Specifies the TCP port the server is listening on. Defaults to 5432."
    },
    {
      "flag": "-W",
      "name": "Password Prompt",
      "description": "Forces psql to prompt for a password before connecting. Highly recommended for remote connections."
    }
  ],
  "scenarios": [
    {
      "title": "Connect to a Local Database for Development",
      "details": {
        "setup": "You have just installed PostgreSQL on your local machine and created a new database named `project_dev` for a web application you are building. Your main administrative user is `postgres`.",
        "goal": "To connect to the `project_dev` database as the `postgres` user to create tables and insert initial data.",
        "outcome": "You are successfully connected to the psql interactive shell for the `project_dev` database, ready to execute SQL commands."
      },
      "example": {
        "command": "psql -U postgres -d project_dev",
        "explanation": "This command specifies the user (`-U postgres`) and the database (`-d project_dev`). Since the host (`-h`) is omitted, it defaults to `localhost`. You will be prompted for the `postgres` user's password if required by your local setup."
      },
      "notes": [
        "If your OS username is `postgres` and you want to connect to a database also named `postgres`, you can often just run `psql` with no parameters."
      ]
    },
    {
      "title": "Securely Connect to a Remote Production Database",
      "details": {
        "setup": "You are a database administrator needing to perform a health check on a production database hosted on a cloud server with the address `db.example.com`. You need to connect using a read-only user, `readonly_user`, to the `analytics_db` database.",
        "goal": "To establish a secure, remote connection to the production database without exposing the password in your command history.",
        "outcome": "A connection is established to the remote server, and you are securely prompted for a password before being granted access to the database shell."
      },
      "example": {
        "command": "psql -h db.example.com -p 5432 -U readonly_user -d analytics_db -W",
        "explanation": "Here, `-h` points to the remote server address, `-U` and `-d` specify the credentials, and `-W` is crucial for security as it forces a password prompt instead of other authentication methods. This prevents the password from being saved in your shell's history file."
      },
      "notes": [
        "For this to work, the remote server's firewall must allow connections from your IP address on the specified port (5432).",
        "The server's `pg_hba.conf` file must also be configured to accept connections from your IP for that user and database."
      ]
    }
  ]
};