export const command6 = {
  // --- This is a command, not a query ---
  "type": "command",

  "path": [
    "Installation and Server Management",
    "Managing the Server",
    "Check Server Status"
  ],
  "command": "[service_manager] status postgresql",
  "description": "Check the current running status of the PostgreSQL server process using the appropriate command for your operating system's service manager.",

  // --- Flag to indicate platform differences ---
  "platformDependent": true,
  
  "parameters": [], // This command itself does not take parameters.

  "scenarios": [
    {
      "title": "Verifying the PostgreSQL Server is Running Before Connecting",
      "details": {
        "setup": "After a system reboot or before deploying an application, you need to ensure the PostgreSQL database server is active and ready to accept connections.",
        "goal": "To confirm that the PostgreSQL process is running without having to attempt a connection and wait for a potential timeout.",
        "outcome": "The command returns a clear status (e.g., 'running', 'started', 'active'), often including the process ID (PID) and how long the server has been up."
      },
      "example": {
        // --- The 'variants' array provides platform-specific commands ---
        "variants": [
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl status postgresql",
            "explanation": "This is the standard command on modern Linux distributions (like Ubuntu 16+, CentOS 7+, Debian 8+). It provides a detailed status, including whether the service is active, enabled on boot, and recent log entries."
          },
          {
            "platform": "macOS (Homebrew)",
            "command": "brew services list",
            "explanation": "This command lists all services managed by Homebrew. Look for your PostgreSQL version in the list and check that its status is 'started'."
          },
          {
            "platform": "Windows (Services)",
            "command": "Get-Service postgres*",
            "explanation": "Using PowerShell, this command finds any service whose name begins with 'postgres'. The 'Status' column in the output should say 'Running'."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "pg_ctl status -D \"C:\\Program Files\\PostgreSQL\\15\\data\"",
            "explanation": "The `pg_ctl` utility is the most direct way to check the server. It works on any platform but requires specifying the path to the PostgreSQL data directory. This is very reliable if you are unsure of the OS service name."
          }
        ]
      },
      "notes": [
        "The exact service name (e.g., 'postgresql', 'postgresql-14', 'postgresql-x64-15') can vary depending on the installation method and version. Use the name that matches your setup.",
        "If the server is not running, you can typically replace 'status' with 'start' in the `systemctl` or Windows service commands to start it."
      ]
    }
  ]
};