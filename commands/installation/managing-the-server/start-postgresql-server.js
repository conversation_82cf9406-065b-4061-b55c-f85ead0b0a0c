export const command3 = {
  "path": [
    "Installation and Server Management",
    "Managing the Server",
    "Start PostgreSQL Server"
  ],
  "command": "[platform_specific_command]",
  "description": "Starts the PostgreSQL database server as a background process, allowing clients to connect and interact with databases.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "postgresql",
      "name": "Service Name",
      "description": "The name of the service registered with the operating system's service manager. This can vary based on the installation method and PostgreSQL version."
    }
  ],
  "scenarios": [
    {
      "title": "Starting the PostgreSQL Server for Local Development",
      "details": {
        "setup": "You have just installed PostgreSQL on your machine, or the server was stopped after a system reboot. You now need to start the database to begin your development work.",
        "goal": "To get the PostgreSQL server running in the background so you can connect to it using `psql` or another database client.",
        "outcome": "The PostgreSQL server process is active and listening for connections on the default port (usually 5432)."
      },
      "example": {
        "variants": [
          {
            "platform": "macOS (Homebrew)",
            "command": "brew services start postgresql",
            "explanation": "This is the standard command when PostgreSQL is installed via Homebrew. It starts the server and registers it to launch automatically on login."
          },
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl start postgresql",
            "explanation": "Used by most modern Linux distributions (like Ubuntu, Fedora, Debian). It starts the PostgreSQL service for the current session."
          },
          {
            "platform": "Windows",
            "command": "net start postgresql-x64-16",
            "explanation": "On Windows, PostgreSQL runs as a system service. The service name (e.g., `postgresql-x64-16`) can vary depending on the specific version you installed."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "pg_ctl -D /var/lib/pgsql/data -l logfile start",
            "explanation": "The `pg_ctl` utility is the most direct way to control the server and works on any OS. You must specify the data directory (`-D`) and it is recommended to specify a log file (`-l`)."
          }
        ]
      },
      "notes": [
        "To have the service start automatically on boot with systemd, run `sudo systemctl enable postgresql`.",
        "If the server fails to start, check the latest entries in the PostgreSQL log file for error messages. The log file location varies by OS.",
        "You can check the status after starting it with `brew services list` (macOS), `systemctl status postgresql` (Linux), or `pg_ctl -D /path/to/data status` (Universal)."
      ]
    }
  ]
};