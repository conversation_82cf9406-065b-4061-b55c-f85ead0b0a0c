export const command4 = {
  // --- Standard command type ---
  "type": "command",

  // --- New flag to identify platform-specific commands ---
  "platformDependent": true,

  "path": [
    "Installation and Server Management",
    "Managing the Server",
    "Stop PostgreSQL Server"
  ],
  // --- Generic command using the universal utility ---
  "command": "pg_ctl stop [-D /path/to/data_directory] [-m mode]",
  "description": "Stops the PostgreSQL server process safely. This should be done before shutting down the host machine or performing system maintenance to ensure data integrity.",
  "parameters": [
    {
      "flag": "-D",
      "name": "datadir",
      "description": "Specifies the file system location of the database data directory. Required if the PGDATA environment variable is not set."
    },
    {
      "flag": "-m",
      "name": "mode",
      "description": "Specifies the shutdown mode. 'smart' (the default) waits for all clients to disconnect. 'fast' rolls back active transactions and disconnects clients. 'immediate' aborts all server processes without a clean shutdown and should only be used as a last resort."
    }
  ],
  "scenarios": [
    {
      "title": "Performing a Safe Shutdown for System Maintenance",
      "details": {
        "setup": "You are a database administrator who needs to apply critical OS patches to the server hosting your PostgreSQL instance. The database is currently active.",
        "goal": "To shut down the PostgreSQL service gracefully, ensuring all active transactions are safely committed or rolled back and all data files are closed properly before the maintenance begins.",
        "outcome": "The PostgreSQL server process is terminated cleanly, and it is now safe to shut down the host machine for maintenance without risking data corruption."
      },
      // --- Example object contains variants for each platform ---
      "example": {
        "variants": [
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl stop postgresql",
            "explanation": "The standard command on modern Linux distributions (like Ubuntu, CentOS, Debian) that use the systemd init system to manage services."
          },
          {
            "platform": "macOS (Homebrew)",
            "command": "brew services stop postgresql",
            "explanation": "The recommended method for stopping PostgreSQL if it was installed and is managed by the Homebrew package manager."
          },
          {
            "platform": "Windows",
            "command": "net stop postgresql-x64-15",
            "explanation": "On Windows, PostgreSQL typically runs as a service. The service name (e.g., 'postgresql-x64-15') can vary based on the PostgreSQL version."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "pg_ctl stop -D /var/lib/postgresql/15/main",
            "explanation": "The `pg_ctl` utility is the most direct way to control the server and works on any platform. However, it requires you to explicitly provide the path to your database's data directory."
          }
        ]
      },
      "notes": [
        "Using a service manager (`systemctl`, `brew services`, `net stop`) is generally preferred as it handles finding the data directory and other settings automatically.",
        "The default 'smart' shutdown mode is the safest, but `pg_ctl stop -m fast` can be used if you need to shut down more quickly and it's acceptable to disconnect active users."
      ]
    }
  ]
};