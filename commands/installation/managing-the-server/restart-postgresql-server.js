export const command5 = {
  "path": [
    "Installation and Server Management",
    "Managing the Server",
    "Restart PostgreSQL Server"
  ],
  "command": "[platform-specific command]",
  "description": "Restarts the PostgreSQL server completely. This is required for certain configuration changes to take effect and will temporarily disconnect all users.",
  // --- This command is platform-dependent ---
  "platformDependent": true,
  "parameters": [], // This command itself does not have parameters
  "scenarios": [
    {
      "title": "Applying a Critical Configuration Change",
      "details": {
        "setup": "You are a database administrator who has just edited the `postgresql.conf` file to change a fundamental parameter, such as `max_connections` or `shared_buffers`. These changes cannot be applied by just reloading the configuration.",
        "goal": "To stop and then start the PostgreSQL server process in a safe and controlled manner so that it loads the new configuration from the file.",
        "outcome": "The PostgreSQL server restarts successfully. Upon reconnection, you can verify with `SHOW max_connections;` that the new setting is active."
      },
      "example": {
        // --- 'variants' array provides platform-specific commands ---
        "variants": [
          {
            "platform": "macOS (Homebrew)",
            "command": "brew services restart postgresql",
            "explanation": "Uses the Homebrew services manager to handle the stop and start operations. The service name might vary if you have multiple versions installed."
          },
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl restart postgresql",
            "explanation": "The standard command for most modern Linux distributions (like Ubuntu 20.04+, CentOS 7+, Debian 9+). The service name can sometimes include the version, e.g., `postgresql-14`."
          },
          {
            "platform": "Windows",
            "command": "net stop postgresql-x64-15 && net start postgresql-x64-15",
            "explanation": "Manages the PostgreSQL Windows service from the command line. The service name (`postgresql-x64-15`) is version-specific and must be adjusted to match your installation."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "pg_ctl restart -D /var/lib/postgresql/15/main",
            "explanation": "The `pg_ctl` utility is the most direct way to control the server and works on all platforms. However, it requires you to explicitly provide the path to your database cluster's data directory (`-D`)."
          }
        ]
      },
      "notes": [
        "A `restart` is more disruptive than a `reload`. Always check the official documentation to see if a configuration change requires a full restart.",
        "This command will terminate all active database connections, so it should be performed during a planned maintenance window.",
        "Be sure to verify the correct service name for your specific PostgreSQL version and operating system."
      ]
    }
  ]
};