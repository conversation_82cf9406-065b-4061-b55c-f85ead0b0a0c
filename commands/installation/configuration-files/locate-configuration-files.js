export const command11 = {
  "type": "query",
  "path": [
    "Installation and Server Management",
    "Configuration Files",
    "Locate Configuration Files"
  ],
  "description": "Finds the absolute paths to PostgreSQL's critical configuration files (`postgresql.conf`, `pg_hba.conf`, etc.) directly from the running server. This is the most reliable method, as locations can vary based on the operating system and installation method.",
  "query_template": "SHOW [parameter_name];",
  "components": [
    {
      "component": "SHOW config_file",
      "name": "postgresql.conf Path",
      "description": "Returns the location of the main server configuration file, which controls resource usage, logging, and other server-wide settings."
    },
    {
      "component": "SHOW hba_file",
      "name": "pg_hba.conf Path",
      "description": "Returns the location of the Host-Based Authentication file, which defines client connection and authentication rules."
    },
    {
      "component": "SHOW ident_file",
      "name": "pg_ident.conf Path",
      "description": "Returns the location of the user name mapping file, used for `ident` and `peer` authentication."
    },
    {
      "component": "SHOW data_directory",
      "name": "Data Directory Path",
      "description": "Returns the location where PostgreSQL stores all of its database files. The configuration files are typically located here."
    }
  ],
  "scenarios": [
    {
      "title": "Locating `postgresql.conf` to Tune Server Performance",
      "details": {
        "setup": "You are a database administrator who has just taken over a new production server. You need to adjust the `shared_buffers` setting to improve memory performance, but are unsure where the configuration files are located on this particular Linux distribution.",
        "goal": "To find the exact file path of `postgresql.conf` so you can edit it with confidence.",
        "outcome": "The query instantly returns the full path (e.g., `/var/lib/pgsql/15/data/postgresql.conf`), allowing you to locate and modify the correct file."
      },
      "example": {
        "query": "SHOW config_file;",
        "explanation": "This SQL command directly asks the running PostgreSQL instance for the location of its main configuration file. It is the most reliable way to find the file, eliminating guesswork and the risk of editing the wrong one."
      },
      "notes": [
        "This query typically requires superuser privileges to run.",
        "After editing `postgresql.conf`, most changes require a server restart or reload to take effect."
      ]
    },
    {
      "title": "Finding `pg_hba.conf` to Update Client Authentication Rules",
      "details": {
        "setup": "A new application server with a static IP address needs to be granted access to the database. Company policy dictates that access must be restricted to only this IP address.",
        "goal": "To locate the Host-Based Authentication (`pg_hba.conf`) file in order to add a new security rule.",
        "outcome": "The query reveals the path to `pg_hba.conf`, allowing you to add the new rule, reload the configuration, and grant access to the new server immediately."
      },
      "example": {
        "query": "SHOW hba_file;",
        "explanation": "The `pg_hba.conf` file is the gatekeeper for all connections. Using `SHOW hba_file;` ensures you are editing the file that the live server is actively using for its security configuration."
      },
      "notes": [
        "Changes to `pg_hba.conf` do not require a server restart. A reload is sufficient and causes no downtime.",
        "You can trigger a reload by running the SQL command `SELECT pg_reload_conf();` as a superuser."
      ]
    }
  ]
};