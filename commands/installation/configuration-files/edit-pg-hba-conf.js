export const command13 = {
  "type": "command",
  "platformDependent": true,
  "path": [
    "Installation and Server Management",
    "Configuration Files",
    "Edit pg_hba.conf"
  ],
  "command": "[editor] [path_to/pg_hba.conf]",
  "description": "Edit the host-based authentication file (pg_hba.conf) to control which users can connect to which databases from which addresses, and how they must authenticate.",
  "parameters": [
    {
      "flag": "sudo",
      "name": "Superuser Privileges",
      "description": "Often required to edit system configuration files that are owned by the 'postgres' user or 'root'."
    },
    {
      "flag": "nano",
      "name": "Text Editor",
      "description": "A command-line text editor used to modify the file. You can replace this with your preferred editor, like 'vim', 'emacs', or a graphical editor like 'gedit' or 'VS Code'."
    }
  ],
  "scenarios": [
    {
      "title": "Granting a Remote Application Server Access to a Database",
      "details": {
        "setup": "You have a new web application running on a server with the IP address '*************'. The application needs to connect to the 'app_db' database as the 'webapp_user', but PostgreSQL is currently rejecting the connection.",
        "goal": "To securely modify the `pg_hba.conf` file to allow the application server to connect to the specified database using SCRAM-SHA-256 password authentication.",
        "outcome": "The application server can successfully connect to 'app_db'. All other remote connections are still denied, maintaining security."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux (Debian/Ubuntu)",
            "command": "sudo nano /etc/postgresql/15/main/pg_hba.conf",
            "explanation": "On Debian-based systems, configuration files are typically in `/etc/postgresql/[version]/[cluster]`. After opening, add the line: `host app_db webapp_user *************/32 scram-sha-256`"
          },
          {
            "platform": "Linux (Red Hat/Fedora)",
            "command": "sudo nano /var/lib/pgsql/15/data/pg_hba.conf",
            "explanation": "On Red Hat-based systems, the file is usually located inside the data directory. Add the line: `host app_db webapp_user *************/32 scram-sha-256`"
          },
          {
            "platform": "macOS (Homebrew)",
            "command": "nano /usr/local/var/postgres/pg_hba.conf",
            "explanation": "Homebrew installations place the data directory, including config files, under `/usr/local/var/`. Add the line: `host app_db webapp_user *************/32 scram-sha-256`"
          },
          {
            "platform": "Windows",
            "command": "notepad \"C:\\Program Files\\PostgreSQL\\15\\data\\pg_hba.conf\"",
            "explanation": "On Windows, you can use a graphical editor like Notepad. Remember to run it as an Administrator. Add the line: `host app_db webapp_user *************/32 scram-sha-256`"
          },
          {
            "platform": "Universal (Find the file)",
            "command": "psql -U postgres -c 'SHOW hba_file;'",
            "explanation": "This is the most reliable method on any platform. It connects to the database and asks PostgreSQL to show you the exact path to the `pg_hba.conf` file it is currently using."
          }
        ]
      },
      "notes": [
        "IMPORTANT: After saving your changes to `pg_hba.conf`, you must reload the PostgreSQL configuration for them to take effect. Use `sudo systemctl reload postgresql` on systemd Linux, or `pg_ctl reload`.",
        "The order of rules in `pg_hba.conf` matters. PostgreSQL reads the file from top to bottom and uses the first matching rule.",
        "Using `md5` is less secure than `scram-sha-256`. Avoid `trust` for remote connections in a production environment."
      ]
    }
  ]
};