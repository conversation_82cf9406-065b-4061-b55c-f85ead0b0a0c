export const command12 = {
  // --- This is a command-line utility, not a query ---
  "type": "command",

  "path": [
    "Installation and Server Management",
    "Configuration Files",
    "Edit postgresql.conf"
  ],
  "command": "sudo [editor] /path/to/postgresql.conf",
  "description": "Opens and edits the main PostgreSQL configuration file (postgresql.conf). The location of this file varies significantly between operating systems and installation methods.",
  "parameters": [
    {
      "flag": "sudo",
      "name": "Superuser Privilege",
      "description": "Editing the configuration file is a privileged operation and typically requires 'sudo'."
    },
    {
      "flag": "[editor]",
      "name": "Text Editor",
      "description": "Your preferred command-line text editor, such as 'nano', 'vim', or 'emacs'."
    },
    {
      "flag": "/path/to/postgresql.conf",
      "name": "Configuration File Path",
      "description": "The full path to the postgresql.conf file. See the scenarios for platform-specific examples."
    }
  ],

  // --- Mark as platform-dependent for special handling in your app ---
  "platformDependent": true,

  "scenarios": [
    {
      "title": "Tuning Memory Settings for Improved Performance",
      "details": {
        "setup": "You are administering a PostgreSQL server running with default settings. After analysis, you've determined that the server is under-utilizing available RAM, leading to suboptimal query performance.",
        "goal": "To locate and edit the `postgresql.conf` file to increase the `shared_buffers` setting, allowing PostgreSQL to use more memory for caching and improve performance.",
        "outcome": "The `postgresql.conf` file is successfully modified. After reloading or restarting the server, the new `shared_buffers` value is active."
      },
      "example": {
        // --- The 'variants' array provides platform-specific examples ---
        "variants": [
          {
            "platform": "Universal (Recommended)",
            "command": "psql -U postgres -c 'SHOW config_file;'",
            "explanation": "This is the most reliable method on any platform. It connects to the database and asks the server directly where its configuration file is located. You can then use the output path to edit the file."
          },
          {
            "platform": "Linux (Debian/Ubuntu)",
            "command": "sudo nano /etc/postgresql/16/main/postgresql.conf",
            "explanation": "On Debian-based systems, configuration is often stored in a version-specific path under `/etc/postgresql/`. The version number (e.g., '16') will change."
          },
          {
            "platform": "Linux (Red Hat/CentOS)",
            "command": "sudo nano /var/lib/pgsql/data/postgresql.conf",
            "explanation": "On Red Hat-based systems, the file is typically located inside the data directory, which requires superuser access."
          },
          {
            "platform": "macOS (Homebrew)",
            "command": "nano /usr/local/var/postgres/postgresql.conf",
            "explanation": "Homebrew installations on macOS usually place the configuration file in this location. 'sudo' is often not required for this path."
          }
        ]
      },
      "notes": [
        "Before editing, it is highly recommended to make a backup of the `postgresql.conf` file.",
        "After saving your changes, many parameters require a server restart (`pg_ctl restart` or `systemctl restart postgresql`) or a configuration reload (`pg_ctl reload` or `systemctl reload postgresql`) to take effect.",
        "The `SHOW config_file;` command is the definitive way to avoid confusion and find the correct file."
      ]
    }
  ]
};