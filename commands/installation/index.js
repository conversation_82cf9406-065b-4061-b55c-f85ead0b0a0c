import { command0 } from './installing-postgresql/using-homebrew/install-homebrew.js';
import { command1 } from './installing-postgresql/using-homebrew/install-postgresql.js';
import { command2 } from './installing-postgresql/using-postgres-app.js';
import { command3 } from './managing-the-server/start-postgresql-server.js';
import { command4 } from './managing-the-server/stop-postgresql-server.js';
import { command5 } from './managing-the-server/restart-postgresql-server.js';
import { command6 } from './managing-the-server/check-server-status.js';
import { command7 } from './connect-to-postgresql.js';
import { command8 } from './connection-methods/connect-with-full-parameters.js';
import { command9 } from './connection-methods/connect-with-connection-string.js';
import { command10 } from './connection-methods/connect-via-environment-variables.js';
import { command11 } from './configuration-files/locate-configuration-files.js';
import { command12 } from './configuration-files/edit-postgresql-conf.js';
import { command13 } from './configuration-files/edit-pg-hba-conf.js';
import { command14 } from './environment-setup/create-pgpass-file.js';
import { command15 } from './environment-setup/setup-postgresql-service-systemd.js';

export const installationCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15
];

export function validateInstallationCommands() {
  return installationCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Installation and Server Management";
    const hasDescription = cmd.description;
    const hasType = cmd.type;

    if (!hasPath || !hasDescription || !hasType) {
      return false;
    }

    switch (cmd.type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
