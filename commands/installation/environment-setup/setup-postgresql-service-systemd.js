export const command15 = {
  "path": [
    "Installation and Server Management",
    "Environment Setup",
    "Setup PostgreSQL Service"
  ],
  "command": "systemctl <action> postgresql",
  "description": "Manages the PostgreSQL service on modern Linux systems (using systemd), allowing it to be started, stopped, and enabled to run on boot.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "enable",
      "name": "Enable Service",
      "description": "Configures the PostgreSQL service to start automatically on system boot."
    },
    {
      "flag": "start",
      "name": "Start Service",
      "description": "Starts the PostgreSQL service immediately."
    },
    {
      "flag": "status",
      "name": "Check Status",
      "description": "Shows the current status of the service (e.g., active, inactive, failed)."
    },
    {
      "flag": "restart",
      "name": "Restart Service",
      "description": "Stops and then starts the service. Used for applying configuration changes that require a full restart."
    },
    {
      "flag": "stop",
      "name": "Stop Service",
      "description": "Stops the PostgreSQL service."
    }
  ],
  "scenarios": [
    {
      "title": "Configuring PostgreSQL to Run Automatically on a New Production Server",
      "details": {
        "setup": "You have just successfully installed PostgreSQL on a new production server running a modern Linux distribution like Ubuntu 22.04 or CentOS 9. By default, the database service is not running and will not start automatically after a server reboot.",
        "goal": "To start the PostgreSQL service for immediate use and, crucially, ensure it launches automatically every time the server boots up to maintain service availability.",
        "outcome": "The PostgreSQL service is active and ready to accept connections. It is now registered as a system service that will reliably start on boot."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl enable postgresql && sudo systemctl start postgresql",
            "explanation": "This is the standard, idempotent command. `enable` creates a system link to ensure the service starts on boot, and `start` runs it immediately for the current session."
          },
          {
            "platform": "Windows",
            "command": "sc config postgresql-x64-15 start=auto\r\nnet start postgresql-x64-15",
            "explanation": "On Windows, PostgreSQL runs as a named service. `sc config` sets its startup type to 'Automatic', and `net start` activates it. The service name (`postgresql-x64-15`) may vary based on your PostgreSQL version."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "# Add 'pg_ctl start -D /path/to/data' to a system startup script (e.g., rc.local)",
            "explanation": "The `pg_ctl` utility is not a service manager itself but is the underlying tool used to control the database server. For non-systemd or custom setups, you would add the `pg_ctl start` command to a boot script."
          }
        ]
      },
      "notes": [
        "After running the command, it's best practice to immediately check the service status using `sudo systemctl status postgresql` to confirm it is 'active (running)'.",
        "The service name might differ slightly, for example, `postgresql-15` or just `postgres` on some distributions. Use `systemctl list-units | grep postgres` to find the correct name."
      ]
    }
  ]
};