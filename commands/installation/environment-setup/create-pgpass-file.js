export const command14 = {
  "type": "command",
  "path": [
    "Installation and Server Management",
    "Environment Setup",
    "Create .pgpass File"
  ],
  "command": "echo '[entry]' >> [path] && chmod 600 [path]",
  "description": "Creates and secures a password file that allows PostgreSQL client tools (like psql, pg_dump) to authenticate without prompting for a password. This is essential for automating scripts.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "hostname",
      "name": "Server Hostname",
      "description": "The hostname or IP address of the PostgreSQL server. Can be '*' to match all."
    },
    {
      "flag": "port",
      "name": "Server Port",
      "description": "The port the PostgreSQL server is running on. Can be '*' to match all."
    },
    {
      "flag": "database",
      "name": "Database Name",
      "description": "The specific database to connect to. Can be '*' to match all."
    },
    {
      "flag": "username",
      "name": "User Name",
      "description": "The PostgreSQL user for the connection. Can be '*' to match all."
    },
    {
      "flag": "password",
      "name": "User's Password",
      "description": "The password for the specified user."
    }
  ],
  "scenarios": [
    {
      "title": "Automating a Nightly Backup Script Securely",
      "details": {
        "setup": "You have a shell script that runs `pg_dump` every night to back up the 'prod_db' database. This script is executed automatically by a cron job (on Linux) or Task Scheduler (on Windows).",
        "goal": "To allow the `pg_dump` command within the script to connect to the PostgreSQL server without hardcoding the password or requiring manual password entry, which would cause the automated job to hang or fail.",
        "outcome": "The backup script runs non-interactively, securely authenticating using the credentials stored in the password file. The file itself is locked down to prevent other users on the system from reading it."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux / macOS",
            "command": "echo 'db.example.com:5432:prod_db:backup_user:Sup3rS3cr3t' >> ~/.pgpass && chmod 600 ~/.pgpass",
            "explanation": "This single line appends the credential string to the `.pgpass` file in the current user's home directory. The `&& chmod 600 ~/.pgpass` command immediately follows to set the file's permissions so that only the file owner can read or write it. This permission setting is mandatory; PostgreSQL will ignore the file if it's accessible by others."
          },
          {
            "platform": "Windows",
            "command": "echo db.example.com:5432:prod_db:backup_user:Sup3rS3cr3t >> %APPDATA%\\postgresql\\pgpass.conf",
            "explanation": "On Windows, the file is named `pgpass.conf` and is located in the `%APPDATA%\\postgresql\\` directory. This command appends the credential line to that file. Windows file permissions are handled differently, and as long as the file is in your user profile directory, it is considered secure by default. There is no `chmod` equivalent needed for PostgreSQL to accept the file."
          }
        ]
      },
      "notes": [
        "The PostgreSQL client will automatically detect and use this file for any tool that uses the `libpq` library.",
        "The first matching line in the file for the connection parameters will be used.",
        "Wildcards (`*`) are useful for matching any database on a specific host, but use them with caution."
      ]
    }
  ]
};