export const command8 = {
  "type": "command",
  "path": [
    "Installation and Server Management",
    "Connection Methods",
    "Connect with Full Parameters"
  ],
  "command": "psql -h [host] -p [port] -U [user] -d [database]",
  "description": "Connects to a PostgreSQL server with specific connection parameters. This is essential when the target database is not on the local machine or does not use default settings.",
  "parameters": [
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (e.g., localhost, an IP address, or a domain name)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "The port number the server is listening on. The default is 5432."
    },
    {
      "flag": "-U",
      "name": "username",
      "description": "The PostgreSQL username to connect with."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The specific database name you want to connect to."
    },
    {
      "flag": "-W",
      "name": "force password prompt",
      "description": "Forces psql to prompt for a password before connecting. This is highly recommended for security."
    },
    {
      "flag": "-w",
      "name": "no password prompt",
      "description": "Never issues a password prompt. Useful for scripts where the authentication is handled by other means (e.g., a `.pgpass` file)."
    }
  ],
  "scenarios": [
    {
      "title": "Connecting to a Remote Production Database for Maintenance",
      "details": {
        "setup": "You are a database administrator who needs to perform a manual data update on the company's production database, which is hosted on a dedicated server with the domain 'db.my-company.com'.",
        "goal": "To securely connect to the 'prod_main' database as the 'db_admin' user and be prompted for a password.",
        "outcome": "A successful and secure connection to the remote production database is established, and you are presented with the psql command prompt."
      },
      "example": {
        "command": "psql -h db.my-company.com -p 5432 -U db_admin -d prod_main -W",
        "explanation": "This command specifies all the necessary details: the host address (`-h`), the user (`-U`), and the database name (`-d`). Crucially, `-W` is used to ensure you are prompted for a password, which prevents it from being stored in your shell history."
      },
      "notes": [
        "If the connection fails, a common cause is a firewall on the remote server blocking incoming connections on port 5432.",
        "For security, the user `db_admin` should have the minimum required privileges for the maintenance task."
      ]
    },
    {
      "title": "Connecting to a PostgreSQL Instance in a Docker Container",
      "details": {
        "setup": "As a developer, you are running a local PostgreSQL instance for your project inside a Docker container. To avoid conflicts with other local services, you have mapped the container's port 5432 to port 5433 on your local machine (`localhost`).",
        "goal": "To connect to the 'app_dev' database running inside the container from your machine's terminal.",
        "outcome": "A psql session is successfully started, connected to the database running inside the Docker container."
      },
      "example": {
        "command": "psql -h localhost -p 5433 -U app_user -d app_dev",
        "explanation": "Even though the host is `localhost`, the `-p 5433` flag is the critical part of this command. It directs `psql` to connect to the non-default port that Docker is exposing on your machine."
      },
      "notes": [
        "A 'Connection refused' error in this context usually means the Docker container is not running or the port mapping is incorrect.",
        "You can check the active port mappings by running the `docker ps` command."
      ]
    }
  ]
};