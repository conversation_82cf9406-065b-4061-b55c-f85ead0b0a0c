export const command9 = {
  "path": [
    "Installation and Server Management",
    "Connection Methods",
    "Connect with Connection String"
  ],
  "command": "psql 'postgresql://[user]:[password]@[hostname]:[port]/[database]?[options]'",
  "description": "Connects to a PostgreSQL database using a single, convenient URI (Uniform Resource Identifier). This method is standard for applications and cloud hosting providers.",
  "parameters": [
    {
      "flag": "user",
      "name": "Username",
      "description": "The database user to connect as."
    },
    {
      "flag": "password",
      "name": "Password",
      "description": "The user's password. It's optional and can be omitted for security."
    },
    {
      "flag": "hostname",
      "name": "Hostname",
      "description": "The IP address or domain name of the database server."
    },
    {
      "flag": "port",
      "name": "Port",
      "description": "The port the PostgreSQL server is listening on. Defaults to 5432."
    },
    {
      "flag": "database",
      "name": "Database Name",
      "description": "The specific database to connect to on the server."
    },
    {
      "flag": "?sslmode=require",
      "name": "Connection Options",
      "description": "Optional parameters to control the connection behavior, such as SSL requirements."
    }
  ],
  "scenarios": [
    {
      "title": "Connecting to a Cloud-Hosted Production Database",
      "details": {
        "setup": "Your web application is deployed, and its PostgreSQL database is managed by a cloud provider (e.g., Heroku, AWS RDS, DigitalOcean). The provider gives you a single 'Database URL' to connect to your instance.",
        "goal": "To quickly and securely connect your local `psql` client to the remote production database for a maintenance check, using the provided connection string.",
        "outcome": "You successfully establish a secure connection to the remote 'prod_db' database, allowing you to run diagnostic queries directly."
      },
      "example": {
        "command": "psql 'postgresql://prod_user:<EMAIL>:5432/prod_db?sslmode=require'",
        "explanation": "This command uses the full connection URI provided by the cloud host. Enclosing the URI in single quotes (' ') is crucial to prevent the shell from misinterpreting special characters like '&', '?', or '$' that might be in the password or options."
      },
      "notes": [
        "In application development, this connection string is almost always stored in an environment variable (e.g., `DATABASE_URL`) rather than being hard-coded.",
        "The `sslmode=require` parameter ensures that the connection to the remote database is encrypted, which is a critical security practice.",
        "If the password contains special characters, using the URI format can be more reliable than using individual `-U`, `-W`, `-h` parameters."
      ]
    }
  ]
};