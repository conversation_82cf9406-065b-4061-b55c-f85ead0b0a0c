export const command10 = {
  // --- This is a command, so we will mark it as such ---
  "type": "command",

  "path": [
    "Installation and Server Management",
    "Connection Methods",
    "Connect via Environment Variables"
  ],
  "command": "psql", // The core command is now simpler, with platform variations in the examples
  "description": "Sets temporary environment variables for the current shell session to allow `psql` and other client applications to connect without specifying connection parameters as arguments.",
  "parameters": [
    {
      "flag": "PGHOST",
      "name": "Server Host",
      "description": "Specifies the hostname or IP address of the database server. Defaults to 'localhost'."
    },
    {
      "flag": "PGPORT",
      "name": "Server Port",
      "description": "Specifies the TCP port the server is listening on. Defaults to '5432'."
    },
    {
      "flag": "PGUSER",
      "name": "Username",
      "description": "The PostgreSQL user name to connect as. Defaults to the current OS username."
    },
    {
      "flag": "PGDATABASE",
      "name": "Database Name",
      "description": "The name of the database to connect to. Defaults to the same as the username."
    },
    {
      "flag": "PGPASSWORD",
      "name": "Password",
      "description": "The password for the user. Using this variable is a security risk on multi-user systems."
    },
    {
      "flag": "PGSSLMODE",
      "name": "SSL Mode",
      "description": "Determines the SSL connection policy (e.g., 'disable', 'allow', 'prefer', 'require')."
    }
  ],
  // --- Flagging this command as having platform-specific syntax ---
  "platformDependent": true,
  "scenarios": [
    {
      "title": "Automating a Script without Hard-Coding Credentials",
      "details": {
        "setup": "You have a nightly data processing script that needs to connect to a PostgreSQL database named `analytics_db` on a remote server (`db.example.com`) as the user `etl_user`.",
        "goal": "To run the script without embedding the database credentials directly in the script file, making it more secure and portable.",
        "outcome": "The environment is configured, and the `psql` command (or another PostgreSQL tool) inside the script connects automatically and executes its tasks."
      },
      "example": {
        // --- 'variants' array provides platform-specific examples ---
        "variants": [
          {
            "platform": "Linux / macOS (bash/zsh)",
            "command": "export PGHOST=db.example.com PGUSER=etl_user PGDATABASE=analytics_db && your_script.sh",
            "explanation": "The `export` command sets the variables for the current session. The `&&` ensures that `your_script.sh` is only run if the export is successful. `psql` or any libpq-based tool within the script will inherit these variables."
          },
          {
            "platform": "Windows (Command Prompt)",
            "command": "set PGHOST=db.example.com && set PGUSER=etl_user && set PGDATABASE=analytics_db && C:\\path\\to\\your_script.bat",
            "explanation": "The `set` command is used in the Windows Command Prompt to create temporary environment variables for the current session."
          },
          {
            "platform": "Windows (PowerShell)",
            "command": "$env:PGHOST='db.example.com'; $env:PGUSER='etl_user'; $env:PGDATABASE='analytics_db'; & C:\\path\\to\\your_script.ps1",
            "explanation": "PowerShell uses the `$env:` scope to manage environment variables. Semicolons are used to run multiple commands sequentially."
          }
        ]
      },
      "notes": [
        "For non-interactive scripts, using `PGPASSWORD` is insecure as it can be visible to other users on the system. The recommended approach is to use a `.pgpass` file in the user's home directory.",
        "To make these settings permanent, add the commands to your shell's startup file (e.g., `~/.bashrc`, `~/.zshrc`, or your PowerShell `$PROFILE` file)."
      ]
    }
  ]
};