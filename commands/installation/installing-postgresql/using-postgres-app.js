export const command2 = {
  "type": "command",
  "platform": "macOS",
  "path": [
    "Installation and Server Management",
    "Installing PostgreSQL",
    "Using Postgres.app (macOS)"
  ],
  "command": "Install and initialize Postgres.app",
  "description": "Postgres.app is a full-featured PostgreSQL server packaged as a standard Mac application. It provides a simple GUI to start, stop, and manage a local server, and it includes popular extensions like PostGIS.",
  "parameters": [],
  "scenarios": [
    {
      "title": "Quickly Set Up a Local PostgreSQL Development Environment on macOS",
      "details": {
        "setup": "A developer needs a local, isolated PostgreSQL database for a new project on their Mac. They want to avoid complex configuration or command-line installers like Homebrew.",
        "goal": "To get a fully functional PostgreSQL server running in under five minutes, with simple GUI controls for managing the server instance.",
        "outcome": "Postgres.app is installed and a server is running. The developer can connect to a default database and has access to command-line tools like `psql` directly from their terminal after a one-time configuration."
      },
      "example": {
        "command": "1. Download from postgresapp.com 2. Drag to /Applications 3. Launch & Initialize",
        "explanation": "The process is entirely GUI-driven:\n1. **Download:** Get the latest version from the official website (postgresapp.com).\n2. **Install:** Drag the downloaded app into your `/Applications` folder.\n3. **Initialize:** Launch the app. Click the elephant icon in the macOS menu bar and then click the 'Initialize' button to create and start your first server."
      },
      "notes": [
        "To use the included command-line tools (like `psql` or `pg_dump`), you must add them to your shell's PATH. The official documentation provides a `sudo` command to set this up permanently.",
        "Postgres.app stores its database files in `~/Library/Application Support/Postgres/var-XX`, where XX is the major PostgreSQL version.",
        "The app allows you to run multiple PostgreSQL server versions simultaneously, which is great for testing compatibility."
      ]
    }
  ]
};