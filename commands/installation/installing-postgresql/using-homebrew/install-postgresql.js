export const command1 = {
  // --- Adding a type for clarity in your app ---
  "type": "command",

  "path": [
    "Installation and Server Management",
    "Installing PostgreSQL",
    "Using Homebrew (macOS)", // Clarified platform in the path
    "Install PostgreSQL"
  ],
  "command": "brew install postgresql",
  "description": "For macOS users with Homebrew, this command downloads and installs the latest stable version of PostgreSQL and all its dependencies. Homebrew manages the installation and future upgrades.",
  // --- This command has no parameters, so the field is omitted ---

  // --- NEW ENHANCED DATA ---
  "scenarios": [
    {
      "title": "Setting Up a Local PostgreSQL Development Environment on macOS",
      "details": {
        "setup": "You are a developer starting a new project on your MacBook. You need a reliable, locally running database for development, testing, and prototyping before deploying to a production environment.",
        "goal": "To quickly install a complete, managed PostgreSQL server on your machine without manually downloading binaries or resolving dependencies.",
        "outcome": "The PostgreSQL server software is successfully installed. You are now ready to start the server and create your first database."
      },
      "example": {
        "command": "brew install postgresql",
        "explanation": "This command leverages Homebrew, the standard package manager for macOS, to handle the entire installation process. It fetches the latest stable PostgreSQL version, compiles it if necessary, and links all the required binaries (like `psql`, `pg_dump`, etc.) into your system's PATH."
      },
      "notes": [
        "After installation, you need to start the PostgreSQL service. The recommended way is: `brew services start postgresql`.",
        "Once the service is running, you can connect to the default database with the command: `psql postgres`.",
        "Homebrew installs the latest major version by default. To install a specific version, use a command like `brew install postgresql@15`.",
        "To see all available PostgreSQL versions in Homebrew, you can run: `brew search postgresql`."
      ]
    }
  ]
};