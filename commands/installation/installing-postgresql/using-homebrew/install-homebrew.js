export const command0 = {
  "type": "command",
  "path": [
    "Installation and Server Management",
    "Installing PostgreSQL",
    "Using Homebrew",
    "Install Homebrew"
  ],
  "command": "/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"",
  "description": "Homebrew is a popular package manager for macOS and Linux that simplifies the installation and management of software like PostgreSQL. This is the official command to install Homebrew itself.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "/bin/bash -c",
      "name": "Execute Command",
      "description": "Tells the system to execute the following string as a shell command."
    },
    {
      "flag": "$(...)",
      "name": "Command Substitution",
      "description": "Executes the command inside the parentheses (in this case, `curl`) and substitutes its output back into the main command."
    },
    {
      "flag": "curl -fsSL",
      "name": "Download Script",
      "description": "A tool to download the installer script from the URL. The flags mean --fail, --silent, --show-error, and --location (follow redirects)."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a New macOS Development Environment",
      "details": {
        "setup": "You have a new Mac and need to install PostgreSQL for a development project. You want an easy, reliable way to install, update, and manage the PostgreSQL server and its command-line tools.",
        "goal": "To install Homebrew, the package manager that will be used to easily manage the PostgreSQL installation.",
        "outcome": "The Homebrew installation script completes successfully, and the `brew` command is now available in your terminal for use."
      },
      "example": {
        "command": "/bin/bash -c \"$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\"",
        "explanation": "This command securely downloads the official Homebrew installer script from its GitHub repository and executes it using the bash shell. It's the standard, recommended method for bootstrapping Homebrew on a new system."
      },
      "notes": [
        "The script will explain what it will install and prompt you for your password to grant `sudo` permissions before it begins.",
        "After the installation, the script will output a 'Next steps' message. This often includes a command you must run to add Homebrew to your shell's PATH. This is a critical step!",
        "For Windows users, a popular alternative is Chocolatey or using the Windows Subsystem for Linux (WSL) and installing Homebrew there.",
        "For Debian/Ubuntu users, `apt` is the native package manager. For Red Hat/Fedora users, use `dnf` or `yum`."
      ]
    }
  ]
};