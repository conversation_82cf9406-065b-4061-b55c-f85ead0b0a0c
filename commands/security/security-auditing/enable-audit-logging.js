export const command9 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "Security Auditing",
    "Enable Audit Logging"
  ],
  "description": "Configures PostgreSQL's logging engine to create a security audit trail. This is crucial for compliance and for tracking activity on the database server.",
  "query_template": "ALTER SYSTEM SET [parameter_name] = '[value]';",
  "components": [
    {
      "component": "ALTER SYSTEM",
      "name": "Alter System Command",
      "description": "Modifies a global server configuration parameter directly via SQL. This command writes the setting to the 'postgresql.auto.conf' file."
    },
    {
      "component": "SET [parameter_name] = '[value]'",
      "name": "Parameter Assignment",
      "description": "Specifies which logging parameter to change and what value to assign to it."
    },
    {
      "component": "SELECT pg_reload_conf();",
      "name": "Applying Changes",
      "description": "After running ALTER SYSTEM, most logging parameters require a configuration reload to take effect. This function applies the changes without a full server restart."
    }
  ],
  "scenarios": [
    {
      "title": "Scenario 1: Log All Statements for Maximum Auditing",
      "details": {
        "setup": "You are deploying a new application that processes sensitive data and must comply with a strict security policy (e.g., PCI-DSS, HIPAA) requiring every executed statement to be logged.",
        "goal": "To configure the PostgreSQL server to log every single SQL statement from all users to a central log file for review.",
        "outcome": "The server configuration is updated, and all subsequent SQL queries are captured in the PostgreSQL log, providing a complete audit trail."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_statement = 'all';\nSELECT pg_reload_conf();",
        "explanation": "The `log_statement = 'all'` setting instructs PostgreSQL to log all queries. `ALTER SYSTEM` makes this change persistent. We immediately follow it with `pg_reload_conf()` to apply the setting without needing to restart the database server."
      },
      "notes": [
        "Logging 'all' statements can generate a very high volume of logs and may introduce a performance overhead on busy systems.",
        "Consider this for high-security environments or temporary debugging sessions."
      ]
    },
    {
      "title": "Scenario 2: Create a Detailed, Production-Ready Audit Trail",
      "details": {
        "setup": "A production database requires a robust audit log that not only tracks data changes but also records who connects, from where, and when. The logs must be easy to parse and contain clear contextual information.",
        "goal": "To implement a comprehensive logging policy that captures connections, disconnections, and all data modification statements (INSERT, UPDATE, DELETE), with each log line prefixed by user, database, and client IP.",
        "outcome": "The PostgreSQL logs now contain a detailed, structured record of all relevant user activity, suitable for automated parsing and security analysis."
      },
      "example": {
        "query": "ALTER SYSTEM SET log_connections = on;\nALTER SYSTEM SET log_disconnections = on;\nALTER SYSTEM SET log_statement = 'mod';\nALTER SYSTEM SET log_line_prefix = '%t [%p]: user=%u,db=%d,client=%h ';\nSELECT pg_reload_conf();",
        "explanation": "This sequence of commands builds a complete policy. `log_connections` and `log_disconnections` track user sessions. `log_statement = 'mod'` logs only data-changing statements, reducing noise. The `log_line_prefix` adds critical context (timestamp, user, database, client host) to every log entry, making the audit trail far more useful."
      },
      "notes": [
        "The `'mod'` setting is a good balance for production, as it captures all data changes without logging high-volume `SELECT` statements.",
        "For even better log management, consider setting `log_destination = 'csvlog'` to create logs in CSV format, which can be easily loaded into a table or analysis tool."
      ]
    }
  ]
};