export const command10 = {
  "type": "command",
  "path": [
    "Security and Authentication",
    "Security Auditing",
    "View Failed Login Attempts"
  ],
  "description": "Describes the procedure for locating and inspecting the PostgreSQL log files to identify failed authentication attempts, which is a critical step in detecting potential security threats like brute-force attacks.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "log_destination",
      "name": "Log Destination",
      "description": "A setting in 'postgresql.conf' that determines where logs are written (e.g., 'stderr', 'csvlog', 'syslog', 'eventlog'). The commands to search logs depend on this."
    },
    {
      "flag": "log_directory",
      "name": "Log Directory",
      "description": "Specifies the directory where log files will be created if logging to 'stderr'. The default is the 'log' subdirectory within the data directory."
    },
    {
      "flag": "log_connections",
      "name": "Log Connections",
      "description": "Set to 'on' in 'postgresql.conf' to log each successful connection attempt."
    },
    {
      "flag": "log_hostname",
      "name": "Log Hostname",
      "description": "Set to 'on' to log the IP address or hostname of connecting clients, which is crucial for identifying the source of an attack."
    }
  ],
  "scenarios": [
    {
      "title": "Auditing for Brute-Force Login Attempts on a Production Server",
      "details": {
        "setup": "You are a database administrator for a production server that accepts connections from multiple applications. You need to implement a routine security check to ensure no unauthorized access attempts are occurring.",
        "goal": "To regularly scan the PostgreSQL logs for failed login attempts, identify the source IP addresses of the attempts, and determine which user accounts are being targeted.",
        "outcome": "You have a clear, filtered list of all 'authentication failed' messages from the logs, allowing you to take action, such as blocking a suspicious IP address at the firewall."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux (File-based Logs)",
            "command": "sudo grep 'FATAL:.*authentication failed' /var/log/postgresql/postgresql-*.log",
            "explanation": "This command searches the default PostgreSQL log directory on Debian/Ubuntu systems for lines containing the 'FATAL' error associated with failed authentication. The path may differ on other distributions (e.g., `/var/lib/pgsql/<version>/data/log`)."
          },
          {
            "platform": "Linux (systemd/journalctl)",
            "command": "sudo journalctl -u postgresql -g 'authentication failed'",
            "explanation": "If your system logs PostgreSQL output to the systemd journal, this is the most efficient way to filter for relevant messages from the `postgresql` service unit."
          },
          {
            "platform": "Windows",
            "command": "findstr /C:\"FATAL:\" /C:\"authentication failed\" \"%PGDATA%\\log\\*.log\"",
            "explanation": "This command uses `findstr` to search for failed authentication messages in the `log` subdirectory of your PostgreSQL data directory (referenced by the `%PGDATA%` environment variable)."
          },
          {
            "platform": "Docker",
            "command": "docker logs your_postgres_container_name 2>&1 | grep 'authentication failed'",
            "explanation": "This command retrieves the logs from the specified Docker container and pipes them to `grep` to filter for authentication failures. `2>&1` ensures that standard error, where PostgreSQL often logs, is included."
          }
        ]
      },
      "notes": [
        "Authentication failures are logged as `FATAL` by default. Your `log_min_messages` setting should be `WARNING` or lower to ensure these are captured.",
        "For a comprehensive audit, also check for successful connections from unexpected IP addresses.",
        "In a high-security environment, consider using automated log analysis tools like pgBadger or shipping logs to a centralized monitoring system (e.g., ELK Stack, Splunk)."
      ]
    }
  ]
};