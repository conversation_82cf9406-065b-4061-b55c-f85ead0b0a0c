export const command8 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "Data Encryption",
    "Column-Level Encryption"
  ],
  "description": "Encrypts and decrypts data within specific table columns using symmetric-key encryption from the pgcrypto extension. This is a crucial technique for protecting sensitive Personally Identifiable Information (PII) at rest.",
  "query_template": "SELECT pgp_sym_encrypt('[sensitive_data]', '[encryption_key]'); -- For encryption\nSELECT pgp_sym_decrypt([encrypted_column], '[encryption_key]'); -- For decryption",
  "components": [
    {
      "component": "pgp_sym_encrypt(data, key)",
      "name": "Symmetric Encryption Function",
      "description": "Takes the plaintext data and a secret key, and returns the encrypted data as a `bytea` value. The column storing this data should be of type `bytea`."
    },
    {
      "component": "pgp_sym_decrypt(bytea, key)",
      "name": "Symmetric Decryption Function",
      "description": "Takes the encrypted `bytea` data and the same secret key used for encryption, and returns the original plaintext data."
    },
    {
      "component": "'encryption_key'",
      "name": "Secret Key",
      "description": "The secret password used for both encryption and decryption. Key management is critical; this key should never be stored in the database itself."
    }
  ],
  "scenarios": [
    {
      "title": "Protecting User Social Security Numbers (SSN) in a 'users' Table",
      "details": {
        "setup": "You have a `users` table that must store sensitive user information, including Social Security Numbers. To comply with data privacy regulations, this data must be encrypted at rest in the database.",
        "goal": "To store SSNs in an encrypted format so that a direct database query cannot reveal the sensitive data, but the application can decrypt it when necessary for verification purposes.",
        "outcome": "The `ssn` column in the `users` table is replaced with an `ssn_encrypted` (type: `bytea`) column. Any new or updated SSNs are stored in their encrypted form."
      },
      "example": {
        "query": "-- First, ensure the pgcrypto extension is enabled:\nCREATE EXTENSION IF NOT EXISTS pgcrypto;\n\n-- To store an encrypted SSN for a user:\nUPDATE users \nSET ssn_encrypted = pgp_sym_encrypt('***********', 'YOUR_APP_SECRET_KEY') \nWHERE user_id = 101;\n\n-- To retrieve and decrypt the SSN for that user:\nSELECT pgp_sym_decrypt(ssn_encrypted, 'YOUR_APP_SECRET_KEY') AS decrypted_ssn \nFROM users \nWHERE user_id = 101;",
        "explanation": "The `UPDATE` statement uses `pgp_sym_encrypt` to convert the plaintext SSN into an encrypted binary format before storing it. The `SELECT` statement uses `pgp_sym_decrypt` with the exact same secret key to convert the data back to its original form for use in the application."
      },
      "notes": [
        "CRITICAL: The encryption key should be managed securely by your application (e.g., using a vault or environment variables) and should NEVER be stored in plaintext in the database.",
        "The column used to store the encrypted data must be of type `bytea`.",
        "This method provides application-level encryption. Users with SQL access and the secret key can decrypt the data."
      ]
    }
  ]
};