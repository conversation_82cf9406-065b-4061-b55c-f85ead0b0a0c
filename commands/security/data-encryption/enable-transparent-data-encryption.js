export const command7 = {
  "path": [
    "Security and Authentication",
    "Data Encryption",
    "Enable Transparent Data Encryption"
  ],
  "command": "Configuration via postgresql.conf and extension-specific commands",
  "description": "Transparent Data Encryption (TDE) encrypts data at rest (the physical files on disk). This is not a native feature in standard PostgreSQL and requires installing a dedicated third-party extension (e.g., pg_tde) or using a commercial version of PostgreSQL. The process involves configuration, key management, and creating encrypted tablespaces.",
  "parameters": [
    {
      "flag": "shared_preload_libraries",
      "name": "Load Extension",
      "description": "The TDE extension must be loaded at server start by adding it to this setting in `postgresql.conf`."
    },
    {
      "flag": "pg_tde.keyring_provider",
      "name": "Keyring Provider",
      "description": "A configuration parameter (from the extension) to define how and where encryption keys are managed (e.g., a local file or a key management service like HashiCorp Vault)."
    },
    {
      "flag": "CREATE TABLESPACE ... WITH (encryption = 'aes-256')",
      "name": "Encrypted Tablespace",
      "description": "A command (specific to the extension) used to create a new tablespace where all data files will be encrypted."
    }
  ],
  "scenarios": [
    {
      "title": "Securing Sensitive Client Data at Rest for GDPR/PCI DSS Compliance",
      "details": {
        "setup": "You are the database administrator for a financial services company. Auditors require that all personally identifiable information (PII) and financial records be encrypted at rest to comply with regulations like GDPR or PCI DSS.",
        "goal": "To implement database-level encryption so that even if an unauthorized party gains access to the server's hard drive, the database files are unreadable without the master encryption key.",
        "outcome": "A new encrypted tablespace is created. All tables and indexes created within this tablespace are automatically and transparently encrypted on disk, satisfying the compliance requirement without any changes to application code."
      },
      "example": {
        "command": "/* This is a multi-step process, not a single command. */\n\n-- Step 1: Configure postgresql.conf (after installing the pg_tde extension)\n-- shared_preload_libraries = 'pg_tde'\n-- pg_tde.keyring_provider = 'file'\n-- pg_tde.keyring_file_path = '/etc/postgresql/15/main/pg_tde_keyring'\n\n-- Step 2: Restart PostgreSQL for changes to take effect.\n\n-- Step 3: Connect to PostgreSQL and create the extension.\nCREATE EXTENSION pg_tde;\n\n-- Step 4: Create an encrypted tablespace.\nCREATE TABLESPACE encrypted_data OWNER db_owner LOCATION '/var/lib/postgresql/data/encrypted_ts' WITH (encryption = 'aes-256');\n\n-- Step 5: Create your sensitive tables within the new tablespace.\nCREATE TABLE sensitive_clients (\n  client_id SERIAL PRIMARY KEY,\n  full_name TEXT NOT NULL,\n  card_number TEXT NOT NULL\n) TABLESPACE encrypted_data;",
        "explanation": "This sequence first configures PostgreSQL to load the `pg_tde` extension and points it to a key management file. After creating the extension, you create an `encrypted_data` tablespace. Any table created in this tablespace, like `sensitive_clients`, will have its data files automatically encrypted on the disk, providing transparent encryption at rest."
      },
      "notes": [
        "TDE adds performance overhead due to the real-time encryption and decryption process. Benchmark to ensure it meets your performance requirements.",
        "Key management is CRITICAL. If you lose the encryption keys, you will lose your data forever. Ensure your key management solution is secure and backed up.",
        "Alternatives to TDE include full-disk encryption at the OS level (e.g., BitLocker, LUKS) or application-level encryption, where the application encrypts data before sending it to the database."
      ]
    }
  ]
};