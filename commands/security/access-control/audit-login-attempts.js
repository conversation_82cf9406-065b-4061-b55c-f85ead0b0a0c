export const command5 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "Access Control",
    "Audit Login Attempts"
  ],
  "description": "Monitors all current database connections in real-time to identify active users, their IP addresses, and the databases they are accessing. This is essential for security audits and performance troubleshooting.",
  "query_template": "SELECT usename, datname, client_addr, backend_start, state FROM pg_stat_activity;",
  "components": [
    {
      "component": "SELECT usename, datname, client_addr, backend_start, state",
      "name": "Selected Columns",
      "description": "Specifies which pieces of information to retrieve about each connection, such as the user, database, client IP, start time, and current status."
    },
    {
      "component": "FROM pg_stat_activity",
      "name": "Data Source",
      "description": "The query targets `pg_stat_activity`, a built-in system view that provides a real-time snapshot of all active backend processes and their corresponding connections."
    }
  ],
  "scenarios": [
    {
      "title": "Real-Time Security Audit of Active Database Connections",
      "details": {
        "setup": "As a database administrator, you are conducting a routine security check. You need to identify all currently active user sessions, especially those from unexpected IP addresses or connections that have been idle for a long time.",
        "goal": "To get a live snapshot of all database connections, showing which user is connected to which database, the client's IP address, and the current status of their session.",
        "outcome": "A tabular list of all active connections is returned, allowing you to quickly identify legitimate users, spot potential unauthorized access, and troubleshoot problematic sessions."
      },
      "example": {
        "query": "SELECT usename, datname, client_addr, backend_start, state FROM pg_stat_activity WHERE usename IS NOT NULL;",
        "explanation": "This query retrieves key details from the `pg_stat_activity` view. Filtering `WHERE usename IS NOT NULL` conveniently removes internal system processes from the list, focusing the audit on actual user connections. The `client_addr` is crucial for tracing the origin of each connection."
      },
      "notes": [
        "A connection `state` of 'idle in transaction' can indicate an application bug where a transaction was started but never committed or rolled back. These connections can hold locks and consume resources.",
        "To view all connections across the entire instance, you typically need superuser privileges. A regular user will only be able to see their own session.",
        "For a historical record of login attempts (successful and failed), you should enable connection logging in `postgresql.conf`. `pg_stat_activity` only shows the current state."
      ]
    }
  ]
};