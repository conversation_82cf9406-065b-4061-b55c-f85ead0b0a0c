export const command6 = {
  // --- Differentiated as a 'query' ---
  "type": "query",

  "path": [
    "Security and Authentication",
    "Access Control",
    "Grant Connect Privilege"
  ],
  "description": "Grants the ability for a specific user or role to connect to a database. This is the first layer of security, controlling who can establish a session with a database before any data access is even considered.",
  
  // --- Query-specific structure ---
  "query_template": "GRANT CONNECT ON DATABASE [database_name] TO [user_or_role];",
  "components": [
    {
      "component": "GRANT CONNECT",
      "name": "Privilege Type",
      "description": "Specifies that the privilege being granted is the ability to connect."
    },
    {
      "component": "ON DATABASE [database_name]",
      "name": "Target Object",
      "description": "Identifies the specific database to which this privilege will apply."
    },
    {
      "component": "TO [user_or_role]",
      "name": "Recipient",
      "description": "The user or role that will receive the connection privilege."
    },
    {
      "component": "WITH GRANT OPTION",
      "name": "<PERSON> Option (Optional)",
      "description": "Allows the recipient user to grant this same privilege to other roles. This should be used with caution."
    }
  ],

  // --- Detailed professional scenario ---
  "scenarios": [
    {
      "title": "Securing a New HR Database with a Dedicated User Role",
      "details": {
        "setup": "A new, highly sensitive database named `human_resources` has been created. By default, all users (`PUBLIC` role) can connect to new databases. This is a security risk, and you need to lock it down.",
        "goal": "To ensure only members of the `hr_department` role can connect to the `human_resources` database. All other non-superusers must be blocked.",
        "outcome": "Members of the `hr_department` role can successfully connect. Any other user, like `sales_user`, who attempts to connect receives a 'FATAL: permission denied for database \"human_resources\"' error."
      },
      "example": {
        "query": "-- Step 1: Revoke the default public connection privilege\nREVOKE CONNECT ON DATABASE human_resources FROM PUBLIC;\n\n-- Step 2: Grant the privilege only to the specific role\nGRANT CONNECT ON DATABASE human_resources TO hr_department;",
        "explanation": "This two-step process enforces the principle of least privilege. First, `REVOKE ... FROM PUBLIC` removes the dangerous default permission for all users. Second, `GRANT CONNECT` explicitly gives access only to the intended role (`hr_department`), ensuring no unauthorized users can even establish a connection."
      },
      "notes": [
        "Granting `CONNECT` does not grant any rights to view, modify, or delete data. You must still `GRANT` `SELECT`, `INSERT`, etc., on specific tables.",
        "This is a critical first step when provisioning a new database in a production environment.",
        "You can grant this privilege to a role (like `hr_department`) or a specific user (like `john_doe`). Using roles is generally better for managing permissions for groups of users."
      ]
    }
  ]
};