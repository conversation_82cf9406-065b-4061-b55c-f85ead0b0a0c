export const command2 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "Authentication Methods",
    "View pg_hba.conf Rules"
  ],
  "description": "Displays the current host-based authentication (HBA) rules directly from the `pg_hba.conf` file without needing to access the server's file system. This view is essential for auditing and verifying security configurations.",
  "query_template": "SELECT * FROM pg_hba_file_rules;",
  "components": [
    {
      "component": "pg_hba_file_rules",
      "name": "System View",
      "description": "A built-in view that parses the `pg_hba.conf` file and presents its rules in a structured SQL format. Access is restricted to superusers."
    },
    {
      "component": "line_number",
      "name": "Line Number",
      "description": "The line number in the `pg_hba.conf` file where the rule is defined."
    },
    {
      "component": "auth_method",
      "name": "Authentication Method",
      "description": "The method required for authentication (e.g., `scram-sha-256`, `md5`, `peer`, `trust`)."
    },
    {
      "component": "address",
      "name": "Client Address",
      "description": "The IP address range (CIDR) that this rule applies to. It is null for `local` connections."
    }
  ],
  "scenarios": [
    {
      "title": "Auditing Remote Access for a New Application Server",
      "details": {
        "setup": "A new application server with the IP address `*************` needs to connect to the `production_db` as the `app_user`. A senior administrator has updated the `pg_hba.conf` file, but you need to verify the rule is active before deploying the application.",
        "goal": "To confirm from your SQL client that there is an active HBA rule allowing `app_user` to connect from `*************` to `production_db` using the `scram-sha-256` authentication method.",
        "outcome": "The query returns the specific HBA rule, confirming that the security configuration is correct and the application can be safely deployed."
      },
      "example": {
        "query": "SELECT line_number, auth_method FROM pg_hba_file_rules WHERE database = 'production_db' AND user_name = 'app_user' AND address = '*************/32';",
        "explanation": "This query filters the HBA rules to find the exact entry for the new application server. Using a `WHERE` clause is far more efficient than manually reading the entire `pg_hba.conf` file. It allows you to programmatically check and audit security rules."
      },
      "notes": [
        "This view only shows the rules that have been loaded by the server. If `pg_hba.conf` was just edited, you may need to run `SELECT pg_reload_conf();` for the changes to appear.",
        "Access to this view is typically restricted to superusers for security reasons.",
        "An empty result means no matching rule was found, and the connection would be denied."
      ]
    }
  ]
};