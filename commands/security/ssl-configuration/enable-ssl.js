export const command0 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "SSL Configuration",
    "Enable SSL"
  ],
  "description": "Enables SSL/TLS encryption for database connections by modifying the server's configuration file. This is the first step in securing data in transit, but it must be followed by a server restart and requires SSL certificates to be in place.",
  "query_template": "ALTER SYSTEM SET ssl = 'on';",
  "components": [
    {
      "component": "ALTER SYSTEM",
      "name": "Alter System Statement",
      "description": "A SQL command used to change server configuration parameters directly. It writes the change to the `postgresql.auto.conf` file, which overrides `postgresql.conf`."
    },
    {
      "component": "SET ssl = 'on'",
      "name": "Enable SSL Parameter",
      "description": "Specifies the configuration parameter to change (`ssl`) and its new value (`on`)."
    }
  ],
  "platformDependent": true, // The follow-up action (restart) is platform-dependent
  "scenarios": [
    {
      "title": "Enforcing Encrypted Connections for a Production Database",
      "details": {
        "setup": "Your company has a new security policy requiring all client-server communication to be encrypted to prevent eavesdropping. The production PostgreSQL server is currently accepting unencrypted connections, and you have already generated and placed the server's SSL certificate and private key files on the server.",
        "goal": "To enable SSL on the PostgreSQL server so it can accept and serve encrypted connections.",
        "outcome": "The server's configuration is updated. After a restart, the PostgreSQL server will support SSL/TLS connections, protecting data in transit."
      },
      "example": {
        "query": "ALTER SYSTEM SET ssl = 'on';",
        "explanation": "This query provides a safe, transactional way to update the `ssl` parameter in the server's configuration. It is often preferred over manually editing `postgresql.conf` because it can be run remotely and reduces the chance of syntax errors in the config file. For this change to take effect, a server restart is mandatory."
      },
      "notes": [
        "**Action Required**: A server restart is **required** for this setting. `SELECT pg_reload_conf();` is not sufficient.",
        "**Prerequisites**: Before running this, you must have `ssl_cert_file` and `ssl_key_file` correctly configured in `postgresql.conf` or via `ALTER SYSTEM`.",
        "This command only *enables* SSL. To *force* all clients to use SSL, you must also edit your `pg_hba.conf` file to change connection types from `host` to `hostssl`.",
        "If the server fails to start after this change, check the PostgreSQL logs for errors related to missing or misconfigured SSL certificate files."
      ]
    },
    {
      "title": "Follow-Up: Restarting the Server to Apply the SSL Configuration",
      "details": {
        "setup": "You have successfully run `ALTER SYSTEM SET ssl = 'on';`.",
        "goal": "To restart the PostgreSQL server so the new SSL setting is loaded and becomes active.",
        "outcome": "The server restarts, and you can now connect using SSL."
      },
      "example": {
        // The 'variants' array shows the platform-specific commands for the required restart
        "variants": [
          {
            "platform": "Linux (systemd)",
            "command": "sudo systemctl restart postgresql",
            "explanation": "The standard method for modern Linux distributions (like Ubuntu, CentOS 7+, Debian 8+) using the systemd service manager."
          },
          {
            "platform": "Windows",
            "command": "net stop postgresql-x64-15 && net start postgresql-x64-15",
            "explanation": "On Windows, PostgreSQL runs as a service. The service name might vary based on your specific PostgreSQL version (e.g., `postgresql-x64-16`)."
          },
          {
            "platform": "Universal (pg_ctl)",
            "command": "pg_ctl restart -D /var/lib/postgresql/15/main",
            "explanation": "The `pg_ctl` utility is part of PostgreSQL and works on any platform, but you must provide the correct path to your database cluster's data directory (`-D`)."
          }
        ]
      },
      "notes": [
        "A restart will temporarily drop all active connections to the database.",
        "Always check the server status after a restart to ensure it came back online successfully."
      ]
    }
  ]
};