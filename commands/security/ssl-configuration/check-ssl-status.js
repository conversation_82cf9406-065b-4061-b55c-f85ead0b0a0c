export const command1 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "SSL Configuration",
    "Check SSL Status"
  ],
  "description": "Checks the server's SSL/TLS configuration and monitors its usage by active client connections.",
  "query_template": "SHOW ssl;",
  "components": [
    {
      "component": "SHOW [parameter]",
      "name": "Show Configuration",
      "description": "A simple SQL command to display the current setting of a server configuration parameter. In this case, it shows if SSL is 'on' or 'off'."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Server-Wide SSL/TLS Configuration",
      "details": {
        "setup": "You are a database administrator hardening a new PostgreSQL instance. You have just edited `postgresql.conf` to set `ssl = on` and restarted the server.",
        "goal": "To confirm that the server process has successfully loaded the new configuration and is now ready to accept encrypted connections.",
        "outcome": "The query returns a value of 'on', confirming that SSL/TLS is enabled at the server level."
      },
      "example": {
        "query": "SHOW ssl;",
        "explanation": "This command directly asks the server for the current value of the 'ssl' parameter. It is the quickest and most direct way to verify if the server's primary SSL setting is active."
      },
      "notes": [
        "If this query returns 'off', check your `postgresql.conf` file for typos and ensure you have restarted (not just reloaded) the PostgreSQL server.",
        "This command only confirms the server is capable of SSL. It does not show if any clients are currently using it."
      ]
    },
    {
      "title": "Auditing Active Connections for Encryption",
      "details": {
        "setup": "As part of a security audit, you must prove that all active connections to the production database are encrypted. You need a real-time report of connection statuses.",
        "goal": "To get a list of all current sessions and explicitly see which ones are secured with SSL/TLS.",
        "outcome": "A table is returned showing details for each connection, including a boolean column that is `true` for encrypted sessions."
      },
      "example": {
        "query": "SELECT pid, datname, usename, client_addr, ssl FROM pg_stat_ssl;",
        "explanation": "The `pg_stat_ssl` view provides a detailed breakdown of SSL usage for every active connection. This query selects key identifying information (process ID, database, user, client IP) along with the `ssl` column, which is true if the connection is encrypted."
      },
      "notes": [
        "If a sensitive connection shows `f` (false) in the `ssl` column, you need to reconfigure that client application's connection string or settings.",
        "This view will only contain information if `ssl` is enabled on the server. If SSL is off, this view will be empty."
      ]
    }
  ]
};