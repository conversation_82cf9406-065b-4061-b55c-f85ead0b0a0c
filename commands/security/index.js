import { command0 } from './ssl-configuration/enable-ssl.js';
import { command1 } from './ssl-configuration/check-ssl-status.js';
import { command2 } from './authentication-methods/view-pg-hba-conf-rules.js';
import { command3 } from './password-security/set-password-policy.js';
import { command4 } from './password-security/force-password-change.js';
import { command5 } from './access-control/audit-login-attempts.js';
import { command6 } from './access-control/grant-connect-privilege.js';
import { command7 } from './data-encryption/enable-transparent-data-encryption.js';
import { command8 } from './data-encryption/column-level-encryption.js';
import { command9 } from './security-auditing/enable-audit-logging.js';
import { command10 } from './security-auditing/view-failed-login-attempts.js';

export const securityCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10
];

export function validateSecurityCommands() {
  return securityCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Security and Authentication";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
