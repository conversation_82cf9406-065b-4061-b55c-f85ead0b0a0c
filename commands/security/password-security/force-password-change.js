export const command4 = {
  "type": "query",
  "path": [
    "Security and Authentication",
    "Password Security",
    "Force Password Change"
  ],
  "description": "Forces a user to change their password by setting an expiration date on their current credentials. This is a key part of database security policy enforcement.",
  "query_template": "ALTER USER <username> [ WITH ] VALID UNTIL '<timestamp>';",
  "components": [
    {
      "component": "ALTER USER username",
      "name": "Target User",
      "description": "The statement that specifies the user account to be modified."
    },
    {
      "component": "PASSWORD 'new_password'",
      "name": "Set Password",
      "description": "Optionally sets a new password for the user. Passwords should be sent as plain text; the server will hash it automatically."
    },
    {
      "component": "VALID UNTIL 'timestamp'",
      "name": "Set Expiration",
      "description": "The core clause for forcing a password change. Once the timestamp is passed, the user must update their password upon the next login."
    }
  ],
  "scenarios": [
    {
      "title": "Onboarding a New Analyst with a Temporary Password",
      "details": {
        "setup": "A new database analyst, 'sara_lee', has joined the team. According to security policy, she must set her own secret password on her first login.",
        "goal": "To provide <PERSON> with a one-time, temporary password that expires immediately, forcing her to create a new one as her first action.",
        "outcome": "Sara can log in using the temporary password, but is immediately met with a `password expired` notice and must change it before she can execute any other queries."
      },
      "example": {
        "query": "ALTER USER sara_lee PASSWORD 'welcome123' VALID UNTIL 'now';",
        "explanation": "This command sets a temporary password and immediately expires it by using `'now'`. When the user connects, PostgreSQL recognizes the expired password and allows the connection but restricts it to only running a `ALTER USER sara_lee PASSWORD 'new_secret_password';` command."
      },
      "notes": [
        "This is the most secure method for user onboarding as it enforces a password change and ensures the administrator never knows the user's final password."
      ]
    },
    {
      "title": "Enforcing a 90-Day Password Rotation Policy",
      "details": {
        "setup": "Your company has implemented a strict security policy requiring all privileged accounts to rotate their passwords every quarter (90 days).",
        "goal": "To update the password for the 'dev_admin' user and ensure it will automatically expire in 90 days, complying with the policy.",
        "outcome": "The 'dev_admin' user has a new password, and the database will automatically enforce a password change when they log in after the 90-day period has passed."
      },
      "example": {
        "query": "ALTER USER dev_admin VALID UNTIL (now() + interval '90 days');",
        "explanation": "This command uses the database's current time (`now()`) and adds a 90-day interval to calculate the precise expiration timestamp. This avoids manual date calculation and makes the policy enforcement scriptable and consistent."
      },
      "notes": [
        "By omitting the `PASSWORD` clause, this command only sets the expiration for the user's *current* password.",
        "Modern PostgreSQL versions handle password encryption automatically. The old `ENCRYPTED PASSWORD` syntax is no longer necessary."
      ]
    }
  ]
};