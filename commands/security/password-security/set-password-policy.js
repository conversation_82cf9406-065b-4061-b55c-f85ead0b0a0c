export const command3 = {
  "path": [
    "Security and Authentication",
    "Password Security",
    "Set Password Policy"
  ],
  "command": "ALTER SYSTEM SET password_encryption = '[method]';",
  "description": "Sets the password encryption method used by the server for newly created or altered user passwords. Using a strong hashing algorithm is a critical security measure to protect user credentials.",
  "parameters": [
    {
      "flag": "ALTER SYSTEM SET",
      "name": "Configuration Command",
      "description": "A SQL command used to change server configuration parameters directly. It writes the setting to the `postgresql.auto.conf` file."
    },
    {
      "flag": "password_encryption = 'scram-sha-256'",
      "name": "Recommended Method",
      "description": "The most secure and modern option, available since PostgreSQL 10. It provides stronger protection against dictionary and brute-force attacks."
    },
    {
      "flag": "password_encryption = 'md5'",
      "name": "Legacy Method",
      "description": "A less secure hashing algorithm that should be avoided in new deployments but may be required for compatibility with very old client drivers."
    }
  ],
  "scenarios": [
    {
      "title": "Upgrading Password Security for a Production Database",
      "details": {
        "setup": "You are managing a PostgreSQL 13 database that was upgraded from an older version. A security audit has flagged the continued use of `md5` for password hashing, which is no longer considered secure.",
        "goal": "To update the server's configuration to use the modern `scram-sha-256` algorithm for all future password creations and updates, bringing the system into compliance with security best practices.",
        "outcome": "The server configuration is persistently updated. After a configuration reload, any user who changes their password, or any new user created, will have their password stored with SCRAM-SHA-256 hashing."
      },
      "example": {
        "command": "ALTER SYSTEM SET password_encryption = 'scram-sha-256';\nSELECT pg_reload_conf();",
        "explanation": "The `ALTER SYSTEM` command writes the setting to the `postgresql.auto.conf` file, making it durable across server restarts. `SELECT pg_reload_conf();` is then executed to apply this setting immediately without requiring a disruptive server restart."
      },
      "notes": [
        "This change is not retroactive. Passwords for existing users will remain in the `md5` format until those users update their passwords.",
        "You can check the current setting with the command: `SHOW password_encryption;`.",
        "After applying this setting, you may want to enforce a password reset for all users to ensure all credentials are moved to the more secure hashing algorithm."
      ]
    }
  ]
};