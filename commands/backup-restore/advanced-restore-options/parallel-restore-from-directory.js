export const command12 = {
  "path": [
    "Backup and Restore",
    "Advanced Restore Options",
    "Parallel Restore from Directory"
  ],
  "command": "pg_restore -U <username> -d <target_database> -j <jobs> <backup_directory>",
  "description": "Restore from a directory format backup using parallel jobs for faster processing. This is the most efficient way to restore large databases from backups created with `pg_dump -F d`.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with restore privileges"
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "Target database to restore into. This database must already exist."
    },
    {
      "flag": "-j",
      "name": "jobs",
      "description": "Number of parallel jobs (should match or be less than backup jobs)"
    },
    {
      "flag": "-v",
      "name": "verbose",
      "description": "Verbose mode for detailed progress information"
    },
    {
      "flag": "-c",
      "name": "clean",
      "description": "Clean (drop) database objects before recreating them"
    },
    {
      "flag": "--if-exists",
      "name": "if-exists",
      "description": "Use IF EXISTS when dropping objects (requires -c to be effective)"
    }
  ],
  "scenarios": [
    {
      "title": "Disaster Recovery: Rapidly Restore a Production Database",
      "details": {
        "setup": "A critical 200GB production database server has failed. A new server has been provisioned, and you have a recent parallel backup (in directory format) located on a network-attached storage volume.",
        "goal": "To perform a full, high-speed restore of the 'ecom_prod' database from the backup directory to the new server, ensuring a clean slate to avoid any data conflicts and minimize application downtime.",
        "outcome": "The 'ecom_prod' database is fully restored and operational on the new server. All data, schemas, indexes, and constraints are recreated successfully, allowing the application to be reconnected."
      },
      "example": {
        "command": "pg_restore -U db_owner -d ecom_prod -j 8 --clean --if-exists /mnt/backups/ecom_prod_backup_2025-09-24",
        "explanation": "This command leverages 8 parallel jobs (`-j 8`) to dramatically accelerate the restoration process. The `--clean` flag ensures that any pre-existing objects in the target database are dropped before the restore begins, which is critical for a clean recovery. Using `--if-exists` prevents errors if an object to be dropped doesn't exist."
      },
      "notes": [
        "The target database ('ecom_prod') must be created before you can run `pg_restore`. You can do this with `createdb -U db_owner ecom_prod`.",
        "For optimal performance, the number of jobs (`-j`) should ideally match the number used during the `pg_dump` backup process.",
        "The user performing the restore (`db_owner`) should be the owner of the target database to avoid permission errors."
      ]
    }
  ]
};