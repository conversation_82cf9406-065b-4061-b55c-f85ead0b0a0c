export const command15 = {
  "path": [
    "Backup and Restore",
    "Advanced Restore Options",
    "Restore with Custom TOC"
  ],
  "command": "pg_restore -U your_user -d target_database -L toc_file.txt backup_file.dump",
  "description": "Restore using a custom table of contents file for selective restoration. This allows you to restore specific database objects (like a single table or schema) from a backup archive.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with restore privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "Target database to restore into."
    },
    {
      "flag": "-L",
      "name": "list-file",
      "description": "Use a table of contents from this file to determine which objects to restore and in what order."
    },
    {
      "flag": "-v",
      "name": "verbose",
      "description": "Specifies verbose mode, which provides detailed progress information during the restore."
    },
    {
      "flag": "--single-transaction",
      "name": "single-transaction",
      "description": "Executes the restore as a single transaction. This ensures that the restore is atomic (either it completes entirely, or no changes are applied)."
    }
  ],
  "scenarios": [
    {
      "title": "Restoring a Single Dropped Table Without a Full Database Restore",
      "details": {
        "setup": "You are managing a large production database. A developer accidentally dropped the 'public.customers' table. You have a full nightly backup created with `pg_dump -Fc`, but restoring the entire database would cause significant downtime and overwrite hours of new transactions in other tables.",
        "goal": "To restore only the 'public.customers' table (including its structure, data, and indexes) from the backup file `prod_db_nightly.dump` into the live database.",
        "outcome": "The 'public.customers' table is successfully restored to its state at the time of the backup, with all other data in the database left untouched. The application can now access customer data again."
      },
      "example": {
        "command": "## This is a multi-step process:\n\n# Step 1: Generate the Table of Contents (TOC) file from your backup.\npg_restore -l prod_db_nightly.dump > restore_plan.toc\n\n# Step 2: Edit the 'restore_plan.toc' file.\n# Open it in a text editor and comment out (with a ';') all lines except those\n# related to the 'public.customers' table (e.g., TABLE, TABLE DATA, PRIMARY KEY).\n\n# Step 3: Perform the selective restore using the edited TOC file.\npg_restore -U db_admin -d production_db --single-transaction -L restore_plan.toc prod_db_nightly.dump",
        "explanation": "This three-step process provides precise control over a restore. First, `pg_restore -l` lists the backup's contents. By saving this list to a file, you create a restore plan. Editing this file lets you specify exactly what to restore. Finally, `pg_restore -L` executes this plan, ensuring only the desired objects are recovered, which is invaluable for surgical fixes in a live environment."
      },
      "notes": [
        "This method only works with custom-format (`-Fc`) or directory-format (`-Fd`) backups from `pg_dump`.",
        "The table of contents file lists objects with dependencies. Be careful not to comment out an object that another object depends on (e.g., the schema for the table).",
        "Using `--single-transaction` is a best practice as it guarantees that the selective restore will not leave the database in a partially restored state if an error occurs."
      ]
    }
  ]
}