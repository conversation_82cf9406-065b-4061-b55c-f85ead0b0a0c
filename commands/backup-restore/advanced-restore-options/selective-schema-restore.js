export const command13 = {
  "path": [
    "Backup and Restore",
    "Advanced Restore Options",
    "Selective Schema Restore"
  ],
  "command": "pg_restore -U <username> -d <target_database> -n <schema_name> <backup_file>",
  "description": "Restores only the objects (tables, functions, etc.) that belong to a specific schema from a custom-format or directory-format backup file. This is ideal for isolating data or refreshing a specific component of a larger database.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with restore privileges"
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "Target database to restore into. This database must already exist."
    },
    {
      "flag": "-n",
      "name": "schema",
      "description": "Specifies the schema to restore. Only objects within this schema will be processed."
    },
    {
      "flag": "-t",
      "name": "table",
      "description": "Restore only the named table(s). Can be used with -n to restore a specific table from a specific schema."
    },
    {
      "flag": "-I",
      "name": "index",
      "description": "Restore only the named index."
    },
    {
      "flag": "-T",
      "name": "trigger",
      "description": "Restore only the named trigger."
    }
  ],
  "scenarios": [
    {
      "title": "Refresh a Microservice's Data in a Staging Environment",
      "details": {
        "setup": "You manage a large production database that consolidates data from multiple microservices, each in its own schema (e.g., `users`, `products`, `orders`). A developer needs a fresh copy of the entire `products` schema to test a new feature in the `staging_db` environment.",
        "goal": "To completely replace the `products` schema in `staging_db` with the version from the latest production backup, without affecting any other schemas in the staging database.",
        "outcome": "The `products` schema and all its tables, data, and related objects are successfully restored into `staging_db` from the production backup. Other schemas like `users` and `orders` remain untouched."
      },
      "example": {
        "command": "pg_restore -U admin -d staging_db --clean -n products /backups/prod_db-latest.dump",
        "explanation": "This command targets the `staging_db` database and uses the `-n products` flag to restore only the objects within the `products` schema. The `--clean` flag is included to drop any existing schema objects before recreating them, ensuring a fresh start. This isolates the data refresh operation to only the relevant microservice."
      },
      "notes": [
        "This command requires a backup file created with `pg_dump` using the custom (`-Fc`) or directory (`-Fd`) format. It will not work with plain-text SQL dumps.",
        "The target database (`staging_db` in this case) must exist before you run the command. `pg_restore` populates the database but does not create it unless you also use the `-C` flag.",
        "To avoid errors, ensure no active connections are using the schema being restored in the target database, especially when using `--clean`."
      ]
    }
  ]
};