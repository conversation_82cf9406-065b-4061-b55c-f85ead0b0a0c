export const command14 = {
  "path": [
    "Backup and Restore",
    "Advanced Restore Options",
    "List Backup Contents"
  ],
  "command": "pg_restore -l <backup_file>",
  "description": "List the contents of a backup file without performing a restore. This is crucial for planning a selective or partial restoration.",
  "parameters": [
    {
      "flag": "-l, --list",
      "name": "list",
      "description": "Lists the table of contents of the archive."
    },
    {
      "flag": "-v, --verbose",
      "name": "verbose",
      "description": "Specifies verbose mode, which will output more detailed object comments and start/stop times to the output file."
    },
    {
      "flag": "-f, --file=<filename>",
      "name": "file",
      "description": "Specifies an output file for the generated script, or for the listing when used with -l."
    }
  ],
  "scenarios": [
    {
      "title": "Planning a Selective Restore of a Dropped Table",
      "details": {
        "setup": "A developer has accidentally dropped a critical table, `public.api_keys`, from the production database. A full database restore is not feasible as it would cause significant data loss for other tables that have been updated since the last backup.",
        "goal": "To safely verify that the `public.api_keys` table exists in last night's backup file before attempting to restore only that specific table.",
        "outcome": "The command prints a table of contents from the backup file, confirming the presence and schema of the `public.api_keys` table, allowing the DBA to proceed with a targeted restore."
      },
      "example": {
        "command": "pg_restore -l /backups/prod_db-daily.dump > backup_contents.txt",
        "explanation": "This command reads the metadata from `prod_db-daily.dump` and lists all archived items (tables, indexes, functions, etc.). By redirecting the output to `backup_contents.txt`, you create a file that can be easily searched (e.g., using `grep api_keys backup_contents.txt`) to confirm the table is available for recovery."
      },
      "notes": [
        "This command works with custom-format (`-Fc`) and directory-format (`-Fd`) dumps created by `pg_dump`.",
        "For a large backup, always redirect the output to a file for easier analysis.",
        "The generated list can be edited and then used with the `--use-list` (`-L`) flag of `pg_restore` to perform a highly selective restore of specific items."
      ]
    }
  ]
};