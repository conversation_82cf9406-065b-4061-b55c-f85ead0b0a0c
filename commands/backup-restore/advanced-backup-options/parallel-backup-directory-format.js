export const command8 = {
  "path": [
    "Backup and Restore",
    "Advanced Backup Options",
    "Parallel Backup (Directory Format)"
  ],
  "command": "pg_dump -U <username> -d <database> -j <jobs> -F d -f <backup_directory>",
  "description": "Performs a high-speed parallel backup of a database using multiple concurrent jobs. This method is ideal for very large databases as it significantly reduces backup time. It outputs to a directory format, which is required for parallel dumps and must be restored using pg_restore.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with backup privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The database to be backed up."
    },
    {
      "flag": "-j",
      "name": "jobs",
      "description": "Number of parallel jobs to run. A good starting point is the number of CPU cores on the database server."
    },
    {
      "flag": "-F d",
      "name": "format",
      "description": "Specifies the output format as a directory. This is mandatory for parallel backups."
    },
    {
      "flag": "-f",
      "name": "output",
      "description": "The path to the output directory where the backup files will be stored. The directory will be created if it does not exist."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host or IP address (default: local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)."
    }
  ],
  "scenarios": [
    {
      "title": "Minimizing Downtime with a High-Speed Production Backup",
      "details": {
        "setup": "You are a database administrator for a rapidly growing SaaS application. The main production database is now over 500GB, and the traditional nightly backup process is taking over four hours, threatening to overlap with peak business hours.",
        "goal": "To reduce the database backup time to under 90 minutes to ensure it completes well within the designated 2-hour maintenance window.",
        "outcome": "A complete, consistent backup of the production database is successfully created in a dedicated directory, with the entire process finishing in approximately 75 minutes."
      },
      "example": {
        "command": "pg_dump -U backup_user -h prod-db.example.com -d app_prod -j 8 -F d -f /mnt/backups/app_prod_$(date +%Y-%m-%d)",
        "explanation": "This command connects to the production database 'app_prod' as 'backup_user'. It utilizes 8 parallel jobs (-j 8) to concurrently export data. The directory format (-F d) is specified as required, and the output is directed to a dynamically named directory that includes the current date (e.g., /mnt/backups/app_prod_2025-09-24), making backups easy to organize."
      },
      "notes": [
        "The number of jobs (-j) should be chosen carefully; setting it higher than the number of available CPU cores can sometimes slow down the process due to context switching.",
        "This backup method generates significant I/O on the server. Monitor disk performance during the backup.",
        "A backup created in the directory format must be restored using the `pg_restore` utility, as it is not a plain-text SQL file.",
        "Ensure the file system has enough space for the backup directory, which can be roughly the size of the database."
      ]
    }
  ]
};