export const command9 = {
  "path": [
    "Backup and Restore",
    "Advanced Backup Options",
    "Schema-Only Backup"
  ],
  "command": "pg_dump -U <username> -d <database> -s -f <output_file.sql>",
  "description": "Create a backup containing only the database schema (tables, indexes, functions, etc.) without any data. This is ideal for replicating database structures or version controlling your schema.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with backup privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The database whose schema you want to back up."
    },
    {
      "flag": "-s",
      "name": "schema-only",
      "description": "Specifies that only the object definitions (the schema) should be dumped, not the data."
    },
    {
      "flag": "-f",
      "name": "output file",
      "description": "The path and name for the output SQL file."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (defaults to local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (defaults to 5432)."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a Staging Environment from a Production Schema",
      "details": {
        "setup": "Your development team needs a new staging environment to test an upcoming feature. This requires a database with a structure identical to production, but without containing any sensitive or large-volume production data.",
        "goal": "To quickly create an empty database on the staging server that perfectly mirrors the production database's tables, views, functions, and roles.",
        "outcome": "A new, empty database is created on the staging server, ready for developers to populate with controlled test data."
      },
      "example": {
        "command": "pg_dump -U db_admin -h prod-db.example.com -d main_app_prod -s -f /backups/schema_prod.sql",
        "explanation": "This command connects to the production database (`main_app_prod`) and exports only its schema definitions using the `-s` flag. The resulting SQL file can be executed on the staging server to replicate the entire structure without copying a single row of data, making the process fast and secure."
      },
      "notes": [
        "After creating the schema dump, you can restore it to a new database using `psql -d new_staging_db -f /backups/schema_prod.sql`.",
        "This method is significantly faster and more secure than performing a full database dump for creating new development or testing environments."
      ]
    },
    {
      "title": "Tracking Database Schema Changes in Version Control",
      "details": {
        "setup": "A team is actively developing an application, and multiple developers are making changes to the database. They need a reliable way to track, review, and manage these schema modifications over time.",
        "goal": "To store the database schema as a text file in a Git repository. This allows the team to view diffs, review changes in pull requests, and revert to previous versions if a problem arises.",
        "outcome": "A `schema.sql` file is committed to the repository, providing a clear, text-based history of all changes made to the database structure."
      },
      "example": {
        "command": "pg_dump -U dev_user -d my_app_dev -s --no-owner --no-privileges -f db/schema.sql",
        "explanation": "The `-s` flag dumps the schema. Using `--no-owner` and `--no-privileges` is critical here because it removes user-specific metadata from the dump file. This prevents irrelevant changes in the 'diff' when the schema is dumped by different users, keeping the version history clean and focused on actual structural changes."
      },
      "notes": [
        "Consider automating this command in a CI/CD pipeline or a Git pre-commit hook to ensure the schema file is always kept up-to-date with the latest changes.",
        "Reviewing schema diffs in pull requests helps catch potential design flaws or breaking changes before they are merged and deployed."
      ]
    }
  ]
}