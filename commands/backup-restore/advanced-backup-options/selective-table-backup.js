export const command11 = {
  "path": [
    "Backup and Restore",
    "Advanced Backup Options",
    "Selective Table Backup"
  ],
  "command": "pg_dump -U your_user -d your_database -t table_name -f table_backup.sql",
  "description": "Backs up only one or more specific tables from a database. This is useful for creating small, targeted backups before performing risky operations on a subset of data.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with backup privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The database containing the table(s)."
    },
    {
      "flag": "-t",
      "name": "table",
      "description": "The name of the table to back up. This flag can be used multiple times to include multiple tables."
    },
    {
      "flag": "-f",
      "name": "output file",
      "description": "The output file name for the table backup."
    },
    {
      "flag": "-n",
      "name": "schema",
      "description": "Backs up all tables within a specified schema. Can be combined with -t."
    },
    {
      "flag": "--exclude-table",
      "name": "exclude-table",
      "description": "Excludes specific tables from the backup. Cannot be used with -t."
    }
  ],
  "scenarios": [
    {
      "title": "Archiving User Data Before a Risky Application Migration",
      "details": {
        "setup": "Your team is about to deploy a new version of an application that includes a major migration script for the user authentication system. The production database is large, but only the `users` and `user_profiles` tables will be affected.",
        "goal": "To create a fast, isolated backup of only the `users` and `user_profiles` tables as a safety net. If the migration fails, you need to be able to restore just this data quickly without performing a full database recovery.",
        "outcome": "A single SQL file, `user_data_pre_migration.sql`, is created. This file contains the complete schema and data for the specified tables, allowing for a quick and targeted restore."
      },
      "example": {
        "command": "pg_dump -U admin_user -d prod_db -t users -t user_profiles -f user_data_pre_migration.sql",
        "explanation": "This command targets the `prod_db` database and uses the `-t` flag twice to explicitly include both the `users` and `user_profiles` tables. The output is a clean SQL script, making it a perfect lightweight backup for this specific, high-risk operation."
      },
      "notes": [
        "The `-t` flag accepts wildcard patterns. For example, `pg_dump -t 'sales_*'` would back up all tables starting with 'sales_'.",
        "This type of backup does not include related objects like sequences unless they are owned by the table. For a complete functional restore, you may need to back up other related objects separately.",
        "Consider using the `--clean` flag with `pg_dump` to include `DROP TABLE` statements in the backup, which simplifies the restore process by first removing the old tables."
      ]
    }
  ]
}