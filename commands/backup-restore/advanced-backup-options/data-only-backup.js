export const command10 = {
  "path": [
    "Backup and Restore",
    "Advanced Backup Options",
    "Data-Only Backup"
  ],
  "command": "pg_dump -U your_user -d your_database -a -f data_only_backup.sql",
  "description": "Create a backup containing only the data (as INSERT or COPY statements) without any of the schema definitions (like CREATE TABLE).",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "PostgreSQL user with backup privileges"
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "Database to back up"
    },
    {
      "flag": "-a",
      "name": "data-only",
      "description": "Dump only the data, not the schema"
    },
    {
      "flag": "-f",
      "name": "output file",
      "description": "Output file name for the data backup"
    },
    {
      "flag": "--column-inserts",
      "name": "column-inserts",
      "description": "Use INSERT commands with explicit column names (more readable and robust but can be larger)"
    },
    {
      "flag": "--inserts",
      "name": "inserts",
      "description": "Use INSERT commands instead of the default COPY command (slower but more portable across different PostgreSQL versions or other databases)"
    }
  ],
  "scenarios": [
    {
      "title": "Refreshing a Staging Database with Production Data",
      "details": {
        "setup": "You have a production database ('prod_analytics') and a staging database ('staging_analytics'). The schemas are identical, but the staging data is stale. You need to update the staging environment with fresh data for accurate testing.",
        "goal": "To export all data from the production database and load it into the staging database without altering the existing schema, roles, or permissions on the staging server.",
        "outcome": "A SQL file containing only the data from 'prod_analytics' is generated. After loading it, the 'staging_analytics' database has an exact copy of the production data, ready for development and QA."
      },
      "example": {
        "command": "pg_dump -U db_admin -d prod_analytics -a --column-inserts -f /backups/prod_data_refresh.sql",
        "explanation": "This command specifically dumps only the data (-a) from the 'prod_analytics' database. Using '--column-inserts' makes the resulting SQL file more robust against column order differences and easier to debug. The output is saved to 'prod_data_refresh.sql'."
      },
      "notes": [
        "Before importing the data into the staging database, you typically need to clear the old data using `TRUNCATE` on the relevant tables.",
        "To import the data, you would use a command like: `psql -U db_admin -d staging_analytics -f /backups/prod_data_refresh.sql`.",
        "This method is ideal when you want to preserve the existing ownership and permissions of the schema in the target database."
      ]
    }
  ]
}
