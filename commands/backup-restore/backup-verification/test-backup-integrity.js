export const command16 = {
  "path": [
    "Backup and Restore",
    "Backup Verification",
    "Test Backup Integrity"
  ],
  "command": "pg_restore -l <backup_file>",
  "description": "Tests if a backup file is readable and not corrupted by listing its contents without actually restoring any data. It's a quick first-pass check for backup integrity.",
  "parameters": [
    {
      "flag": "-l",
      "name": "list",
      "description": "Lists the contents of the backup file. This process reads through the archive, effectively testing its structural integrity."
    },
    {
      "flag": "> /dev/null or > NUL",
      "name": "redirect",
      "description": "Discards the normal output (the list of contents), ensuring you only see error messages if the file is corrupted."
    },
    {
      "flag": "-v",
      "name": "verbose",
      "description": "Provides more detailed output during the process, which can help diagnose specific issues."
    }
  ],
  "platformDependent": true,
  "scenarios": [
    {
      "title": "Verifying a Nightly Production Backup Before Archiving",
      "details": {
        "setup": "Your automated system has just created a nightly backup of a critical 50GB production database. Before this file is copied to long-term cloud storage, you need a quick way to validate that the backup process completed successfully and the file isn't corrupt.",
        "goal": "To confirm the integrity of the backup file `prod_main_2025-09-24.dump` without performing a time-consuming full restore.",
        "outcome": "The command finishes silently with no errors, giving you high confidence that the backup file is structurally sound and ready for archival."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux / macOS",
            "command": "pg_restore -l /var/lib/postgresql/backups/prod_main_2025-09-24.dump > /dev/null",
            "explanation": "This command attempts to read the table of contents from the backup file. By redirecting the standard output to the null device (`/dev/null`), we suppress the content list itself. The command's success is determined by its exit code; if it runs silently, the backup is considered readable."
          },
          {
            "platform": "Windows",
            "command": "pg_restore -l \"C:\\Program Files\\PostgreSQL\\15\\backups\\prod_main_2025-09-24.dump\" > NUL",
            "explanation": "This performs the same integrity check on Windows. `NUL` is the Windows equivalent of `/dev/null`, discarding the output so that only error messages, if any, are displayed. The file path is quoted to handle spaces."
          }
        ]
      },
      "notes": [
        "This check confirms the backup file is readable and its format is correct. It does not guarantee the logical consistency of the data within.",
        "For mission-critical data, the most thorough verification is a full restore onto a separate, non-production server.",
        "If the command outputs any errors, the backup should be considered failed and investigated immediately."
      ]
    }
  ]
};