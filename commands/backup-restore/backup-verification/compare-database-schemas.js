export const command17 = {
  "path": [
    "Backup and Restore",
    "Backup Verification",
    "Compare Database Schemas"
  ],
  "command": "pg_dump -s db1 | diff - <(pg_dump -s db2)",
  "description": "Compares the schemas of two databases by dumping them and using a diff utility. This is essential for verifying migrations, replications, or restore operations.",
  "parameters": [
    {
      "flag": "-s",
      "name": "schema-only",
      "description": "Instructs pg_dump to dump only the object definitions (the schema), not the data."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The name of the database to dump."
    },
    {
      "flag": "diff",
      "name": "diff command",
      "description": "A standard Unix utility that compares two text streams line by line."
    },
    {
      "flag": "<()",
      "name": "process substitution",
      "description": "A shell feature (bash/zsh) that treats the output of a command as a temporary file."
    }
  ],
  "platformDependent": true,
  "scenarios": [
    {
      "title": "Verifying Schema Consistency After a Production to Staging Restore",
      "details": {
        "setup": "Your team has just restored a backup of the 'prod_db' into the 'staging_db' environment. This is a critical step to ensure that staging accurately reflects the production environment before testing new application features.",
        "goal": "To programmatically verify that the schema in 'staging_db' is an exact replica of 'prod_db', ensuring no tables, functions, indexes, or other objects were missed or altered during the restore process.",
        "outcome": "The command produces no output, confirming the schemas are identical. If differences were found, they would be printed, immediately highlighting the inconsistencies that need to be addressed."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux / macOS (bash/zsh)",
            "command": "pg_dump -s -d prod_db | diff - <(pg_dump -s -d staging_db)",
            "explanation": "This is the most efficient method. It streams the schema dumps directly into the `diff` command without creating temporary files, comparing them in memory. The process substitution `<()` is key to this efficiency."
          },
          {
            "platform": "Windows (Command Prompt)",
            "command": "pg_dump -s -d prod_db > prod_schema.sql\r\npg_dump -s -d staging_db > staging_schema.sql\r\nfc prod_schema.sql staging_schema.sql",
            "explanation": "The native Windows Command Prompt does not support process substitution. The standard approach is to dump each schema to a separate SQL file and then use the built-in File Compare (`fc`) utility to check for differences."
          },
          {
            "platform": "Cross-Platform (using temp files)",
            "command": "pg_dump -s -d prod_db > prod_schema.sql\r\npg_dump -s -d staging_db > staging_schema.sql\r\ndiff prod_schema.sql staging_schema.sql",
            "explanation": "This method is universal and works on any system where a `diff` utility is available (e.g., via Git for Windows). It's more explicit than the bash version but achieves the same result by saving the schemas to disk first."
          }
        ]
      },
      "notes": [
        "For this command, silence is golden: no output means the schemas are identical.",
        "This comparison can also be useful for identifying unintended schema changes before deploying new code.",
        "For a more visual and user-friendly comparison, GUI tools like pgAdmin and DBeaver have built-in schema diff features."
      ]
    }
  ]
};