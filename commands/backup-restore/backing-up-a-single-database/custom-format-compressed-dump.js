export const command1 = {
  "path": [
    "Backup and Restore",
    "Backing Up a Single Database",
    "Custom Format (Compressed) Dump"
  ],
  "command": "pg_dump -U your_user -d your_database -F c -f your_database_backup.dump",
  "description": "Creates a compressed binary file (.dump) that is smaller than plain-text and more flexible for restoring. This format allows you to selectively restore parts of the database (like specific tables) and is generally more resilient to errors during restoration. Recommended for most production backups.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The name of the database you want to back up. Replace 'your_database' with the actual database name."
    },
    {
      "flag": "-F c",
      "name": "format",
      "description": "Specifies the output format. 'c' stands for custom format, which is compressed and flexible."
    },
    {
      "flag": "-f",
      "name": "output file",
      "description": "Specifies the output file name. Replace 'your_database_backup.dump' with your desired backup filename (typically ends with .dump)."
    }
  ],
  "scenarios": [
    {
      "title": "Routine Backup of a Production Application Database",
      "details": {
        "setup": "You are the database administrator for a web application with a 50GB PostgreSQL database named 'webapp_prod'. A nightly backup is required for disaster recovery purposes.",
        "goal": "To create a space-efficient, reliable, and restorable backup of the 'webapp_prod' database and store it on a dedicated backup volume.",
        "outcome": "A single compressed backup file named 'webapp_prod_2025-09-24.dump' is successfully created in the '/mnt/backups/postgres' directory."
      },
      "example": {
        "command": "pg_dump -U backup_user -d webapp_prod -F c -f /mnt/backups/postgres/webapp_prod_2025-09-24.dump",
        "explanation": "This command uses the custom format (-F c), which creates a compressed binary file, saving significant disk space compared to a plain SQL dump. This format is also required to be used with `pg_restore`, which allows for parallel restoration and selective object restoration (e.g., a single table)."
      },
      "notes": [
        "The restoration of this backup file must be done using the `pg_restore` utility.",
        "For automation, this command is typically added to a shell script and scheduled to run daily using a cron job (on Linux) or Task Scheduler (on Windows).",
        "Best practice is to store backups on a separate physical server, network share, or cloud storage to protect against server failure."
      ]
    }
  ]
}