export const command0 = {
  "path": [
    "Backup and Restore",
    "Backing Up a Single Database",
    "Plain-Text SQL Dump"
  ],
  "command": "pg_dump -U your_user -d your_database > your_database_backup.sql",
  "description": "This method creates a human-readable .sql file containing SQL commands to recreate the database. Ideal for small databases, version control, or when you need to edit the backup file before restoring. The file will contain CREATE TABLE statements, INSERT statements, and other SQL commands.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with. Replace 'your_user' with your actual PostgreSQL username that has backup privileges."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The name of the database you want to back up. Replace 'your_database' with the actual database name."
    },
    {
      "flag": ">",
      "name": "output file",
      "description": "Shell redirection operator that saves the output to a file. Replace 'your_database_backup.sql' with your desired backup filename."
    }
  ],
  "scenarios": [
    {
      "title": "Creating a Version-Controlled Schema and Data Backup for a Development Project",
      "details": {
        "setup": "You are a developer working on a new feature for a web application. You've just finalized several schema changes and added new seed data in your local 'dev_webapp' database.",
        "goal": "To create a complete, human-readable backup of the database. This file will be committed to a Git repository to share the updated schema and data with the rest of the team, allowing them to easily replicate the development environment.",
        "outcome": "A file named 'dev_webapp_dump_v2.sql' is created. This text file contains all the SQL necessary to drop and recreate the database objects and their data, making it easy to see the changes in a git diff."
      },
      "example": {
        "command": "pg_dump -U app_developer -d dev_webapp > dev_webapp_dump_v2.sql",
        "explanation": "This command connects as the 'app_developer' user to the 'dev_webapp' database and pipes the entire SQL output into a single .sql file. The plain-text format is ideal for version control systems as it allows for clear, line-by-line comparisons (diffs) of schema and data changes between commits."
      },
      "notes": [
        "This method is best suited for small to medium-sized databases. For larger databases, consider using a custom or directory format (`-Fc` or `-Fd`) for better performance and flexibility.",
        "The backup file will not include roles or tablespace information. Use `pg_dumpall --globals-only` for that.",
        "To automate this without password prompts, consider using a `.pgpass` file in your home directory."
      ]
    }
  ]
}