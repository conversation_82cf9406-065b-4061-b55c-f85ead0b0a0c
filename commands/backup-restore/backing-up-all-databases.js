export const command2 = {
  "path": [
    "Backup and Restore",
    "Backing Up All Databases"
  ],
  "command": "pg_dumpall -U postgres > output.sql",
  "description": "Create a backup of the entire PostgreSQL cluster including all databases, users, and tablespace definitions. This is crucial for a full server restore as it backs up global objects like roles and permissions that pg_dump ignores.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with. Usually 'postgres' for cluster-wide operations."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host or socket directory (default: local socket)"
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port number (default: 5432)"
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Force password prompt (should happen automatically)"
    },
    {
      "flag": "--clean",
      "name": "clean",
      "description": "Include commands to clean (drop) databases before recreating them"
    },
    {
      "flag": "--if-exists",
      "name": "if-exists",
      "description": "Use IF EXISTS when dropping objects (requires --clean)"
    },
    {
      "flag": ">",
      "name": "output file",
      "description": "Shell redirection operator that saves the output to a file"
    }
  ],
  "scenarios": [
    {
      "title": "Full Cluster Backup for Disaster Recovery",
      "details": {
        "setup": "You are the database administrator responsible for a PostgreSQL server that hosts multiple applications, each with its own database and set of user roles. You need a comprehensive backup strategy that allows for a complete server restoration on new hardware.",
        "goal": "To create a single backup file that contains all data from all databases, as well as all global objects like user roles, groups, and tablespace definitions.",
        "outcome": "A text file containing SQL commands is generated. This file can be used with `psql` to completely rebuild the PostgreSQL cluster's state on a freshly installed server."
      },
      "example": {
        "command": "pg_dumpall -U postgres > /mnt/backups/pg_cluster_full_$(date +%F).sql",
        "explanation": "This command connects as the superuser 'postgres' and dumps the entire cluster. The `>` operator redirects the output to a dynamically named .sql file in a dedicated backup directory. The output is a plain-text SQL script."
      },
      "notes": [
        "The output of pg_dumpall is restored using `psql`, not `pg_restore`.",
        "For very large databases, this plain-text backup can be significantly larger and slower to restore than the custom formats available with `pg_dump`.",
        "This is the only standard method to back up global objects like roles and tablespaces."
      ]
    },
    {
      "title": "Migrating a PostgreSQL Cluster to a New Server",
      "details": {
        "setup": "An old server is being decommissioned, and you need to move the entire PostgreSQL instance, including all databases and users, to a new, more powerful machine. You want to perform this migration over the network without creating a large intermediate file on disk.",
        "goal": "To efficiently transfer all data and global objects from the source server to the destination server.",
        "outcome": "The new server has a complete and identical copy of the databases, roles, and permissions from the old server."
      },
      "example": {
        "command": "pg_dumpall -U postgres -h old_server_ip | psql -U postgres -h new_server_ip",
        "explanation": "This command chain performs the migration in one step. `pg_dumpall` connects to the old server and streams its SQL output via a pipe (`|`) directly to the `psql` client, which connects to the new server and executes the script, recreating the cluster."
      },
      "notes": [
        "This method is excellent for migrating between PostgreSQL major versions (e.g., from v12 to v15).",
        "Ensure network connectivity and proper authentication (e.g., `.pgpass` file) are configured between the servers.",
        "For multi-terabyte databases, dumping to a file first might be safer to allow for recovery from network interruptions."
      ]
    }
  ]
}