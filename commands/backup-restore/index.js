import { command0 } from './backing-up-a-single-database/plain-text-sql-dump.js';
import { command1 } from './backing-up-a-single-database/custom-format-compressed-dump.js';
import { command2 } from './backing-up-all-databases.js';
import { command3 } from './restoring-a-database/from-a-plain-text-sql-dump/create-a-new-database.js';
import { command4 } from './restoring-a-database/from-a-plain-text-sql-dump/import-the-backup.js';
import { command5 } from './restoring-a-database/from-a-custom-format-dump/create-a-new-database.js';
import { command6 } from './restoring-a-database/from-a-custom-format-dump/restore-with-pg-restore.js';
import { command7 } from './restoring-a-database/from-a-pg-dumpall-backup.js';
import { command8 } from './advanced-backup-options/parallel-backup-directory-format.js';
import { command9 } from './advanced-backup-options/schema-only-backup.js';
import { command10 } from './advanced-backup-options/data-only-backup.js';
import { command11 } from './advanced-backup-options/selective-table-backup.js';
import { command12 } from './advanced-restore-options/parallel-restore-from-directory.js';
import { command13 } from './advanced-restore-options/selective-schema-restore.js';
import { command14 } from './advanced-restore-options/list-backup-contents.js';
import { command15 } from './advanced-restore-options/restore-with-custom-toc.js';
import { command16 } from './backup-verification/test-backup-integrity.js';
import { command17 } from './backup-verification/compare-database-schemas.js';

export const backupRestoreCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15,
  command16,
  command17
];

export function validateBackupRestoreCommands() {
  return backupRestoreCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Backup and Restore";
    const hasDescription = cmd.description;
    const type = cmd.type || 'command'; // Default to 'command' for backwards compatibility

    if (!hasPath || !hasDescription) {
      return false;
    }

    switch (type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
