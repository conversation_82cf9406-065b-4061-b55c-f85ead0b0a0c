export const command4 = {
  "path": [
    "Backup and Restore",
    "Restoring a Database",
    "From a Plain-Text SQL Dump",
    "Import the Backup"
  ],
  "command": "psql -U <username> -d <database> < your_database_backup.sql",
  "description": "Restores a database from a plain-text SQL dump file by executing the SQL commands within it. This method is straightforward but less flexible for large databases compared to custom-format archives.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with."
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The target database name to restore into. This database must exist prior to running the command."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (default: local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)."
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Force a password prompt before connecting."
    },
    {
      "flag": "<",
      "name": "input file",
      "description": "Shell redirection operator that reads from the specified backup file and pipes its content to psql."
    }
  ],
  "scenarios": [
    {
      "title": "Cloning a Production Database for a Staging Environment",
      "details": {
        "setup": "You are a developer tasked with troubleshooting a bug that only appears with production data. You have received a `prod_db_dump.sql` file, which is a plain-text backup of the production database. You have already created a new, empty database named 'staging_db' on your local server.",
        "goal": "To populate the 'staging_db' with the exact schema and data from the production backup to create a safe and realistic environment for debugging.",
        "outcome": "The 'staging_db' is successfully populated. You can now connect to it and replicate the bug without affecting the live production system."
      },
      "example": {
        "command": "psql -U my_user -d staging_db < /path/to/backups/prod_db_dump.sql",
        "explanation": "This command connects to the 'staging_db' database as 'my_user'. The shell's input redirection operator (`<`) feeds the contents of the `.sql` file to the `psql` client, which then executes all the SQL statements (CREATE TABLE, INSERT, etc.) contained within the backup file."
      },
      "notes": [
        "The target database ('staging_db' in this case) must be created before you can run this restore command.",
        "For critical restores, consider adding the `-1` or `--single-transaction` flag. This wraps the entire restore process in a single transaction, ensuring that if any command fails, the entire operation is rolled back, leaving the target database untouched.",
        "If the backup file contains commands to drop and recreate the database, you might connect to the default 'postgres' database instead to run the script."
      ]
    }
  ]
};