export const command3 = {
  "path": [
    "Backup and Restore",
    "Restoring a Database",
    "From a Plain-Text SQL Dump",
    "Create a New Database"
  ],
  "command": "createdb -U <username> <new_database_name>",
  "description": "Creates a new, empty PostgreSQL database. This is a required first step before importing data from a SQL dump file into a new database.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user who will own the new database."
    },
    {
      "flag": "new_database_name",
      "name": "database name",
      "description": "The name of the new database to create."
    }
  ],
  "scenarios": [
    {
      "title": "Staging Environment Setup for a Web Application",
      "details": {
        "setup": "A development team needs to test new features using a recent copy of the production data. A new, isolated database environment must be created for this purpose.",
        "goal": "To create a new database named 'webapp_staging' that will be owned by the 'dev_team' user, making it ready for a data import.",
        "outcome": "An empty database named 'webapp_staging' is created on the server, and the 'dev_team' user is set as its owner."
      },
      "example": {
        "command": "createdb -U dev_team webapp_staging",
        "explanation": "This command creates the database 'webapp_staging' and assigns ownership to the 'dev_team' user via the -U flag. Assigning ownership is critical for managing permissions correctly in a non-production environment."
      },
      "notes": [
        "The user executing this command must have the CREATEDB privilege or be a PostgreSQL superuser.",
        "This command should be run before using `psql` to import the .sql backup file.",
        "If the -U flag is omitted, the database will be owned by the operating system user running the command."
      ]
    }
  ]
};