export const command7 = {
  "path": [
    "Backup and Restore",
    "Restoring a Database",
    "From a pg_dumpall Backup"
  ],
  "command": "psql -U postgres -f <backup_file.sql>",
  "description": "Restores a full PostgreSQL cluster from a plain-text SQL backup file created by pg_dumpall. This single file contains all the necessary commands to recreate roles, tablespaces, databases, and data.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with (usually 'postgres' for cluster operations)."
    },
    {
      "flag": "-f",
      "name": "file",
      "description": "The path to the SQL backup file to be executed."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (default: local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)."
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Force a password prompt before connecting."
    },
    {
      "flag": "-v",
      "name": "variable",
      "description": "Set a psql variable, such as ON_ERROR_STOP=1 to halt on any error."
    }
  ],
  "scenarios": [
    {
      "title": "Full Cluster Restoration for Disaster Recovery",
      "details": {
        "setup": "A complete server failure has resulted in the loss of your PostgreSQL instance. You have a fresh, clean installation of PostgreSQL on a new server and a full cluster backup file named 'cluster_backup_2025-09-24.sql' generated by `pg_dumpall`.",
        "goal": "To restore the entire PostgreSQL cluster to the new server, including all users (roles), tablespaces, and databases, exactly as they existed at the time of the backup.",
        "outcome": "The new PostgreSQL server is fully populated with all roles and databases from the backup file. Applications can connect, and normal operations are restored."
      },
      "example": {
        "command": "psql -U postgres -v ON_ERROR_STOP=1 -f /mnt/backups/cluster_backup_2025-09-24.sql",
        "explanation": "This command connects to the new instance as the superuser (`-U postgres`) and executes the SQL script from the backup file. The script itself contains the `CREATE ROLE` and `CREATE DATABASE` commands, so you do not need to create them beforehand. Using `-v ON_ERROR_STOP=1` is a best practice, as it ensures the restore process will halt immediately if any command fails."
      },
      "notes": [
        "This restore process must be performed by a database superuser to ensure it has the necessary permissions to create roles and databases.",
        "The server you are restoring to should be a clean installation to avoid conflicts with existing objects.",
        "File paths are platform-dependent. For Windows, you might use a path like 'C:\\Backups\\cluster_backup.sql'."
      ]
    }
  ]
};