export const command5 = {
  "path": [
    "Backup and Restore",
    "Restoring a Database",
    "From a Custom Format Dump",
    "Create a New Database"
  ],
  "command": "createdb -U <username> -O <owner> <new_database_name>",
  "description": "Creates a new, empty PostgreSQL database. This is often the first step before restoring a backup, as the target database must exist before data can be imported into it.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with (must have CREATEDB privilege)."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (default: local socket)."
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)."
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Force a password prompt."
    },
    {
      "flag": "-O",
      "name": "owner",
      "description": "Specifies the database user who will own the new database (default: current user)."
    },
    {
      "flag": "-T",
      "name": "template",
      "description": "The template database to copy from (default: template1)."
    },
    {
      "flag": "new_database_name",
      "name": "database name",
      "description": "The name of the new database to create."
    }
  ],
  "scenarios": [
    {
      "title": "Preparing a New Staging Database for a Production Restore",
      "details": {
        "setup": "You have a backup file from your production e-commerce database and need to restore it into a new, isolated environment for staging and testing purposes. The application's user is 'ecom_user'.",
        "goal": "To create a new, empty database named 'ecom_staging' that is owned by the correct application user, making it ready for the `pg_restore` process.",
        "outcome": "A new database named 'ecom_staging' exists on the server and is owned by 'ecom_user'. You can now proceed to restore the backup into it."
      },
      "example": {
        "command": "createdb -U postgres -O ecom_user ecom_staging",
        "explanation": "This command connects as the 'postgres' superuser to ensure creation privileges. The `-O ecom_user` flag is crucial as it assigns ownership of the new database to the application user, preventing permission issues after the data is restored."
      },
      "notes": [
        "Running `createdb` is a prerequisite for `pg_restore`. The target database will not be created automatically by the restore command.",
        "Assigning the correct owner with `-O` at the time of creation is a best practice that simplifies permission management.",
        "For specific encoding or collation needs, you can add `--encoding=UTF8` and `--lc-collate='en_US.UTF-8'` to the command."
      ]
    }
  ]
};