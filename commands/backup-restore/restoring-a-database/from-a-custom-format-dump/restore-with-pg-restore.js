export const command6 = {
  "path": [
    "Backup and Restore",
    "Restoring a Database",
    "From a Custom Format Dump",
    "Restore with pg_restore"
  ],
  "command": "pg_restore -U your_user -d new_database_name your_database_backup.dump",
  "description": "Use pg_restore to load data from a custom format dump. Offers powerful options for parallel processing and selective restoration of database objects.",
  "parameters": [
    {
      "flag": "-U",
      "name": "username",
      "description": "Specifies the PostgreSQL user to connect with"
    },
    {
      "flag": "-d",
      "name": "database",
      "description": "The target database name to restore into"
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "Database server host (default: local socket)"
    },
    {
      "flag": "-p",
      "name": "port",
      "description": "Database server port (default: 5432)"
    },
    {
      "flag": "-W",
      "name": "password",
      "description": "Force password prompt"
    },
    {
      "flag": "-j",
      "name": "jobs",
      "description": "Number of parallel jobs for faster restoration (e.g., -j 4)"
    },
    {
      "flag": "-v",
      "name": "verbose",
      "description": "Verbose mode - show detailed progress information"
    },
    {
      "flag": "-c",
      "name": "clean",
      "description": "Clean (drop) database objects before recreating them"
    },
    {
      "flag": "-C",
      "name": "create",
      "description": "Create the database before restoring into it"
    },
    {
      "flag": "-t",
      "name": "table",
      "description": "Restore only the named table(s)"
    },
    {
      "flag": "-n",
      "name": "schema",
      "description": "Restore only objects in the named schema(s)"
    }
  ],
  "scenarios": [
    {
      "title": "Rapid Disaster Recovery of a Production Database",
      "details": {
        "setup": "A critical hardware failure has destroyed the main database server. A new server is provisioned, and you have a parallel backup file named 'prod_db_backup.dump' from the previous night.",
        "goal": "To restore the entire 'prod_db' database onto the new server as quickly as possible to minimize application downtime.",
        "outcome": "The 'prod_db' database is fully recreated on the new server, including all schemas, tables, and data, ready for the application to reconnect."
      },
      "example": {
        "command": "pg_restore -U postgres -h new-db-host.com -j 8 -C -d postgres prod_db_backup.dump",
        "explanation": "This command uses 8 parallel jobs (-j 8) to dramatically speed up the restoration. The -C flag instructs pg_restore to first create the target database ('prod_db' is stored in the backup file). The connection is made to the default 'postgres' database (-d postgres) because the target database does not yet exist."
      },
      "notes": [
        "The number of jobs should ideally not exceed the number of CPU cores on the destination server.",
        "This is the most common use-case for recovering from a total data loss event."
      ]
    },
    {
      "title": "Refreshing a Staging Environment with Specific Data",
      "details": {
        "setup": "The QA team needs to test new features against up-to-date data from the 'customers' and 'analytics' schemas of the production database. The full database is too large for the staging server.",
        "goal": "To completely overwrite the 'customers' and 'analytics' schemas in the 'staging_db' with fresh data from the 'full_prod_backup.dump' file, without touching other schemas.",
        "outcome": "The targeted schemas in 'staging_db' are dropped and recreated with the latest production data, providing an accurate testing environment."
      },
      "example": {
        "command": "pg_restore -U qa_user -d staging_db -c -n customers -n analytics full_prod_backup.dump",
        "explanation": "The -n flag is used twice to select only the 'customers' and 'analytics' schemas. The -c (clean) flag ensures that any existing objects within these schemas on the staging server are dropped before the new ones are created, preventing errors."
      },
      "notes": [
        "This technique is invaluable for creating focused, manageable datasets for development and testing.",
        "You can combine -n (schema) and -t (table) to restore specific tables from within specific schemas."
      ]
    },
    {
      "title": "Recovering a Single Accidentally Deleted Table",
      "details": {
        "setup": "A developer accidentally ran a DROP TABLE command on the 'public.accounts' table in the live database. All other data is intact, but this critical table is gone.",
        "goal": "To restore only the 'public.accounts' table, including its structure and data, from the latest nightly backup ('daily_backup.dump') without disrupting the rest of the database.",
        "outcome": "The 'public.accounts' table is seamlessly recreated and repopulated with the data from the backup, resolving the incident with minimal impact."
      },
      "example": {
        "command": "pg_restore -U db_admin -d live_database -t accounts daily_backup.dump",
        "explanation": "The -t accounts flag targets the restoration specifically to the table named 'accounts'. pg_restore is intelligent enough to restore its definition, data, and associated indexes from the backup file."
      },
      "notes": [
        "Before running, check if other tables have foreign key dependencies on the table being restored. They might need to be temporarily disabled.",
        "This is a surgical operation that avoids the need for a full database restore, saving significant time and resources."
      ]
    }
  ]
}