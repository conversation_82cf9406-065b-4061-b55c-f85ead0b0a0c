export const command27 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Setup",
    "Create Text Search Configuration"
  ],
  "description": "Creates a new, custom text search configuration. This is the first step in building a tailored full-text search engine that can handle specialized vocabularies, languages, or indexing rules.",
  "query_template": "CREATE TEXT SEARCH CONFIGURATION [new_config_name] (COPY = [base_config_name]);",
  "components": [
    {
      "component": "CREATE TEXT SEARCH CONFIGURATION",
      "name": "DDL Statement",
      "description": "The SQL command to create a new text search configuration object in the database."
    },
    {
      "component": "COPY = [base_config_name]",
      "name": "Copy Clause",
      "description": "Initializes the new configuration by copying an existing one (e.g., 'english', 'simple'). This provides a solid baseline for further customization."
    }
  ],
  "scenarios": [
    {
      "title": "Creating a Custom Search for a Technical Knowledge Base",
      "details": {
        "setup": "You are building a search engine for a technical documentation website. The articles contain many hyphenated terms (e.g., 'state-of-the-art', 'command-line'). The default 'english' search configuration breaks these terms into separate words, leading to irrelevant search results.",
        "goal": "To create a custom search configuration named `tech_docs_config` that correctly handles hyphenated words, treating them as single terms to improve search accuracy.",
        "outcome": "The `tech_docs_config` is successfully created. It can now be further modified and then used to index and search the knowledge base content, providing more accurate results for specialized technical terms."
      },
      "example": {
        "query": "-- Step 1: Create the new configuration by copying the 'english' default\nCREATE TEXT SEARCH CONFIGURATION public.tech_docs_config (COPY = english);\n\n-- Step 2: (Follow-up) Alter the configuration to correctly handle hyphenated words\nALTER TEXT SEARCH CONFIGURATION public.tech_docs_config \n   ALTER MAPPING FOR hword_part WITH simple;",
        "explanation": "First, we create `tech_docs_config` as a copy of the robust `english` configuration. The follow-up `ALTER` command is the key: it modifies the mapping for `hword_part` (parts of a hyphenated word) to use the `simple` dictionary. The `simple` dictionary performs no modifications, ensuring that a term like 'command-line' is indexed as a single token instead of being broken apart."
      },
      "notes": [
        "Creating the configuration is only the first step. The real power comes from using `ALTER TEXT SEARCH CONFIGURATION` to add custom dictionaries, stop word lists, and thesauruses.",
        "After creating and applying a new configuration to your data, you must re-index your documents for the changes to take effect on existing records.",
        "You can inspect the tokenization behavior of any configuration using the `ts_debug()` function."
      ]
    }
  ]
};