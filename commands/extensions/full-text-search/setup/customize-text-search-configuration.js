export const command28 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Setup",
    "Customize Text Search Configuration"
  ],
  "description": "Customizes a full-text search configuration by adding or modifying the rules that map text token types (like words or numbers) to specific dictionaries for processing (e.g., stemming, stop-word removal).",
  "query_template": "ALTER TEXT SEARCH CONFIGURATION <config_name> <action> MAPPING FOR <token_type> WITH <dictionary_list>;",
  "components": [
    {
      "component": "ALTER TEXT SEARCH CONFIGURATION",
      "name": "Target Configuration",
      "description": "Specifies the name of the text search configuration you intend to modify."
    },
    {
      "component": "ADD MAPPING FOR",
      "name": "Add Mapping",
      "description": "Adds a new rule for a specific token type. If a mapping for the token already exists, you must use ALTER MAPPING."
    },
    {
      "component": "DROP MAPPING FOR",
      "name": "Drop Mapping",
      "description": "Removes an existing rule for a specific token type."
    },
    {
      "component": "WITH <dictionary_list>",
      "name": "Dictionary Assignment",
      "description": "A comma-separated list of dictionaries that will process the token. PostgreSQL will try them in order until one recognizes the token."
    }
  ],
  "scenarios": [
    {
      "title": "Improving Search Accuracy for a Bilingual Product Catalog",
      "details": {
        "setup": "You are running an e-commerce website with product descriptions in both English and Spanish. The default 'english' text search configuration does not handle Spanish words correctly. For example, a search for 'zapatos' (shoes) does not match products described with the singular 'zapato'.",
        "goal": "To create and configure a custom full-text search configuration named 'product_search_config' that can correctly stem words in both English and Spanish, making the search feature more effective for all users.",
        "outcome": "The new configuration is active. When used in a query, it correctly processes both languages, so a search for 'running' will match 'run', and a search for 'zapatos' will match 'zapato'."
      },
      "example": {
        "query": "-- Step 1: Create a new configuration by copying an existing one\nCREATE TEXT SEARCH CONFIGURATION public.product_search_config (COPY = pg_catalog.english);\n\n-- Step 2: Add mappings for Spanish and English stemming\nALTER TEXT SEARCH CONFIGURATION public.product_search_config\n  ADD MAPPING FOR word, asciiword WITH spanish_stem, english_stem;",
        "explanation": "First, we create a new configuration `product_search_config` by copying the standard `english` configuration. Then, we use `ALTER... ADD MAPPING` to tell PostgreSQL that for any token identified as a `word` or `asciiword`, it should first try to process it with the `spanish_stem` dictionary. If that dictionary doesn't recognize the word, it will then fall back to the `english_stem` dictionary."
      },
      "notes": [
        "The order of dictionaries in the 'WITH' clause is important. The first dictionary to recognize a token will be the one used.",
        "After creating the configuration, you must explicitly use it in your queries, for example: `to_tsvector('product_search_config', product_description)`.",
        "If you have an existing full-text search index, you will need to re-index it to use the new configuration: `REINDEX INDEX your_fts_index;`"
      ]
    }
  ]
};