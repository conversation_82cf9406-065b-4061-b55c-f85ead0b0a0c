export const command26 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Setup",
    "Create Documents Table"
  ],
  "description": "Creates a foundational table for a full-text search system. It includes columns for the original content and a crucial 'tsvector' column to store the processed, searchable text.",
  "query_template": "CREATE TABLE documents (id SERIAL PRIMARY KEY, title TEXT, content TEXT, document_vector TSVECTOR);",
  "components": [
    {
      "component": "document_vector TSVECTOR",
      "name": "Search Vector Column",
      "description": "A dedicated column of type TSVECTOR. This stores the processed text (lexemes) and is the target for all full-text search queries, making them highly efficient."
    },
    {
      "component": "title TEXT, content TEXT",
      "name": "Source Text Columns",
      "description": "Standard text columns that hold the original, human-readable content. This data will be used to populate the 'document_vector' column."
    },
    {
      "component": "id SERIAL PRIMARY KEY",
      "name": "Primary Key",
      "description": "A unique identifier for each document, essential for managing and retrieving specific records."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a Searchable Knowledge Base",
      "details": {
        "setup": "You are building an internal company wiki to store technical articles, guides, and project documentation. Employees need a fast and effective way to search the entire content library.",
        "goal": "To create the primary database table that will store the articles, structured specifically to support efficient full-text search capabilities from day one.",
        "outcome": "The `articles` table is created in the database, ready to be populated with content and have its `search_vector` column indexed and updated for searching."
      },
      "example": {
        "query": "CREATE TABLE articles (\n  id SERIAL PRIMARY KEY,\n  title TEXT NOT NULL,\n  body TEXT,\n  author_id INT,\n  last_updated TIMESTAMPTZ DEFAULT NOW(),\n  search_vector TSVECTOR\n);",
        "explanation": "This query creates a robust `articles` table. The `search_vector` column is added to hold the `tsvector` data. A separate process (often a database trigger) will be set up to combine text from the `title` and `body` columns, convert it into a `tsvector`, and store it here. This pre-processing is the key to fast search performance."
      },
      "notes": [
        "Crucial next step: After creating the table, you must create a GIN index on the `search_vector` column (e.g., `CREATE INDEX articles_search_idx ON articles USING GIN(search_vector);`). Without it, your searches will be slow.",
        "You will also need a mechanism to keep the `search_vector` synchronized with the `title` and `body` columns. This is typically done using a database trigger and a function.",
        "Using `TIMESTAMPTZ` (timestamp with time zone) for date/time columns is a best practice for applications that might be used across different time zones."
      ]
    }
  ]
};