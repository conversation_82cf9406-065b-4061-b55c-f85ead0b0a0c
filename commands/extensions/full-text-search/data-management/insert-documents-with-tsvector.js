export const command32 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Data Management",
    "Insert Documents with tsvector"
  ],
  "description": "Inserts a new document into a table and manually pre-calculates the `tsvector` for full-text search. This method involves combining multiple text fields and converting them into the `tsvector` format at the time of insertion.",
  "query_template": "INSERT INTO [table] ([col1], [col2], [tsvector_col]) VALUES ('value1', 'value2', to_tsvector('[language]', 'value1' || ' ' || 'value2'));",
  "components": [
    {
      "component": "to_tsvector('[language]', ...)",
      "name": "Text to Tsvector Conversion",
      "description": "The core function that processes a string into a searchable `tsvector`. It performs critical tasks like normalization (making words lowercase), stemming (reducing words to their root form), and removing common 'stop words' (e.g., 'a', 'the', 'is')."
    },
    {
      "component": "column1 || ' ' || column2",
      "name": "String Concatenation",
      "description": "The `||` operator is used to combine the contents of multiple text fields into a single string, allowing the `tsvector` to be built from all relevant text in the record."
    }
  ],
  "scenarios": [
    {
      "title": "Manually Populating a Search Index for a Blog Platform",
      "details": {
        "setup": "You are building a blog platform and have an `articles` table with `title` and `body` columns. To enable fast and relevant searching, you have added a `search_vector` column of type `tsvector`.",
        "goal": "To insert a new article into the `articles` table and ensure its `search_vector` is immediately and correctly populated by indexing both the title and the body, making the article instantly available to the search feature.",
        "outcome": "A new row for the article 'Advanced SQL Techniques' is successfully added to the table. The `search_vector` column contains the processed text from the title and body, ready to be matched against user search queries."
      },
      "example": {
        "query": "INSERT INTO articles (author_id, title, body, search_vector)\nVALUES (101, 'Advanced SQL Techniques', 'This article covers window functions, CTEs, and more.', to_tsvector('english', 'Advanced SQL Techniques' || ' ' || 'This article covers window functions, CTEs, and more.'));",
        "explanation": "This query manually creates the `tsvector` during the `INSERT` operation. By concatenating the `title` and `body` with a space in between, we ensure that searches can match terms from either field. The `to_tsvector` function then processes this combined text using the 'english' language configuration."
      },
      "notes": [
        "While effective, this manual method is error-prone. A more robust and automated approach is to use a **generated column** (in PostgreSQL 12+) or a **trigger** to compute the `tsvector` automatically whenever a row is inserted or updated. This removes the responsibility from your application code.",
        "For searches to be fast, you must create a GIN (Generalized Inverted Index) on the `tsvector` column: `CREATE INDEX articles_search_idx ON articles USING GIN(search_vector);`"
      ]
    }
  ]
};