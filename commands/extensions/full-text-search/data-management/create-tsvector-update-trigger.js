export const command33 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Data Management",
    "Create tsvector Update Trigger"
  ],
  "description": "Creates a trigger that automatically populates and maintains a tsvector column from other text fields. This is the foundation for implementing a powerful and efficient full-text search engine within PostgreSQL, allowing words in different fields (like title and content) to be weighted differently in search rankings.",
  "query_template": "/* Step 1: Create the function that builds the tsvector */\nCREATE OR REPLACE FUNCTION [function_name]() RETURNS TRIGGER AS $$\nBEGIN\n  NEW.[tsvector_column] := \n    setweight(to_tsvector('[config]', COALESCE(NEW.[column1], '')), 'A') ||\n    setweight(to_tsvector('[config]', COALESCE(NEW.[column2], '')), 'B');\n  RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\n/* Step 2: Create the trigger that executes the function on data changes */\nCREATE TRIGGER [trigger_name]\n  BEFORE INSERT OR UPDATE ON [table_name]\n  FOR EACH ROW EXECUTE FUNCTION [function_name]();",
  "components": [
    {
      "component": "CREATE OR REPLACE FUNCTION ... RETURNS TRIGGER",
      "name": "Trigger Function",
      "description": "Defines a PL/pgSQL function that is designed to be executed by a trigger. It contains the logic for how the tsvector column will be generated."
    },
    {
      "component": "NEW.[tsvector_column] := ...",
      "name": "Vector Assignment",
      "description": "Inside the function, 'NEW' refers to the row being inserted or updated. This line assigns the generated tsvector to the target column for that row."
    },
    {
      "component": "setweight(to_tsvector(...), 'A')",
      "name": "Weighted Vectorization",
      "description": "The core of the logic. `to_tsvector` converts raw text into a search vector. `setweight` assigns a priority ('A' is highest, 'D' is lowest) to the words from that text, enabling ranked search results."
    },
    {
      "component": "CREATE TRIGGER ... BEFORE INSERT OR UPDATE",
      "name": "Trigger Definition",
      "description": "Creates the trigger itself, linking the table (`ON [table_name]`) to the function. It specifies that the function should run automatically before any new row is inserted or an existing one is updated."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing Weighted Search for a Knowledge Base",
      "details": {
        "setup": "You are building a knowledge base with an 'articles' table containing 'title', 'content', and 'keywords' columns. You need a search feature that ranks articles where the search term appears in the title higher than those where it only appears in the content.",
        "goal": "To automatically create and maintain a weighted search vector in a column named 'document_vector' every time an article is created or updated.",
        "outcome": "The `document_vector` column is always kept in sync with the article's text, with different weights assigned to title, keywords, and content, enabling sophisticated search ranking."
      },
      "example": {
        "query": "/* Step 1: The function */\nCREATE OR REPLACE FUNCTION articles_tsvector_update() RETURNS TRIGGER AS $$\nBEGIN\n  NEW.document_vector := \n    setweight(to_tsvector('pg_catalog.english', COALESCE(NEW.title, '')), 'A') ||\n    setweight(to_tsvector('pg_catalog.english', COALESCE(NEW.keywords, '')), 'B') ||\n    setweight(to_tsvector('pg_catalog.english', COALESCE(NEW.content, '')), 'C');\n  RETURN NEW;\nEND;\n$$ LANGUAGE plpgsql;\n\n/* Step 2: The trigger */\nCREATE TRIGGER tsvector_update \n  BEFORE INSERT OR UPDATE ON articles\n  FOR EACH ROW EXECUTE FUNCTION articles_tsvector_update();",
        "explanation": "This implementation correctly sets up the automatic update process. The `title` is given the highest weight ('A'), `keywords` a medium weight ('B'), and `content` a lower weight ('C'). When a user searches for a term, you can use the `ts_rank` function to score results, and articles with the term in the title will naturally score higher."
      },
      "notes": [
        "For this to be fast, you MUST create a GIN index on the tsvector column after setting this up. Example: `CREATE INDEX articles_doc_vector_idx ON articles USING GIN(document_vector);`",
        "This approach is far superior to using `LIKE '%term%'` as it is faster, supports stemming (e.g., 'running' matches 'run'), and allows for relevance ranking."
      ]
    }
  ]
};