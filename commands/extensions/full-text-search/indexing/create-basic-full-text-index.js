export const command29 = {
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Indexing",
    "Create Basic Full-Text Index"
  ],
  "description": "Creates a GIN (Generalized Inverted Index) on one or more text columns, enabling efficient and linguistically-aware full-text search.",
  "query_template": "CREATE INDEX [index_name] ON [table_name] USING GIN(to_tsvector('[language]', [column1] || ' ' || [column2]));",
  "components": [
    {
      "component": "USING GIN",
      "name": "Index Method",
      "description": "Specifies the use of a GIN (Generalized Inverted Index), which is the recommended index type for full-text search due to its performance on queries."
    },
    {
      "component": "to_tsvector('english', ...)",
      "name": "Text Conversion Function",
      "description": "A core function that converts plain text into a `tsvector`, a sorted list of distinct words (lexemes) that have been normalized. The 'english' argument specifies the language rules for stemming and stop words."
    },
    {
      "component": "title || ' ' || content",
      "name": "Document Concatenation",
      "description": "Combines text from multiple columns into a single string to be indexed, allowing users to search across all of them at once as if they were one document."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing a Search Feature for a Technical Blog Platform",
      "details": {
        "setup": "You have a blog application with an `articles` table containing `title` and `content` columns. The current search functionality uses `ILIKE '%search_term%'`, which is slow, inefficient, and does not provide relevant results (e.g., searching for 'run' does not find 'running').",
        "goal": "To implement a high-performance, linguistically-aware search that allows users to find articles by matching words in either the title or the content.",
        "outcome": "A GIN index is created on the `articles` table. Subsequent search queries are fast and can match different forms of the same word, providing a much better user experience."
      },
      "example": {
        "query": "CREATE INDEX idx_articles_search ON articles USING GIN(to_tsvector('english', title || ' ' || content));",
        "explanation": "This query creates a GIN index named `idx_articles_search`. It pre-processes the combined text of the `title` and `content` columns for every article, using English language rules. The resulting `tsvector` is indexed, allowing PostgreSQL to rapidly find articles containing specific words."
      },
      "notes": [
        "To use this index, your `SELECT` queries must use the `@@` operator with `to_tsquery()`, for example: `WHERE to_tsvector('english', title || ' ' || content) @@ to_tsquery('english', 'database & performance');`",
        "While GIN indexes are fast for searching, they can be slower to update than other index types. Consider this for tables with very high write frequency.",
        "For more advanced use cases, consider creating a dedicated `tsvector` column that is updated by a trigger, which can improve search performance by avoiding the `to_tsvector` call in the `WHERE` clause."
      ]
    }
  ]
};