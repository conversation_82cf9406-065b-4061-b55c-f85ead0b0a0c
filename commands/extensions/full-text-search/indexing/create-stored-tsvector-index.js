export const command31 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Indexing",
    "Create Stored tsvector Index"
  ],
  "description": "Creates a GIN (Generalized Inverted Index) on a pre-computed tsvector column. This is the most efficient method for full-text search as it allows the database to perform lookups very quickly, rather than calculating the tsvector for every row at query time.",
  "query_template": "CREATE INDEX [index_name] ON [table_name] USING GIN([tsvector_column]);",
  "components": [
    {
      "component": "CREATE INDEX [index_name] ON [table_name]",
      "name": "Standard Index Creation",
      "description": "The standard SQL command to create a new index on a specified table."
    },
    {
      "component": "USING GIN",
      "name": "Index Method",
      "description": "Specifies the GIN index type, which is highly optimized for indexing composite values where items appear multiple times, making it ideal for full-text search."
    },
    {
      "component": "([tsvector_column])",
      "name": "Target Column",
      "description": "The name of the column that already contains the pre-computed tsvector data."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing High-Performance Search for a Knowledge Base",
      "details": {
        "setup": "You are managing a corporate knowledge base with an `articles` table containing thousands of long-form technical documents. A `tsvector_content` column is already set up to automatically store the tsvector representation of the `article_body` using a database trigger.",
        "goal": "To make the search functionality nearly instantaneous, ensuring employees can find relevant articles without delay as they type into a search bar.",
        "outcome": "A GIN index named `idx_articles_content` is created on the `tsvector_content` column. Subsequent full-text search queries on this table now complete in milliseconds, even with a large dataset."
      },
      "example": {
        "query": "CREATE INDEX idx_articles_content ON articles USING GIN(tsvector_content);",
        "explanation": "This query builds a GIN index on the `tsvector_content` column. When a user searches for a term, PostgreSQL can use this index to instantly locate all articles containing that term, avoiding a slow and costly full-table scan. This is the standard practice for production-grade full-text search."
      },
      "notes": [
        "This approach is highly recommended over creating an expression-based index, as the tsvector computation is done once on insert/update, not during every search.",
        "Remember that creating this index depends on having a dedicated `tsvector` column that is kept up-to-date, typically via a trigger on the source text column.",
        "While GIN indexes are very fast for queries, they can be slower to update than GiST indexes. For write-heavy applications, you might compare performance, but GIN is usually the best choice for full-text search."
      ]
    }
  ]
};