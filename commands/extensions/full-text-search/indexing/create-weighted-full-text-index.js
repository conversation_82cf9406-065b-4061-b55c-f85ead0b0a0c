export const command30 = {
  // --- Identifier for the app ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Indexing",
    "Create Weighted Full-Text Index"
  ],
  "description": "Creates a sophisticated full-text search index that assigns different levels of importance (weights) to different text fields, enabling more relevant search result ranking.",

  // --- Query-specific fields ---
  "query_template": "CREATE INDEX [index_name] ON [table_name] USING GIN(setweight(to_tsvector('[config]', [column1]), '[weight]') || setweight(to_tsvector('[config]', [column2]), '[weight]'));",
  "components": [
    {
      "component": "USING GIN",
      "name": "GIN Index Type",
      "description": "Specifies the Generalized Inverted Index (GIN), which is highly efficient for indexing composite values like tsvectors used in full-text search."
    },
    {
      "component": "to_tsvector('english', ...)",
      "name": "Text to Vector",
      "description": "A function that processes text, converting it into a normalized list of lexemes (words) suitable for searching."
    },
    {
      "component": "setweight(..., 'A')",
      "name": "Set Weight",
      "description": "Assigns a specific weight (A, B, C, or D) to a tsvector. 'A' is the highest priority, 'D' is the lowest."
    },
    {
      "component": "||",
      "name": "Concatenation Operator",
      "description": "Combines the two weighted tsvectors into a single document vector that the index will store."
    }
  ],

  "scenarios": [
    {
      "title": "Improving Search Result Relevancy on a Knowledge Base",
      "details": {
        "setup": "You manage a knowledge base with thousands of articles stored in a table named `articles` (with `title` and `content` columns). When users search for a term, articles where the term appears only in the content are ranked just as highly as articles where the term is in the title, leading to irrelevant results.",
        "goal": "To create a search index that prioritizes matches in the `title` over matches in the `content`, ensuring that the most relevant articles appear first in search results.",
        "outcome": "A new GIN index is created that embeds the weighting information. Subsequent search queries that use ranking functions like `ts_rank` will now return more relevant, logically ordered results."
      },
      "example": {
        "query": "CREATE INDEX idx_articles_weighted_search ON articles USING GIN(setweight(to_tsvector('english', title), 'A') || setweight(to_tsvector('english', content), 'B'));",
        "explanation": "This command creates an index on the `articles` table. It processes the `title` and assigns it the highest weight ('A'), then processes the `content` and gives it a lower weight ('B'). By combining them, the index allows ranking functions to score documents with title matches higher than those with only content matches."
      },
      "notes": [
        "To take advantage of this index, your `SELECT` queries must use a ranking function like `ts_rank` or `ts_rank_cd` in the `ORDER BY` clause.",
        "Example search query: `SELECT title FROM articles WHERE to_tsvector('english', title || ' ' || content) @@ to_tsquery('english', 'search-term') ORDER BY ts_rank(setweight(to_tsvector('english', title), 'A') || setweight(to_tsvector('english', content), 'B'), to_tsquery('english', 'search-term')) DESC;`",
        "If you have an existing, unweighted full-text index on these columns, you should drop it to save space and avoid confusion."
      ]
    }
  ]
};