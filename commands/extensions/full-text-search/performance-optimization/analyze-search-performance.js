export const command43 = {
  // --- Set type to 'query' for proper handling in your app ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Performance Optimization",
    "Analyze Search Performance"
  ],
  "description": "Analyzes the execution plan of a full-text search query to verify that a GIN or GiST index is being used effectively, which is essential for fast search performance.",

  // --- Query-specific fields for SQL syntax breakdown ---
  "query_template": "EXPLAIN ANALYZE SELECT [columns] FROM [table] WHERE [tsvector_column] @@ to_tsquery('[language]', '[search_terms]');",
  "components": [
    {
      "component": "EXPLAIN ANALYZE",
      "name": "Query Plan Analysis",
      "description": "Executes the search query and returns the detailed execution plan, including timings, to reveal how PostgreSQL is retrieving the data."
    },
    {
      "component": "@@ to_tsquery(...)",
      "name": "Full-Text Match Operator",
      "description": "The core of the full-text search. The `@@` operator checks if the `tsvector` (pre-processed document) matches the `tsquery` (user's search terms)."
    }
  ],

  // --- Scenario-based examples ---
  "scenarios": [
    {
      "title": "Diagnosing a Slow Search Feature on a Knowledge Base",
      "details": {
        "setup": "You manage a large documentation website with a search feature. The `articles` table has a `tsvector_content` column with a GIN index on it. Users are reporting that searches are becoming slower as more articles are added.",
        "goal": "To confirm that the GIN index (`articles_ts_idx`) is being used for search queries and that the database isn't resorting to a slow, full-table scan.",
        "outcome": "The query plan is generated, and you can confirm that an efficient 'Bitmap Index Scan' is being used, proving the index is working as intended."
      },
      "example": {
        "query": "EXPLAIN ANALYZE SELECT title FROM articles WHERE tsvector_content @@ to_tsquery('english', 'optimizing & query');",
        "explanation": "This query analyzes the performance of a search for articles containing 'optimizing' and 'query'. For a GIN index to be effective, the output must show a 'Bitmap Heap Scan' combined with a 'Bitmap Index Scan on articles_ts_idx'. This indicates the index was used to quickly find matching rows."
      },
      "notes": [
        "If you see 'Seq Scan' (Sequential Scan) in the plan, it means the index is not being used. This could be due to outdated table statistics; run `ANALYZE articles;` to fix this.",
        "A 'Bitmap Heap Scan' is the expected and most efficient plan for GIN indexes in full-text search, not a direct 'Index Scan'.",
        "The complexity of your `to_tsquery` function can also impact performance. Simple terms are faster than complex phrases with positional operators."
      ]
    }
  ]
};
