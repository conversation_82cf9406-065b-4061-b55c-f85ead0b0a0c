export const command44 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Performance Optimization",
    "Search Statistics"
  ],
  "description": "Views the planner statistics for a tsvector column. This is crucial for diagnosing performance issues by understanding what the query planner knows about the data distribution in your search vectors.",
  "query_template": "SELECT schemaname, tablename, attname, n_distinct, correlation FROM pg_stats WHERE tablename = '[table_name]' AND attname = '[tsvector_column]';",
  "components": [
    {
      "component": "pg_stats",
      "name": "System Statistics View",
      "description": "A system catalog view that stores statistical data about the contents of the database. Querying it gives insight into how the planner sees your data."
    },
    {
      "component": "n_distinct",
      "name": "Number of Distinct Values",
      "description": "An estimate of the number of unique tsvector values in the column. If this number is negative, it's a percentage of the total rows (e.g., -0.8 means 80% of the rows are unique)."
    },
    {
      "component": "correlation",
      "name": "Physical/Logical Correlation",
      "description": "Measures the correlation between the physical row order on disk and the logical ordering of the column values. For a tsvector, this is less intuitive but still used by the planner."
    }
  ],
  "scenarios": [
    {
      "title": "Diagnosing Inefficient Full-Text Search Plans",
      "details": {
        "setup": "You have a `products` table with a `tsvector` column named `search_vector` used for the site's search feature. After a large data import, you notice that some common search terms are performing much slower than expected, suggesting the GIN index is not being used effectively.",
        "goal": "To inspect the database's internal statistics for the `search_vector` column to see if they are stale and misleading the query planner.",
        "outcome": "The query reveals that `n_distinct` is much lower than expected, confirming the statistics are out of date. After updating the statistics, query performance returns to normal."
      },
      "example": {
        "query": "SELECT n_distinct, correlation FROM pg_stats WHERE tablename = 'products' AND attname = 'search_vector';",
        "explanation": "This query retrieves the key statistics for the `search_vector` column. If you recently added thousands of new, unique products but `n_distinct` shows a very small number, it tells you the planner is likely making poor decisions based on old information."
      },
      "notes": [
        "The most common reason for inaccurate statistics is a lack of analysis after significant data changes (inserts, updates, deletes).",
        "If statistics are stale, you must run `ANALYZE products;` to update them. This operation is lightweight and can be run in production.",
        "PostgreSQL's autovacuum daemon is responsible for automatically analyzing tables, but its timing may not be frequent enough after a bulk data load."
      ]
    }
  ]
};