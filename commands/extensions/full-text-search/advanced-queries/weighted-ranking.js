export const command38 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Queries",
    "Weighted Ranking"
  ],
  "description": "Performs a full-text search query that ranks results based on custom weights. This allows you to give more importance to matches found in different parts of a document (e.g., title vs. body).",
  "query_template": "SELECT [column], ts_rank('{D, C, B, A}', [tsvector_column], to_tsquery('[language]', '[search_terms]')) as rank FROM [table] WHERE [tsvector_column] @@ to_tsquery('[language]', '[search_terms]') ORDER BY rank DESC;",
  "components": [
    {
      "component": "ts_rank('{weights}', ...)",
      "name": "Weighted Rank Function",
      "description": "Calculates a relevance score for a document based on the search terms. The initial array '{0.1, 0.2, 0.4, 1.0}' provides the weights for D, C, B, and A labels, respectively."
    },
    {
      "component": "tsvector_column",
      "name": "Weighted tsvector",
      "description": "A pre-processed tsvector column where different parts of the source text have been labeled with weights (A, B, C, or D). For example, titles are often labeled 'A' and body content 'D'."
    },
    {
      "component": "@@ to_tsquery(...)",
      "name": "Match Operator",
      "description": "The core full-text search match operator. It is fast and essential for allowing the query to use a GIN index for high performance."
    }
  ],
  "scenarios": [
    {
      "title": "Improving Search Relevance in a Product Documentation Site",
      "details": {
        "setup": "You run a technical documentation website with hundreds of articles. You have already created a `tsvector` column named `document_vector` where article titles are assigned weight 'A' and the main content is weight 'D'.",
        "goal": "When a user searches for a term, you want to ensure that articles where the term appears in the **title** are ranked significantly higher than articles where the term only appears in the body content.",
        "outcome": "The search results are returned with the most relevant articles (title matches) appearing at the top, providing a much better user experience."
      },
      "example": {
        "query": "SELECT title, ts_rank('{0.1, 0.2, 0.4, 1.0}', document_vector, to_tsquery('english', 'backup & restore')) as rank FROM articles WHERE document_vector @@ to_tsquery('english', 'backup & restore') ORDER BY rank DESC LIMIT 10;",
        "explanation": "This query searches for 'backup' and 'restore'. The `ts_rank` function is configured to apply a high weight (1.0) to 'A' labels. Since titles were labeled 'A' when the `document_vector` was created, any article with the search terms in its title gets a much higher rank and appears first."
      },
      "notes": [
        "For this to work, the `tsvector` column must be created using `setweight`. For example: `setweight(to_tsvector(title), 'A') || setweight(to_tsvector(body), 'D')`.",
        "For optimal performance, you must create a GIN index on the `tsvector` column: `CREATE INDEX idx_articles_doc_vector ON articles USING GIN(document_vector);`.",
        "The weights in `ts_rank` are defaults. You can omit them if your needs are simple, but specifying them provides explicit control over the ranking algorithm."
      ]
    }
  ]
};