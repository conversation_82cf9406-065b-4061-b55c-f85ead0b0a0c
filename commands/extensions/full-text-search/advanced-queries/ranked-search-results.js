export const command37 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Queries",
    "Ranked Search Results"
  ],
  "description": "Performs a full-text search and orders the results by a relevance score, ensuring the most relevant documents appear first. This is the foundation of a 'best match' search feature.",
  "query_template": "SELECT [column], ts_rank([vector_column], to_tsquery('[config]', '[search_terms]')) as rank FROM [table] WHERE [vector_column] @@ to_tsquery('[config]', '[search_terms]') ORDER BY rank DESC;",
  "components": [
    {
      "component": "ts_rank(vector, query)",
      "name": "Ranking Function",
      "description": "Calculates a relevance score based on how often and where the search terms appear in the document's tsvector."
    },
    {
      "component": "@@ to_tsquery(...)",
      "name": "Match Operator",
      "description": "The core full-text search operator. It returns true if the document's tsvector matches the text query."
    },
    {
      "component": "ORDER BY rank DESC",
      "name": "Sort by Relevance",
      "description": "Sorts the results in descending order based on the calculated rank, placing the most relevant items at the top."
    }
  ],
  "scenarios": [
    {
      "title": "Building a 'Best Match' Search for a Product Review System",
      "details": {
        "setup": "You have a `reviews` table in your database with a `tsvector` column named `review_vector`. This column contains the pre-processed text from user reviews. A simple boolean search returns too many irrelevant results.",
        "goal": "To implement a search feature that not only finds reviews containing the search terms 'durable' and 'battery' but also shows the reviews where these terms are most significant first.",
        "outcome": "A user searches for 'durable battery' and receives a ranked list of reviews. The top results are those that discuss the 'durable battery' most prominently, providing a much better user experience."
      },
      "example": {
        "query": "SELECT product_id, review_title, ts_rank(review_vector, to_tsquery('english', 'durable & battery')) as rank FROM reviews WHERE review_vector @@ to_tsquery('english', 'durable & battery') ORDER BY rank DESC LIMIT 20;",
        "explanation": "This query filters all reviews to find those that match the terms 'durable' AND 'battery' using the `@@` operator. The `ts_rank` function then assigns a relevance score to each match. Finally, the results are sorted by this rank in descending order to show the 20 most relevant reviews."
      },
      "notes": [
        "For this query to be performant on a large table, you MUST have a GIN index on the `review_vector` (the tsvector) column.",
        "The `&` in `to_tsquery` signifies AND. You can also use `|` for OR (e.g., 'durable | battery') to find reviews containing either term.",
        "Consider using `ts_rank_cd` as an alternative ranking function. It ranks based on 'cover density' and can be better at finding documents where search terms appear close to each other."
      ]
    }
  ]
};