export const command39 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Queries",
    "Highlighting Search Results"
  ],
  "description": "Generates dynamic, highlighted snippets from a document, showing the user's search terms in context. This is essential for building user-friendly search result pages.",
  "query_template": "SELECT [title_column], ts_headline([config], [content_column], to_tsquery([config], [search_terms]), [options]) as snippet FROM [table] WHERE [tsvector_column] @@ to_tsquery([config], [search_terms]);",
  "components": [
    {
      "component": "ts_headline(config, content, query, options)",
      "name": "Headline Function",
      "description": "The core function that processes the original text content and a search query to produce a snippet with highlighted terms. It is highly customizable through an options string."
    },
    {
      "component": "WHERE tsvector_column @@ to_tsquery(...)",
      "name": "Full-Text Search Condition",
      "description": "The clause that filters the documents to find only those that match the search query. The `@@` is the text search match operator."
    },
    {
      "component": "'MaxWords=50, MinWords=20, MaxFragments=3'",
      "name": "Customization Options",
      "description": "An optional string passed to `ts_headline` to control the snippet's appearance. Common options include `MaxWords`, `MinWords` (to control length), `MaxFragments` (to show context from different parts of the document), and `StartSel`/`StopSel` (to define the HTML tags for highlighting)."
    }
  ],
  "scenarios": [
    {
      "title": "Building a Search Results Page for a Documentation Site",
      "details": {
        "setup": "You are building a search feature for a large technical documentation website. When a user searches, you need to show them a list of matching pages. Simply showing the page title is not enough; you need to display a short excerpt showing *why* the page is a relevant result.",
        "goal": "For a user's search query, retrieve the title of each matching article and a formatted text snippet from the article's content. The snippet must highlight the search terms in bold.",
        "outcome": "The SQL query returns a list of article titles and their corresponding snippets, where search terms are wrapped in `<b>` and `</b>` tags, ready to be rendered on a search results page."
      },
      "example": {
        "query": "SELECT \n  title, \n  ts_headline('english', content, to_tsquery('english', 'optimizing & queries'), \n    'StartSel=<b>, StopSel=</b>, MaxWords=35, MinWords=15, MaxFragments=2'\n  ) as snippet \nFROM \n  articles \nWHERE \n  content_vector @@ to_tsquery('english', 'optimizing & queries');",
        "explanation": "This query searches for articles matching 'optimizing' and 'queries'. For each match, `ts_headline` generates up to 2 fragments. It uses the original `content` column to create a human-readable snippet and wraps the matching terms in bold tags for clear visibility on the front end."
      },
      "notes": [
        "The `StartSel` and `StopSel` options allow you to use any HTML you want for highlighting, for instance, `<span class='highlight'>` and `</span>` for CSS styling.",
        "Using `ts_headline` can add processing overhead. It's generally fast but should be tested for performance on very large text documents or high-traffic systems.",
        "Ensure the text search configuration ('english' in this case) used in `ts_headline` and `to_tsquery` is the same one used to create your `tsvector` column for consistent results."
      ]
    }
  ]
};