export const command46 = {
  // --- Using the 'query' type for SQL-based commands ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Maintenance",
    "Rebuild Full-Text Index"
  ],
  "description": "Rebuilds a full-text search index to optimize its structure and performance after many data changes. Using the CONCURRENTLY option allows this to happen without blocking application queries.",
  
  // --- Query-specific fields for SQL syntax breakdown ---
  "query_template": "REINDEX [INDEX | TABLE] [CONCURRENTLY] [name];",
  "components": [
    {
      "component": "REINDEX INDEX [index_name]",
      "name": "Rebuild a Single Index",
      "description": "Recreates the specified index from scratch. This is the most targeted approach."
    },
    {
      "component": "REINDEX TABLE [table_name]",
      "name": "Rebuild All Indexes on a Table",
      "description": "A convenient way to rebuild all indexes associated with a particular table in one go."
    },
    {
      "component": "CONCURRENTLY",
      "name": "Non-Blocking Operation",
      "description": "The key option for live systems. It rebuilds the index without taking locks that would prevent writes (INSERT, UPDATE, DELETE) on the table. (Available since PostgreSQL 12)."
    }
  ],

  "scenarios": [
    {
      "title": "Resolving Full-Text Search Performance Degradation on a Live Application",
      "details": {
        "setup": "You manage a documentation platform with a full-text search feature. The underlying `articles` table is constantly updated. Over several months, you notice that search queries are becoming slower. This is a classic sign of index bloat, where the GIN/GIST index has become fragmented and inefficient due to frequent updates and deletions.",
        "goal": "To rebuild the `articles_fts_idx` index to compact it and restore search performance, without taking the application offline or blocking editors from saving new articles.",
        "outcome": "The index is rebuilt in the background. Once the new, optimized index is ready, PostgreSQL atomically switches to it. Search query performance returns to normal, and there was no service interruption for users or editors."
      },
      "example": {
        "query": "REINDEX INDEX CONCURRENTLY articles_fts_idx;",
        "explanation": "This command specifically targets the `articles_fts_idx`. The `CONCURRENTLY` keyword is critical: it builds a new, healthy copy of the index in the background. While this is happening, all application queries continue to use the old, bloated index. Once the new index is fully built and validated, PostgreSQL swaps it with the old one in a single, non-blocking transaction."
      },
      "notes": [
        "The `CONCURRENTLY` option is available from PostgreSQL 12 onwards. For older versions, you must use `REINDEX INDEX articles_fts_idx;`, which will lock the table against writes, requiring a planned maintenance window.",
        "A concurrent reindex typically takes longer and consumes more CPU and I/O resources than a standard reindex.",
        "`REINDEX CONCURRENTLY` cannot be run inside a transaction block (BEGIN/COMMIT)."
      ]
    }
  ]
};