export const command45 = {
  // --- Set type to 'query' ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Maintenance",
    "Update Search Statistics & Check Size"
  ],
  "description": "Updates the table statistics for the query planner and checks the total disk space used by a full-text search index. This is a common maintenance routine to ensure optimal FTS performance and monitor for index bloat.",

  // --- Query-specific fields ---
  "query_template": "ANALYZE [table_name];\nSELECT pg_size_pretty(pg_total_relation_size('[index_name]')) AS index_size;",
  "components": [
    {
      "component": "ANALYZE [table_name]",
      "name": "Update Statistics",
      "description": "Collects statistics about the contents of a table. For FTS, this helps the query planner make more intelligent decisions when executing search queries."
    },
    {
      "component": "pg_total_relation_size('[index_name]')",
      "name": "Calculate Index Size",
      "description": "A PostgreSQL function that calculates the total disk space used by a relation (in this case, an index), including any associated TOAST tables."
    },
    {
      "component": "pg_size_pretty(...)",
      "name": "Format Size",
      "description": "A convenient function that converts a size in bytes (from the previous function) into a human-readable format like 'MB' or 'GB'."
    }
  ],

  // --- Scenario for this maintenance workflow ---
  "scenarios": [
    {
      "title": "Maintaining a Knowledge Base with Frequent Content Updates",
      "details": {
        "setup": "You manage a corporate wiki stored in a PostgreSQL database. The `articles` table has a GIN index for full-text search. Thousands of articles are added, edited, and deleted each month. You've noticed that some search queries are becoming slower over time.",
        "goal": "To refresh the database's metadata to improve query performance and to check if the FTS index is growing excessively large, which might indicate bloat.",
        "outcome": "The query planner now has fresh statistics for the `articles` table, leading to better execution plans for FTS queries. You also have a clear reading of the index's current size to log for trend analysis."
      },
      "example": {
        "query": "ANALYZE articles;\nSELECT pg_size_pretty(pg_total_relation_size('articles_content_fts_idx')) AS index_size;",
        "explanation": "The `ANALYZE articles;` command updates the statistics that the query planner relies on, which is crucial after significant data changes. The `SELECT` query then inspects the size of the FTS index (`articles_content_fts_idx`), allowing you to track its growth and decide if more intensive maintenance, like a `REINDEX`, is needed in the future."
      },
      "notes": [
        "For routine maintenance, running `VACUUM ANALYZE articles;` is often better, as it both reclaims dead row space and updates statistics in one pass.",
        "Regularly monitoring the index size can help you identify index bloat. A sudden, large increase in size without a corresponding increase in data may warrant investigation."
      ]
    }
  ]
};