export const command47 = {
  // --- This is a query, not a command-line tool ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Use Cases",
    "Document Classification"
  ],
  "description": "Classifies documents into predefined categories based on their content using full-text search. This is a powerful technique for automatically organizing and analyzing large volumes of text data.",

  // --- Query-specific structure ---
  "query_template": "SELECT category, COUNT(*) FROM (SELECT CASE WHEN [tsvector_column] @@ to_tsquery('[dictionary]', '[search_terms_1]') THEN '[Category_1]' WHEN [tsvector_column] @@ to_tsquery('[dictionary]', '[search_terms_2]') THEN '[Category_2]' ELSE '[Default_Category]' END as category FROM [table_name]) as subquery GROUP BY category;",
  "components": [
    {
      "component": "CASE WHEN ... THEN ... END",
      "name": "Conditional Logic Block",
      "description": "The core of the classification logic. It evaluates a series of conditions and assigns a category name ('Database', 'Programming', etc.) when a condition is met."
    },
    {
      "component": "tsvector @@ to_tsquery(...)",
      "name": "Full-Text Match Operator",
      "description": "The `@@` operator returns true if the `tsvector` (the document's content) matches the `tsquery` (the search terms). This is the engine that powers the classification."
    },
    {
      "component": "GROUP BY category",
      "name": "Aggregation",
      "description": "After the inner query assigns a category to every document, this clause groups all the documents by their assigned category and `COUNT(*)` tallies them up."
    }
  ],
  "scenarios": [
    {
      "title": "Automated Tagging of Customer Support Tickets",
      "details": {
        "setup": "A company has a `support_tickets` table with thousands of untagged entries. The table contains a `tsvector_content` column generated from the ticket's subject and body, with a GIN index on it for performance.",
        "goal": "To automatically classify all incoming and existing tickets into 'Billing', 'Technical Issue', or 'General Inquiry' to help route them to the correct department and analyze common problem areas.",
        "outcome": "The query produces a summary table showing the total count of tickets for each category, providing immediate business insights without manual effort."
      },
      "example": {
        "query": "SELECT category, COUNT(*) as ticket_count\nFROM (\n  SELECT CASE\n    WHEN tsvector_content @@ to_tsquery('english', 'invoice | payment | refund | subscription') THEN 'Billing'\n    WHEN tsvector_content @@ to_tsquery('english', 'error | bug | crash | connect | login') THEN 'Technical Issue'\n    ELSE 'General Inquiry'\n  END as category\n  FROM support_tickets\n) as classified_tickets\nGROUP BY category\nORDER BY ticket_count DESC;",
        "explanation": "This query uses a subquery to first assign a category to each ticket. The `CASE` statement checks the ticket's text vector against different sets of keywords joined by ' | ' (OR). The outer query then groups the results to count how many tickets fall into each category, ordering them for a clear report."
      },
      "notes": [
        "For this query to be performant on a large table, a GIN index on the `tsvector_content` column is absolutely essential.",
        "The quality of classification depends entirely on the keywords chosen for each category. These may need to be refined over time.",
        "This method is great for rule-based classification. For more complex needs, consider exploring PostgreSQL's machine learning extensions like `madlib`."
      ]
    }
  ]
};