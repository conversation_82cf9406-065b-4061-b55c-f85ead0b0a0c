export const command48 = {
  // --- Converted to the 'query' type ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Use Cases",
    "Search Analytics"
  ],
  "description": "Log search queries, result counts, and timestamps to a dedicated table for analytics. This helps in understanding user behavior, optimizing search relevance, and identifying content gaps.",

  // --- Query-specific fields for the operational query ---
  "query_template": "INSERT INTO search_log (query, results_count) SELECT '[search_term]', COUNT(*) FROM [documents_table] WHERE [vector_column] @@ to_tsquery('[config]', '[search_term_for_tsquery]');",
  "components": [
    {
      "component": "INSERT INTO search_log (...)",
      "name": "Log Insertion",
      "description": "The statement that adds a new row to the analytics table for each search performed."
    },
    {
      "component": "SELECT '[search_term]', COUNT(*)",
      "name": "Data Capture",
      "description": "A subquery that captures the literal search term string and calculates the total number of matching documents for that term."
    },
    {
      "component": "@@ to_tsquery(...)",
      "name": "Full-Text Search Match",
      "description": "The core full-text search condition that finds the matching documents."
    }
  ],

  // --- Scenario explaining the entire workflow ---
  "scenarios": [
    {
      "title": "Implementing Search Analytics to Improve a Knowledge Base",
      "details": {
        "setup": "You run a documentation website with a full-text search feature. You have no data on what users are searching for, which queries yield zero results, or which topics are most popular.",
        "goal": "To create a system that logs every search query performed by users, allowing you to analyze search patterns and improve the content and search configuration.",
        "outcome": "A `search_log` table is created and begins collecting data. You can now run queries against this table to find the most frequent search terms or identify common queries that return no results."
      },
      "example": {
        // --- The example shows the recurring query ---
        "query": "INSERT INTO search_log (query, results_count) SELECT 'postgresql performance', COUNT(*) FROM documents WHERE document_vector @@ to_tsquery('english', 'postgresql & performance');",
        "explanation": "This two-part process first requires a one-time setup of the logging table: `CREATE TABLE search_log (id SERIAL PRIMARY KEY, query TEXT, results_count INTEGER, search_time TIMESTAMP DEFAULT NOW());`. \n\nAfter that, the application executes the `INSERT` statement each time a user searches. This example logs a search for 'postgresql performance' and dynamically counts the number of matching documents to store in the `results_count` column."
      },
      "notes": [
        "For a high-traffic application, consider performing the INSERT into the log table asynchronously to avoid adding latency to the user's search request.",
        "Periodically analyze this table to gain insights, for example: `SELECT query, COUNT(query) FROM search_log GROUP BY query ORDER BY count DESC LIMIT 20;` to find the top 20 most popular queries.",
        "Consider adding an index to the `query` column in `search_log` to speed up analytics queries on the log table itself."
      ]
    }
  ]
};