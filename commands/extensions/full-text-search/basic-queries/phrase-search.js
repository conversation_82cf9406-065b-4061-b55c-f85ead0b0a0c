export const command36 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Basic Queries",
    "Phrase Search"
  ],
  "description": "Performs a full-text search for an exact, multi-word phrase, ensuring the words appear in the specified order. This is essential for searching for specific titles, quotes, or technical terms.",
  "query_template": "SELECT [columns] FROM [table] WHERE [tsvector_column] @@ phraseto_tsquery('[config]', '[search_phrase]');",
  "components": [
    {
      "component": "phraseto_tsquery()",
      "name": "Phrase to tsquery Function",
      "description": "Converts a plain text phrase into a `tsquery` that matches the sequence of words. It automatically inserts the `<->` (followed by) operator between the lexemes."
    },
    {
      "component": "@@",
      "name": "Match Operator",
      "description": "The full-text search operator that returns `true` if the `tsvector` (the document) matches the `tsquery` (the search phrase)."
    },
    {
      "component": "[tsvector_column]",
      "name": "Document Vector",
      "description": "The column of type `tsvector` that contains the pre-processed, searchable text of your documents."
    }
  ],
  "scenarios": [
    {
      "title": "Searching for Exact Error Messages in a Knowledge Base",
      "details": {
        "setup": "You manage a technical support knowledge base stored in a PostgreSQL database. Support agents need to find articles that contain the exact error message 'unique constraint violated', but their searches for the phrase are returning irrelevant articles that just contain the words 'unique' and 'violated' separately.",
        "goal": "To construct a query that finds only those articles containing the precise, ordered phrase 'unique constraint violated'.",
        "outcome": "The query successfully returns a targeted list of articles, allowing support agents to quickly find the correct troubleshooting guide."
      },
      "example": {
        "query": "SELECT title, url FROM articles WHERE content_vector @@ phraseto_tsquery('english', 'unique constraint violated');",
        "explanation": "This query uses `phraseto_tsquery` to create a search query that looks for 'unique' immediately followed by 'constraint' immediately followed by 'violated'. This is far more precise than `plainto_tsquery('unique constraint violated')`, which would match documents containing the three words anywhere."
      },
      "notes": [
        "For optimal performance, ensure you have a GIN index on the `tsvector` column (`content_vector` in this example).",
        "Use `plainto_tsquery()` when you want to find documents containing all the words, but not necessarily in order.",
        "Use `websearch_to_tsquery()` (PostgreSQL 11+) to provide users with a more flexible, Google-style search syntax that allows for quoted phrases and exclusions."
      ]
    }
  ]
};