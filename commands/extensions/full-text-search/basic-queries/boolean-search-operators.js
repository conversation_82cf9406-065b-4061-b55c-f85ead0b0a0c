export const command35 = {
  // --- Type identifier is set to 'query' ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Basic Queries",
    "Boolean Search Operators"
  ],
  "description": "Performs a full-text search using boolean operators to create complex and precise search logic, allowing for combinations of AND, OR, and NOT conditions.",

  // --- Query-specific fields ---
  "query_template": "SELECT [columns] FROM [table] WHERE [tsvector_column] @@ to_tsquery('english', '[term1] & ([term2] | [term3])');",
  "components": [
    {
      "component": "@@",
      "name": "Matches Operator",
      "description": "The operator used to test if a `tsvector` (a document) matches a `tsquery` (a search query)."
    },
    {
      "component": "to_tsquery()",
      "name": "Query Function",
      "description": "Parses a string into a `tsquery` type, interpreting boolean operators."
    },
    {
      "component": "&",
      "name": "AND Operator",
      "description": "Requires that both terms must be present in the document."
    },
    {
      "component": "|",
      "name": "OR Operator",
      "description": "Requires that at least one of the terms must be present."
    },
    {
      "component": "!",
      "name": "NOT Operator",
      "description": "Requires that the term must NOT be present in the document."
    },
    {
      "component": "<->",
      "name": "Followed By Operator",
      "description": "A more advanced operator requiring one term to be immediately followed by another."
    }
  ],

  // --- Scenario for a real-world use case ---
  "scenarios": [
    {
      "title": "Implementing an 'Advanced Search' Feature for a Documentation Platform",
      "details": {
        "setup": "You are building a technical knowledge base with thousands of articles stored in a `articles` table. The table has a `tsvector_content` column for full-text search. Users need to perform more specific searches than simple keyword matching.",
        "goal": "To find all articles that are about PostgreSQL and specifically mention either 'performance' or 'optimization', while excluding any articles that are tagged as 'obsolete'.",
        "outcome": "A precise list of article titles is returned, matching the complex user criteria and improving the relevance of search results."
      },
      "example": {
        "query": "SELECT title FROM articles WHERE tsvector_content @@ to_tsquery('english', 'postgresql & (performance | optimization) & !obsolete');",
        "explanation": "This query effectively translates the user's need. It finds documents where `postgresql` MUST be present, AND either `performance` OR `optimization` must also be present, AND the term `obsolete` MUST NOT be present. The parentheses group the OR condition, ensuring it's evaluated before the AND conditions."
      },
      "notes": [
        "Boolean operators are fundamental for building powerful search interfaces, allowing users to refine their queries interactively.",
        "Always ensure you have a GIN index on your `tsvector` column to make these queries fast and efficient. For example: `CREATE INDEX articles_fts_idx ON articles USING GIN(tsvector_content);`"
      ]
    }
  ]
};