export const command34 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Basic Queries",
    "Simple Text Search"
  ],
  "description": "Performs a basic full-text search to find documents containing a specific word. This leverages PostgreSQL's built-in text search capabilities for language-aware searching.",
  "query_template": "SELECT [columns] FROM [table] WHERE [tsvector_column] @@ to_tsquery('[config]', '[search_term]');",
  "components": [
    {
      "component": "@@",
      "name": "Matches Operator",
      "description": "The core full-text search operator. It returns true if the `tsvector` (the document) matches the `tsquery` (the search term)."
    },
    {
      "component": "to_tsquery('[config]', '[search_term]')",
      "name": "Text Search Query",
      "description": "A function that converts a plain text search string into the special `tsquery` type. It normalizes the word (e.g., stemming) based on the specified dictionary (e.g., 'english')."
    },
    {
      "component": "[tsvector_column]",
      "name": "TSVector Column",
      "description": "The column that stores the pre-processed document text in a format optimized for searching. This column should have a GIN index for performance."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing a Simple Search Bar for a Blog or Knowledge Base",
      "details": {
        "setup": "You are managing a blog with an `articles` table that includes `title`, `body`, and a `body_tsvector` column (of type `tsvector`). This `tsvector` column is automatically updated with the content of the `body` whenever an article is saved.",
        "goal": "To efficiently find all articles that mention the word 'performance', so they can be displayed on a search results page.",
        "outcome": "A list of titles and body content for all articles matching the search term is returned quickly, even with thousands of articles in the database."
      },
      "example": {
        "query": "SELECT title, body FROM articles WHERE body_tsvector @@ to_tsquery('english', 'performance');",
        "explanation": "This query uses the `@@` operator to match the search query for 'performance' against the pre-processed `body_tsvector` column. The 'english' configuration allows the search to be language-aware, so it will also match variations like 'performing' or 'performed' (stemming)."
      },
      "notes": [
        "For this query to be fast on a large table, you MUST create a GIN index on the `tsvector` column. Example: `CREATE INDEX articles_tsvector_idx ON articles USING GIN(body_tsvector);`",
        "The `to_tsquery` function is the counterpart to `to_tsvector`. Using the same configuration for both is essential for accurate matching.",
        "For searching phrases or multiple words, you can use operators within the `to_tsquery` string, such as '&' (AND), '|' (OR), and '<->' (FOLLOWED BY)."
      ]
    }
  ]
};