export const command42 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Features",
    "Multi-language Search Index"
  ],
  "description": "Creates a single GIN index to enable efficient, linguistically-aware full-text search across content in multiple languages (e.g., English and Spanish).",
  "query_template": "CREATE INDEX [index_name] ON [table_name] USING GIN( (setweight(to_tsvector('[lang1]', [column1]), '[weight]') || setweight(to_tsvector('[lang2]', [column1]), '[weight]')) );",
  "components": [
    {
      "component": "CREATE INDEX ... USING GIN",
      "name": "GIN Index",
      "description": "Creates a Generalized Inverted Index (GIN), which is the recommended index type for full-text search as it is optimized for indexing composite values like tsvectors."
    },
    {
      "component": "to_tsvector('[language]', ...)",
      "name": "Text to Vector Conversion",
      "description": "A core function that parses text into a `tsvector`, applying language-specific rules for stemming (e.g., 'running' becomes 'run') and removing stop words (e.g., 'a', 'the')."
    },
    {
      "component": "setweight(tsvector, '[A-D]')",
      "name": "Document Ranking",
      "description": "Assigns a weight ('A', 'B', 'C', or 'D') to a tsvector. This allows you to rank search results, giving higher priority to matches found in a title (Weight 'A') than in the body text (Weight 'B')."
    },
    {
      "component": "||",
      "name": "TSVector Concatenation",
      "description": "The operator used to combine multiple tsvectors into a single vector that can be stored in the index."
    }
  ],
  "scenarios": [
    {
      "title": "Building a Search Engine for a Bilingual Knowledge Base",
      "details": {
        "setup": "You manage a corporate knowledge base stored in a `kb_articles` table. This table has `title` and `content` columns, and articles are written in either English or Spanish. Employees need to search for articles using terms from either language.",
        "goal": "To implement a fast and relevant search feature that correctly handles stemming for both languages and considers matches in the `title` to be more important than matches in the `content`.",
        "outcome": "A single, powerful GIN index is created on the `kb_articles` table. This index allows search queries to efficiently find documents in either language, with results correctly ranked by relevance."
      },
      "example": {
        "query": "CREATE INDEX idx_fts_kb_articles ON kb_articles USING GIN(\n  (setweight(to_tsvector('english', title), 'A') || \n   setweight(to_tsvector('spanish', title), 'A') ||\n   setweight(to_tsvector('english', content), 'B') ||\n   setweight(to_tsvector('spanish', content), 'B'))\n);",
        "explanation": "This query creates a single `tsvector` for the GIN index. It processes the `title` and `content` through both English and Spanish parsers. Title matches are weighted 'A' (highest), and content matches are weighted 'B'. Concatenating them all (`||`) creates a comprehensive search document for each row, enabling one query to search across both languages and fields."
      },
      "notes": [
        "This approach can be extended by adding more languages (e.g., `to_tsvector('french', title)`).",
        "For improved performance and simpler queries, consider creating a separate, generated column to store the combined tsvector and then creating the index on that column.",
        "Remember that your search queries must also use `to_tsvector` to match against this index effectively."
      ]
    }
  ]
};