export const command40 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Features",
    "Fuzzy Search with Similarity"
  ],
  "description": "Enables powerful and efficient fuzzy text search using trigram similarity. This is ideal for handling typos, misspellings, and minor variations in user search queries.",
  "query_template": "SELECT [column], similarity([column], '[search_term]') AS sim FROM [table] WHERE [column] % '[search_term]' ORDER BY sim DESC;",
  "components": [
    {
      "component": "pg_trgm",
      "name": "Trigram Extension",
      "description": "The core PostgreSQL extension that provides functions and operators to determine the similarity of text based on trigram (3-character chunk) matching."
    },
    {
      "component": "similarity(text, text)",
      "name": "Similarity Function",
      "description": "A function from the pg_trgm extension that calculates a score between 0 (completely different) and 1 (identical) indicating how similar two strings are."
    },
    {
      "component": "%",
      "name": "Similarity Operator",
      "description": "An operator that returns true if two strings are similar enough. The threshold is configurable but defaults to 0.3. It is indexable for high performance."
    },
    {
      "component": "ORDER BY sim DESC",
      "name": "Ranking Results",
      "description": "Orders the results from most similar to least similar, ensuring the most relevant matches appear first."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing a Typo-Tolerant Search for a Product Catalog",
      "details": {
        "setup": "You are managing a website with a product search feature. Users frequently make spelling mistakes (e.g., 'smartfone' instead of 'smartphone'), leading to no results. You have a `products` table with a `name` column, and you have run `CREATE EXTENSION IF NOT EXISTS pg_trgm;` to enable the extension.",
        "goal": "To provide relevant product search results even when the user's query contains typos, improving user experience and engagement.",
        "outcome": "A search for 'wirless headfones' successfully returns a ranked list of products, including 'Wireless Headphones', 'Wireless Headset', and other similar items."
      },
      "example": {
        "query": "SELECT name, similarity(name, 'wirless headfones') AS sim FROM products WHERE name % 'wirless headfones' ORDER BY sim DESC LIMIT 10;",
        "explanation": "This query uses the `%` operator to efficiently find all product names that are 'similar' to the misspelled query 'wirless headfones'. The `similarity()` function is then used to calculate a precise score for each match, and `ORDER BY sim DESC` ranks the most relevant items first, providing a forgiving search experience."
      },
      "notes": [
        "For this query to be fast on a large table, you MUST create a GIN or GiST index. A GIN index is generally faster for this use case: `CREATE INDEX idx_products_name_trgm ON products USING gin (name gin_trgm_ops);`",
        "You can adjust the similarity threshold required by the `%` operator by running `SET pg_trgm.similarity_threshold = 0.4;` (the default is 0.3).",
        "The `pg_trgm` extension is highly effective for short-to-medium length text, like names, titles, and tags."
      ]
    }
  ]
};