export const command41 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Full-Text Search",
    "Advanced Features",
    "Search Suggestions"
  ],
  "description": "Generates a list of search suggestions (autocomplete) by finding the most frequently occurring terms in the text corpus that match a user's input prefix.",
  "query_template": "SELECT word FROM ts_stat('SELECT [tsvector_column] FROM [table_name]') WHERE nentry > [min_frequency] AND word ILIKE '[prefix]%' ORDER BY nentry DESC LIMIT [count];",
  "components": [
    {
      "component": "ts_stat('SELECT ...')",
      "name": "Text Corpus Statistics",
      "description": "A powerful function that inspects a tsvector column across a table and returns statistics for each distinct word (lexeme), including its frequency."
    },
    {
      "component": "word",
      "name": "Output Word",
      "description": "The column returned by `ts_stat` that represents the actual term from the text."
    },
    {
      "component": "nentry",
      "name": "Total Occurrences",
      "description": "A column from `ts_stat` indicating the total number of times a word appears across all documents. It's an excellent metric for ranking suggestions by popularity."
    },
    {
      "component": "WHERE word ILIKE '[prefix]%'",
      "name": "Prefix Matching",
      "description": "Filters the results to include only those words that begin with the text the user has typed so far (e.g., 'postgre')."
    },
    {
      "component": "ORDER BY nentry DESC",
      "name": "Ranking",
      "description": "Sorts the matching suggestions, placing the most common terms at the top of the list."
    }
  ],
  "scenarios": [
    {
      "title": "Implementing an Autocomplete Search Feature for a Knowledge Base",
      "details": {
        "setup": "You are managing a technical documentation website with thousands of articles. The content is stored in a `documents` table, which includes a `document_vector` column of type `tsvector` used for full-text search.",
        "goal": "To provide users with real-time search suggestions as they type in the search bar. This improves the user experience and helps guide them toward relevant content.",
        "outcome": "When a user types 'postgre' into the search box, the application executes a query that returns a list of the 10 most common matching terms (e.g., 'postgresql', 'postgres', 'pg_dump'), which are then displayed in a dropdown suggestion menu."
      },
      "example": {
        "query": "SELECT word FROM ts_stat('SELECT document_vector FROM documents') WHERE nentry > 5 AND word ILIKE 'postgre%' ORDER BY nentry DESC LIMIT 10;",
        "explanation": "This query analyzes the entire text corpus via `ts_stat`. It filters for words that start with the user's input ('postgre%'), appear more than 5 times (`nentry > 5`) to exclude rare terms and typos, and then returns the top 10 most frequent results. This list is perfectly suited for populating an autocomplete UI."
      },
      "notes": [
        "For a live autocomplete feature, performance is critical. Running `ts_stat` on a very large table can be slow.",
        "A common optimization is to pre-calculate these statistics and store them in a separate table or a materialized view that is refreshed periodically (e.g., every hour). The application then queries this much smaller, faster table.",
        "To speed up the `ILIKE` clause, you can create a trigram index (`CREATE EXTENSION pg_trgm; CREATE INDEX ... ON your_stats_table USING GIN (word gin_trgm_ops);`) on the `word` column of your statistics table."
      ]
    }
  ]
};
