export const command49 = {
  // --- Type identifier is 'query' ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "UUID and Crypto",
    "Install UUID Extension"
  ],
  "description": "Installs the 'uuid-ossp' extension to add functions for generating Universally Unique Identifiers (UUIDs) within the database.",
  
  // --- Query-specific fields ---
  "query_template": "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "Extension Installation Command",
      "description": "The standard SQL command to install a new extension into the current database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Conditional Clause",
      "description": "An optional but recommended clause that prevents the command from failing if the extension has already been installed."
    },
    {
      "component": "\"uuid-ossp\"",
      "name": "Extension Name",
      "description": "The specific extension that provides functions for generating various types of UUIDs."
    }
  ],

  // --- <PERSON><PERSON><PERSON> for practical application ---
  "scenarios": [
    {
      "title": "Ensuring Unique Primary Keys for a New User Accounts Table",
      "details": {
        "setup": "You are designing the database schema for a new application. You need to create a `users` table where the primary key (`id`) is a non-sequential, globally unique identifier to prevent enumeration attacks and simplify data federation in the future.",
        "goal": "To prepare the database for UUID generation and then create the `users` table using a UUID as the default value for its primary key.",
        "outcome": "The 'uuid-ossp' extension is installed, and the `users` table is created. Every new record inserted into this table will automatically receive a unique version 4 UUID as its ID."
      },
      "example": {
        "query": "-- Step 1: Ensure the UUID generation functions are available\nCREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";\n\n-- Step 2: Use a UUID function as a default value in your table\nCREATE TABLE users (\n    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),\n    username TEXT NOT NULL UNIQUE,\n    email TEXT NOT NULL UNIQUE,\n    created_at TIMESTAMPTZ DEFAULT NOW()\n);",
        "explanation": "First, `CREATE EXTENSION` adds the UUID functionality to the database. Then, in the `CREATE TABLE` statement, `DEFAULT uuid_generate_v4()` sets the `id` column to automatically generate a new, random UUID for each user upon creation. This is more secure and scalable than using a sequential integer."
      },
      "notes": [
        "For PostgreSQL 13 and newer, the `gen_random_uuid()` function is built-in and does not require installing any extensions. It is the recommended modern approach.",
        "`uuid_generate_v4()` creates a random UUID and is generally preferred for primary keys. `uuid_generate_v1()` creates a time-based UUID, which can leak information about creation time."
      ]
    }
  ]
};