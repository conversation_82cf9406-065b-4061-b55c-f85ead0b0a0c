export const command50 = {
  // --- Identifier for application logic ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "UUID and Crypto",
    "Install Crypto Extension"
  ],
  "description": "Installs the pgcrypto extension, providing a powerful suite of cryptographic functions directly within the database. It is essential for tasks like secure password hashing and data encryption.",
  
  // --- Query-specific fields ---
  "query_template": "CREATE EXTENSION IF NOT EXISTS pgcrypto;",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "SQL Command",
      "description": "The standard SQL command to load an extension into the current database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Clause",
      "description": "A safety clause that prevents the command from failing if the pgcrypto extension has already been installed."
    },
    {
      "component": "pgcrypto",
      "name": "Extension Name",
      "description": "The name of the extension that provides cryptographic functions like crypt(), gen_salt(), encrypt(), and decrypt()."
    }
  ],
  
  // --- Real-world scenario ---
  "scenarios": [
    {
      "title": "Implementing Secure User Password Storage",
      "details": {
        "setup": "You are building a new application with a 'users' table that will store usernames and passwords. Storing passwords in plain text is a critical security vulnerability.",
        "goal": "To securely store user passwords by hashing them with a salt, so they cannot be reversed into plain text, even if the database is compromised.",
        "outcome": "The `pgcrypto` extension is installed in the database. Its `gen_salt()` and `crypt()` functions are now available to create secure password hashes."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS pgcrypto;",
        "explanation": "This command must be run once on your database. It registers all the necessary functions for securely handling passwords directly within PostgreSQL, forming the foundation for a secure authentication system."
      },
      "notes": [
        "This extension must be enabled on a per-database basis.",
        "After installation, you can hash a new password using: `INSERT INTO users (username, password_hash) VALUES ('new_user', crypt('user_password', gen_salt('bf')));`",
        "The 'bf' argument for `gen_salt` specifies the Blowfish algorithm, which is a strong and recommended choice for password hashing."
      ]
    }
  ]
};