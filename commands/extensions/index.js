import { command0 } from './extension-management/list-available-extensions.js';
import { command1 } from './extension-management/list-installed-extensions.js';
import { command2 } from './extension-management/install-extension.js';
import { command3 } from './postgis-spatial-data/installation/enable-postgis-core.js';
import { command4 } from './postgis-spatial-data/installation/enable-postgis-topology.js';
import { command5 } from './postgis-spatial-data/installation/enable-postgis-raster.js';
import { command6 } from './postgis-spatial-data/installation/check-postgis-version.js';
import { command7 } from './postgis-spatial-data/basic-geometry-types/create-point-table.js';
import { command8 } from './postgis-spatial-data/basic-geometry-types/create-linestring-table.js';
import { command9 } from './postgis-spatial-data/basic-geometry-types/create-polygon-table.js';
import { command10 } from './postgis-spatial-data/data-input/insert-point-data.js';
import { command11 } from './postgis-spatial-data/data-input/insert-linestring-data.js';
import { command12 } from './postgis-spatial-data/data-input/insert-polygon-data.js';
import { command13 } from './postgis-spatial-data/spatial-queries/distance-queries.js';
import { command14 } from './postgis-spatial-data/spatial-queries/intersection-queries.js';
import { command15 } from './postgis-spatial-data/spatial-queries/area-and-length-calculations.js';
import { command16 } from './postgis-spatial-data/advanced-spatial-operations/spatial-joins.js';
import { command17 } from './postgis-spatial-data/advanced-spatial-operations/buffer-operations.js';
import { command18 } from './postgis-spatial-data/advanced-spatial-operations/centroid-and-envelope.js';
import { command19 } from './postgis-spatial-data/coordinate-system-operations/transform-coordinates.js';
import { command20 } from './postgis-spatial-data/coordinate-system-operations/list-available-projections.js';
import { command21 } from './postgis-spatial-data/spatial-indexing/create-spatial-index.js';
import { command22 } from './postgis-spatial-data/spatial-indexing/analyze-spatial-index-usage.js';
import { command23 } from './postgis-spatial-data/import-export/import-shapefile.js';
import { command24 } from './postgis-spatial-data/import-export/export-to-shapefile.js';
import { command25 } from './postgis-spatial-data/import-export/import-geojson.js';
import { command26 } from './full-text-search/setup/create-documents-table.js';
import { command27 } from './full-text-search/setup/create-text-search-configuration.js';
import { command28 } from './full-text-search/setup/customize-text-search-configuration.js';
import { command29 } from './full-text-search/indexing/create-basic-full-text-index.js';
import { command30 } from './full-text-search/indexing/create-weighted-full-text-index.js';
import { command31 } from './full-text-search/indexing/create-stored-tsvector-index.js';
import { command32 } from './full-text-search/data-management/insert-documents-with-tsvector.js';
import { command33 } from './full-text-search/data-management/create-tsvector-update-trigger.js';
import { command34 } from './full-text-search/basic-queries/simple-text-search.js';
import { command35 } from './full-text-search/basic-queries/boolean-search-operators.js';
import { command36 } from './full-text-search/basic-queries/phrase-search.js';
import { command37 } from './full-text-search/advanced-queries/ranked-search-results.js';
import { command38 } from './full-text-search/advanced-queries/weighted-ranking.js';
import { command39 } from './full-text-search/advanced-queries/highlighting-search-results.js';
import { command40 } from './full-text-search/advanced-features/fuzzy-search-with-similarity.js';
import { command41 } from './full-text-search/advanced-features/search-suggestions.js';
import { command42 } from './full-text-search/advanced-features/multi-language-search.js';
import { command43 } from './full-text-search/performance-optimization/analyze-search-performance.js';
import { command44 } from './full-text-search/performance-optimization/search-statistics.js';
import { command45 } from './full-text-search/maintenance/update-search-statistics.js';
import { command46 } from './full-text-search/maintenance/rebuild-full-text-index.js';
import { command47 } from './full-text-search/advanced-use-cases/document-classification.js';
import { command48 } from './full-text-search/advanced-use-cases/search-analytics.js';
import { command49 } from './uuid-and-crypto/install-uuid-extension.js';
import { command50 } from './uuid-and-crypto/install-crypto-extension.js';
import { command51 } from './foreign-data-wrappers/install-postgres-fdw.js';
import { command52 } from './foreign-data-wrappers/create-foreign-server.js';
import { command53 } from './time-series-data/install-timescaledb.js';

export const extensionsCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15,
  command16,
  command17,
  command18,
  command19,
  command20,
  command21,
  command22,
  command23,
  command24,
  command25,
  command26,
  command27,
  command28,
  command29,
  command30,
  command31,
  command32,
  command33,
  command34,
  command35,
  command36,
  command37,
  command38,
  command39,
  command40,
  command41,
  command42,
  command43,
  command44,
  command45,
  command46,
  command47,
  command48,
  command49,
  command50,
  command51,
  command52,
  command53
];

export function validateExtensionsCommands() {
  return extensionsCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Extensions and Advanced Features";
    const hasDescription = cmd.description;
    const hasType = cmd.type;

    if (!hasPath || !hasDescription || !hasType) {
      return false;
    }

    switch (cmd.type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
