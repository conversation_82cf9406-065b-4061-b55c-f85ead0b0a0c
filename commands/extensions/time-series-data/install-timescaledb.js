export const command53 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Time Series Data",
    "Install TimescaleDB"
  ],
  "description": "Enables the TimescaleDB extension within a specific PostgreSQL database, unlocking functionalities for handling time-series data like hypertables and specialized functions.",
  "query_template": "CREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "Enable Extension",
      "description": "The standard SQL command to install an extension into the current database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Conditional Clause",
      "description": "A safety clause that prevents the command from failing if the extension is already installed."
    },
    {
      "component": "CASCADE",
      "name": "Cascade Option",
      "description": "Automatically installs any other extensions that TimescaleDB depends on, ensuring all prerequisites are met."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a Database for IoT Sensor Data Monitoring",
      "details": {
        "setup": "You are tasked with building a system to ingest and analyze millions of data points per hour from environmental sensors. You have a new PostgreSQL database named `sensor_data_db` and have already installed the TimescaleDB binaries on the server itself.",
        "goal": "To activate TimescaleDB's features within the `sensor_data_db` so you can create a hypertable to efficiently store and query the incoming time-series data.",
        "outcome": "The TimescaleDB extension is successfully enabled in the database. You can now use functions like `create_hypertable()` and `time_bucket()`."
      },
      "example": {
        "query": "-- Make sure you are connected to your target database, e.g., \\c sensor_data_db\nCREATE EXTENSION IF NOT EXISTS timescaledb CASCADE;",
        "explanation": "This query registers all of TimescaleDB's objects, data types, and functions with the current database. The `CASCADE` option is included to handle dependencies automatically. Using `IF NOT EXISTS` makes the script safely re-runnable without causing errors."
      },
      "notes": [
        "CRITICAL: This command activates the extension for a database, but it does NOT install the TimescaleDB software on your server. You must do that first by following the official TimescaleDB installation instructions for your operating system.",
        "You must have superuser privileges in PostgreSQL to run `CREATE EXTENSION`.",
        "After this step, your immediate next action is typically to create a table for your time-series data and then convert it into a hypertable using the `create_hypertable()` function."
      ]
    }
  ]
};