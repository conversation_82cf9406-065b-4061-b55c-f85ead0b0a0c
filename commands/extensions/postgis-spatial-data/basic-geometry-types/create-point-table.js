export const command7 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Basic Geometry Types",
    "Create Point Table"
  ],
  "description": "Creates a table to store geographic point data, which is the foundational step for any location-based application. This example uses the WGS 84 coordinate system, the standard for GPS and web mapping.",
  "query_template": "CREATE TABLE [table_name] (id SERIAL PRIMARY KEY, name TEXT, location GEOMETRY(POINT, 4326));",
  "components": [
    {
      "component": "GEOMETRY(POINT, 4326)",
      "name": "Spatial Column Definition",
      "description": "Defines a column with the 'GEOMETRY' type, specifically constrained to hold only 'POINT' data. The number 4326 specifies the Spatial Reference System Identifier (SRID)."
    },
    {
      "component": "POINT",
      "name": "Geometry Type",
      "description": "Specifies that this column will only store zero-dimensional geometries, representing single points in space (e.g., a specific address, a GPS reading)."
    },
    {
      "component": "4326",
      "name": "SRID (WGS 84)",
      "description": "The SRID for the World Geodetic System 1984. This is the most common coordinate system, used by GPS and most web mapping services like Google Maps and OpenStreetMap."
    }
  ],
  "scenarios": [
    {
      "title": "Storing Business Locations for a Store Finder Application",
      "details": {
        "setup": "You are building a 'store finder' feature for a retail company's website. You need to store the address, name, and geographic coordinates for hundreds of store locations.",
        "goal": "To create a robust and spatially-aware table in the database to hold all the necessary information for each store location.",
        "outcome": "A table named `stores` is created in the database. It includes a dedicated `location` column that is type-safe and ready to store standardized geographic coordinates."
      },
      "example": {
        "query": "CREATE TABLE stores (store_id SERIAL PRIMARY KEY, store_name VARCHAR(150) NOT NULL, address TEXT, location GEOMETRY(POINT, 4326));",
        "explanation": "This query creates a `stores` table with a `location` column designed to hold geographic points. Using `GEOMETRY(POINT, 4326)` ensures that only valid point data in the standard WGS 84 format can be inserted, which is essential for running reliable 'find nearby' queries later on."
      },
      "notes": [
        "After creating the table, the next step is to populate it using a function like `ST_GeomFromText('POINT(-73.9855 40.7580)', 4326)` in your INSERT statement.",
        "For fast spatial queries (e.g., finding all stores within 5 miles), you MUST create a spatial index on the location column: `CREATE INDEX idx_stores_location ON stores USING GIST (location);`"
      ]
    }
  ]
};