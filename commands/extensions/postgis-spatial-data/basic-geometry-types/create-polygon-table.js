export const command9 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Basic Geometry Types",
    "Create Polygon Table"
  ],
  "description": "Creates a table designed to store polygonal spatial data, such as administrative boundaries (countries, states) or property lots. This structure is fundamental for any area-based spatial analysis.",
  "query_template": "CREATE TABLE [table_name] (id SERIAL PRIMARY KEY, [column_name] [data_type], [geom_column_name] GEOMETRY(POLYGON, [SRID]));",
  "components": [
    {
      "component": "GEOMETRY(POLYGON, 4326)",
      "name": "Typed Geometry Column",
      "description": "Defines a column to store geometry data. Specifying `<PERSON><PERSON><PERSON><PERSON><PERSON>` enforces that only polygon data can be inserted, ensuring data integrity. The `4326` is the SRID (Spatial Reference ID) for WGS 84, the standard for GPS coordinates."
    },
    {
      "component": "SERIAL PRIMARY KEY",
      "name": "Unique Identifier",
      "description": "A standard auto-incrementing integer primary key for uniquely identifying each feature."
    }
  ],
  "scenarios": [
    {
      "title": "Storing US State Boundaries for a Data Visualization App",
      "details": {
        "setup": "You are building a business intelligence dashboard that needs to display sales data on a map of the United States. To do this, you first need a way to store the geographic shape of each state.",
        "goal": "To create a table named `us_states` that can store the name, abbreviation, and geographic boundary for all 50 states.",
        "outcome": "A table is created in the database, ready to be populated with state boundary data from a shapefile or other source. The structure ensures each boundary is stored as a polygon with the correct coordinate system."
      },
      "example": {
        "query": "CREATE TABLE us_states (id SERIAL PRIMARY KEY, name TEXT NOT NULL, abbrev CHAR(2) NOT NULL, boundary GEOMETRY(MULTIPOLYGON, 4326));",
        "explanation": "This query creates the `us_states` table. `GEOMETRY(MULTIPOLYGON, 4326)` is used because some states are composed of multiple disjointed areas (e.g., Hawaii's islands). Using `MULTIPOLYGON` allows you to store all parts of a state in a single row. The SRID is set to 4326 to align with standard latitude/longitude data."
      },
      "notes": [
        "**Pro Tip:** After populating the table with data, create a spatial index on the `boundary` column to dramatically speed up queries. Use the command: `CREATE INDEX idx_us_states_boundary ON us_states USING GIST(boundary);`",
        "A `POLYGON` can only store a single, contiguous area. Use `MULTIPOLYGON` for features that have separate parts, like countries with islands or territories.",
        "Choosing the correct SRID is critical. 4326 is the most common for global data, but using a local projected coordinate system might be better for performance and accuracy if your data is regional."
      ]
    }
  ]
};