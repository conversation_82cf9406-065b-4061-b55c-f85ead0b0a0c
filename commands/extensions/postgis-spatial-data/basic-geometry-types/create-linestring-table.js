export const command8 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Basic Geometry Types",
    "Create LineString Table"
  ],
  "description": "Creates a table designed to store linear geographic features, such as roads, rivers, or pipelines, using the LineString geometry type.",
  "query_template": "CREATE TABLE [table_name] (id SERIAL PRIMARY KEY, [columns...], [geom_column] GEOMETRY(LINESTRING, [SRID]));",
  "components": [
    {
      "component": "GEOMETRY(LineString, [SRID])",
      "name": "Geometry Type Definition",
      "description": "Defines a column to store geometry data. It specifies the type of geometry (e.g., LineString) and the Spatial Reference Identifier (SRID) for the coordinate system."
    },
    {
      "component": "LINESTRING",
      "name": "LineString Geometry",
      "description": "A PostGIS data type representing a path, composed of a sequence of two or more connected points. Ideal for modeling linear features."
    },
    {
      "component": "4326",
      "name": "SRID (Spatial Reference ID)",
      "description": "The SRID specifies the coordinate system for the data. 4326 (WGS 84) is the most common standard for global latitude and longitude data, used by GPS and web maps."
    }
  ],
  "scenarios": [
    {
      "title": "Modeling a City's Transportation Network",
      "details": {
        "setup": "A municipal planning authority needs to create a digital inventory of all city streets to power a new route-planning application and manage road maintenance schedules.",
        "goal": "To design a database table that can store each street's path, name, and classification (e.g., 'motorway', 'residential', 'cyclepath').",
        "outcome": "A `streets` table is created in the database, ready to be populated with data from GIS files or GPS tracking."
      },
      "example": {
        "query": "CREATE TABLE streets (street_id SERIAL PRIMARY KEY, street_name VARCHAR(255), classification VARCHAR(50), geom GEOMETRY(LINESTRING, 4326));",
        "explanation": "This query creates a `streets` table with a unique ID, text fields for the name and classification, and a `geom` column to hold the actual path. The `GEOMETRY(LINESTRING, 4326)` constraint ensures that only linear features using the standard WGS 84 coordinate system can be inserted."
      },
      "notes": [
        "After populating the table with data, it is crucial to create a spatial index on the 'geom' column (e.g., `CREATE INDEX idx_streets_geom ON streets USING GIST(geom);`) for fast spatial query performance.",
        "For complex road systems with separate northbound/southbound lanes, consider using `MULTILINESTRING` to store multiple lines under a single record.",
        "Choosing the correct SRID is critical. If working with local planar data, use a regional SRID (e.g., a UTM zone) for more accurate distance and area calculations."
      ]
    }
  ]
};