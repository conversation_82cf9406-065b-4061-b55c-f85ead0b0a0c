export const command22 = {
  // --- New type identifier ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Spatial Indexing",
    "Analyze Spatial Index Usage"
  ],
  "description": "Analyzes the query execution plan to verify if a spatial index is being used, which is critical for the performance of geospatial queries.",

  // --- Query-specific fields ---
  "query_template": "EXPLAIN ANALYZE SELECT [columns] FROM [table] WHERE [spatial_condition];",
  "components": [
    {
      "component": "EXPLAIN ANALYZE",
      "name": "Query Plan Analysis",
      "description": "A command that precedes your actual SELECT query. It executes the query and returns the detailed execution plan along with the actual time taken."
    },
    {
      "component": "ST_DWithin(...)",
      "name": "Spatial Condition",
      "description": "A PostGIS function used in the WHERE clause to filter data based on spatial relationships. The performance of this function relies on a spatial index."
    }
  ],

  // --- Scenario structure is preserved ---
  "scenarios": [
    {
      "title": "Diagnosing a Slow 'Find Nearby' Feature",
      "details": {
        "setup": "You have a `points_of_interest` table with a GIST spatial index on the `geom` column, but queries to find nearby locations are unexpectedly slow.",
        "goal": "To verify that PostgreSQL is using the spatial index (`poi_geom_idx`) for the 'find nearby' query, and not performing a slow, full-table scan.",
        "outcome": "The query plan confirms an 'Index Scan' is being performed, validating that the spatial index is effective."
      },
      "example": {
        // --- Note the use of 'query' instead of 'command' ---
        "query": "EXPLAIN ANALYZE SELECT name, category FROM points_of_interest WHERE ST_DWithin(geom, 'SRID=4326;POINT(-74.0060 40.7128)', 1000);",
        "explanation": "This query finds points of interest within 1km of a NYC coordinate. The `EXPLAIN ANALYZE` output should include a line like 'Index Scan using poi_geom_idx...', which confirms the index is working correctly."
      },
      "notes": [
        "If the output shows 'Seq Scan' (Sequential Scan), the index is not being used. This might require running `ANALYZE your_table;` to update table statistics.",
        "On very small tables, the query planner might correctly choose a 'Seq Scan' if it's faster than using an index."
      ]
    }
  ]
};