export const command21 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Spatial Indexing",
    "Create Spatial Index"
  ],
  "description": "Creates a spatial index on a geometry or geography column. This is one of the most critical steps for achieving high performance in any application that performs spatial queries.",
  "query_template": "CREATE INDEX [index_name] ON [table_name] USING GIST ([geometry_column]);",
  "components": [
    {
      "component": "CREATE INDEX [index_name] ON [table_name]",
      "name": "Index Definition",
      "description": "The standard SQL command to create a new index with a specific name on a given table."
    },
    {
      "component": "USING GIST",
      "name": "Index Type (GIST)",
      "description": "Specifies the index type as Generalized Search Tree (GIST). This is the most common and versatile index type for spatial data in PostGIS."
    },
    {
      "component": "([geometry_column])",
      "name": "Target Column",
      "description": "The name of the column that contains the spatial data (e.g., of type 'geometry' or 'geography')."
    }
  ],
  "scenarios": [
    {
      "title": "Accelerating a 'Find Nearby Venues' Feature",
      "details": {
        "setup": "You are building a mobile application that needs to find venues (restaurants, cafes, etc.) near a user's current location. The `venues` table has millions of records, and queries using `ST_DWithin` are taking several seconds to return, causing a poor user experience.",
        "goal": "To reduce the query response time from seconds to milliseconds by creating an efficient index on the `location_geom` column, which stores the venue coordinates.",
        "outcome": "A GIST index is created. Subsequent 'find nearby' queries are now extremely fast, and the application feature becomes highly responsive."
      },
      "example": {
        "query": "CREATE INDEX idx_venues_location_geom ON venues USING GIST (location_geom);",
        "explanation": "This query builds a GIST index named `idx_venues_location_geom` on the `location_geom` column. This index organizes the spatial data in a way that allows PostgreSQL to quickly eliminate vast areas from a search, focusing only on the bounding box relevant to the query, which drastically speeds up functions like `ST_DWithin` or `ST_Intersects`."
      },
      "notes": [
        "On a live production database with heavy traffic, consider using `CREATE INDEX CONCURRENTLY ...` to avoid locking the table against writes while the index is being built.",
        "After creating the index, it's a good practice to run `ANALYZE venues;` to update the table statistics for the query planner.",
        "While GIST is standard, SP-GIST (`USING SPGIST`) can sometimes be more performant for specific data types, like points that do not overlap."
      ]
    }
  ]
};