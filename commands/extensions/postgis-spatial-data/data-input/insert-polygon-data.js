export const command12 = {
  // --- Set type to 'query' ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Data Input",
    "Insert Polygon Data"
  ],
  "description": "Inserts polygon data into a geometry or geography column, typically to store boundaries, building footprints, or other area-based features.",

  // --- Query-specific fields ---
  "query_template": "INSERT INTO [table_name] ([column1], [geom_column]) VALUES ([value1], ST_GeomFromText('POLYGON((...))', [SRID]));",
  "components": [
    {
      "component": "INSERT INTO ... VALUES",
      "name": "SQL Insert Statement",
      "description": "The standard SQL command for adding a new row to a table."
    },
    {
      "component": "ST_GeomFromText('POLYGON((...))', 4326)",
      "name": "Geometry Creation",
      "description": "The core PostGIS function that converts a Well-Known Text (WKT) representation of a shape into a geometry object that can be stored and indexed."
    },
    {
      "component": "POLYGON((x1 y1, x2 y2, ...))",
      "name": "Well-Known Text (WKT)",
      "description": "A standard text format for representing vector geometry. For a polygon, the coordinates must form a closed loop (the first and last points are identical)."
    },
    {
      "component": "4326",
      "name": "Spatial Reference ID (SRID)",
      "description": "Specifies the coordinate system for the data. 4326 is the standard SRID for WGS 84, used by GPS and most web mapping services."
    }
  ],

  // --- Scenario for this query ---
  "scenarios": [
    {
      "title": "Mapping Administrative Zones for Urban Planning",
      "details": {
        "setup": "You are a GIS analyst working with a city planning department. You have a table named `planning_zones` designed to hold different land-use areas (commercial, residential, industrial). You have received the exact coordinates for a new 'Downtown Commercial' zone.",
        "goal": "To insert the new commercial zone's boundary into the `planning_zones` table so it can be visualized on a map and used in spatial analyses.",
        "outcome": "A new row for the 'Downtown Commercial' zone is successfully added to the table, with its boundary stored correctly in the `boundary` geometry column."
      },
      "example": {
        "query": "INSERT INTO planning_zones (zone_name, zone_type, boundary) VALUES ('Downtown Commercial', 'COM-1', ST_GeomFromText('POLYGON((-74.00 40.71, -73.99 40.71, -73.99 40.72, -74.00 40.72, -74.00 40.71))', 4326));",
        "explanation": "This query inserts a new record for the planning zone. It uses `ST_GeomFromText` to parse the WKT string representing the rectangular boundary of the zone. The SRID is set to 4326 to ensure it aligns with other global map data."
      },
      "notes": [
        "The coordinate pairs in the POLYGON string should be ordered logically to form the shape's perimeter.",
        "For complex polygons with holes (e.g., a park with a lake in the middle), the WKT format is `POLYGON((outer_ring), (inner_ring_hole))`.",
        "While WKT is common for examples, in a production application, data is often imported from shapefiles or GeoJSON using tools like `shp2pgsql` or `ogr2ogr`."
      ]
    }
  ]
};