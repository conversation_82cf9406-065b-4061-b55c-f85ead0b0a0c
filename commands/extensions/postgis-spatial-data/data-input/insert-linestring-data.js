export const command11 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Data Input",
    "Insert LineString Data"
  ],
  "description": "Inserts linear geographic data (like roads, rivers, or hiking trails) into a PostGIS-enabled table. The geometry is created from a series of connected points that form a line.",
  "query_template": "INSERT INTO [table_name] ([column1], [geom_column]) VALUES ('[value1]', [function_to_create_linestring]);",
  "components": [
    {
      "component": "INSERT INTO ... VALUES",
      "name": "SQL Insert Statement",
      "description": "The standard SQL command for adding a new row to a table."
    },
    {
      "component": "ST_GeomFromText('LINESTRING(...)', 4326)",
      "name": "Geometry from Text (WKT)",
      "description": "The most common function for creating geometries. It takes a string in Well-Known Text (WKT) format and a Spatial Reference ID (SRID). 4326 is the standard for GPS coordinates."
    },
    {
      "component": "ST_GeomFromGeoJSON('...')",
      "name": "Geometry from GeoJSON",
      "description": "An alternative function used to create a geometry directly from a GeoJSON string, which is common in web and API contexts."
    }
  ],
  "scenarios": [
    {
      "title": "Mapping a New Municipal Road Using GPS Coordinates (WKT)",
      "details": {
        "setup": "You are a GIS analyst for a city. A new road, 'Elm Street Extension', has been built, and you have its centerline coordinates from a GPS survey.",
        "goal": "To add the new road to the city's `roads` database table, which has columns for `name`, `road_type`, and a geometry column named `path`.",
        "outcome": "The 'Elm Street Extension' is successfully added to the database as a LINESTRING, making it available for network analysis and mapping applications."
      },
      "example": {
        "query": "INSERT INTO roads (name, road_type, path) VALUES ('Elm Street Extension', 'residential', ST_GeomFromText('LINESTRING(-122.41 37.77, -122.40 37.78, -122.39 37.79)', 4326));",
        "explanation": "This query inserts a new record for the road. The `ST_GeomFromText` function is used to convert the Well-Known Text (WKT) representation of the road's path, captured as a series of longitude/latitude pairs, into a PostGIS geometry object. The SRID 4326 explicitly defines the coordinate system as WGS 84."
      },
      "notes": [
        "In WKT, coordinates are space-separated, and points are comma-separated.",
        "Always ensure your data's SRID matches the one specified in the function call."
      ]
    },
    {
      "title": "Importing a Shipping Route from a GeoJSON API",
      "details": {
        "setup": "Your logistics application receives shipping route data from a third-party API. The route is provided as a GeoJSON LineString object.",
        "goal": "To directly insert the shipping route into your `shipping_lanes` table without needing to parse and reformat the GeoJSON string in your application code.",
        "outcome": "The route is stored correctly in the `route_geom` column, preserving the data's integrity from the API source."
      },
      "example": {
        "query": "INSERT INTO shipping_lanes (lane_id, route_geom) VALUES ('LANE-72A', ST_GeomFromGeoJSON('{\"type\":\"LineString\",\"coordinates\":[[12.49,41.89],[12.51,41.90],[12.53,41.88]]}'));",
        "explanation": "This query uses `ST_GeomFromGeoJSON` to natively parse the JSON string and convert it into a geometry. This is highly efficient and less error-prone than manual conversion, as it handles the entire GeoJSON object in the database."
      },
      "notes": [
        "PostGIS must be compiled with GeoJSON support for this function to be available (it is standard in modern versions).",
        "This method is extremely useful when building applications that interact with modern web mapping services and APIs."
      ]
    }
  ]
};