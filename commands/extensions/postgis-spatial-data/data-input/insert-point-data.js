export const command10 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Data Input",
    "Insert Point Data"
  ],
  "description": "Inserts a new record containing a geographic point into a table. This is the fundamental operation for storing location data like addresses, points of interest, or GPS coordinates.",
  "query_template": "INSERT INTO [table] ([columns], [geom_column]) VALUES ([values], ST_GeomFromText('POINT([lon] [lat])', [srid]));",
  "components": [
    {
      "component": "INSERT INTO ... VALUES",
      "name": "SQL Insert Statement",
      "description": "The standard SQL command for adding a new row to a table."
    },
    {
      "component": "ST_GeomFromText('...', srid)",
      "name": "Geometry from Text",
      "description": "The core PostGIS function that converts a Well-Known Text (WKT) string representation of a shape into a native geometry data type."
    },
    {
      "component": "'POINT(-122.4194 37.7749)'",
      "name": "Well-Known Text (WKT)",
      "description": "A standard text format for representing vector geometry. For a point, the format is 'POINT(longitude latitude)'."
    },
    {
      "component": "4326",
      "name": "Spatial Reference ID (SRID)",
      "description": "Specifies the coordinate system for the geometry. 4326 is the standard SRID for WGS 84, used by GPS and most web mapping services."
    }
  ],
  "scenarios": [
    {
      "title": "Storing Geocoded Customer Addresses for a Delivery Service",
      "details": {
        "setup": "You are building an application for a local delivery service. When a new customer signs up, their address is converted into latitude and longitude coordinates by a third-party geocoding API. This data needs to be stored for dispatching and route planning.",
        "goal": "To insert a new customer's record, including their geocoded location, into the `customers` table so it can be used in future spatial queries (e.g., 'find all customers within a 5-mile radius').",
        "outcome": "A new row is added to the `customers` table with the `delivery_location` column correctly populated with a PostGIS point geometry object."
      },
      "example": {
        "query": "INSERT INTO customers (name, address, delivery_location) VALUES ('Jane Doe', '456 Oak Ave, Springfield', ST_GeomFromText('POINT(-89.6501 39.7817)', 4326));",
        "explanation": "This query inserts a new customer record. The `ST_GeomFromText` function takes the longitude and latitude provided by the geocoding service and converts them into a geometry data type that PostGIS can understand and index. The SRID 4326 ensures the data is stored in the standard global coordinate system."
      },
      "notes": [
        "A common and often more efficient alternative is to use `ST_SetSRID(ST_MakePoint(longitude, latitude), 4326)` when you have separate numeric longitude and latitude values.",
        "For this operation to be efficient at scale, the `delivery_location` column should have a GIST spatial index created on it.",
        "Always ensure the order of coordinates in WKT is longitude then latitude."
      ]
    }
  ]
};