export const command23 = {
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Import/Export",
    "Import Shapefile"
  ],
  "command": "shp2pgsql -I -s <SRID> /path/to/shapefile.shp <table_name> | psql -d <database_name>",
  "description": "Imports an ESRI Shapefile into a PostGIS-enabled PostgreSQL database. This command converts the shapefile into SQL statements and pipes them directly to psql for execution.",
  "platformDependent": true,
  "parameters": [
    {
      "flag": "shp2pgsql",
      "name": "shapefile importer",
      "description": "A PostGIS utility that converts shapefiles into SQL insert statements."
    },
    {
      "flag": "-I",
      "name": "create index",
      "description": "Creates a GiST spatial index on the geometry column after the data is imported, which is crucial for query performance."
    },
    {
      "flag": "-s 4326",
      "name": "set SRID",
      "description": "Sets the Spatial Reference Identifier (SRID) for the geometries. 4326 is the standard for WGS 84 GPS coordinates."
    },
    {
      "flag": "-W",
      "name": "encoding",
      "description": "Specifies the character encoding of the shapefile's attribute data (e.g., -W 'UTF-8')."
    },
    {
      "flag": "-D",
      "name": "dump format",
      "description": "Uses the PostgreSQL dump format, which is generally faster for large files compared to plain SQL inserts."
    }
  ],
  "scenarios": [
    {
      "title": "Importing US State Boundaries for Geospatial Analysis",
      "details": {
        "setup": "You are a data analyst for a logistics company. You have downloaded a public-domain Shapefile containing the boundaries of all U.S. states and need to import it into your PostGIS database to analyze shipping routes.",
        "goal": "To create a new table named 'us_states' in the 'analytics_db' database, populate it with the data from the Shapefile, and ensure the geometry column is indexed for efficient spatial queries.",
        "outcome": "A new table 'us_states' exists in the database, containing the state boundary data. Spatial queries against this table are fast and accurate."
      },
      "example": {
        "variants": [
          {
            "platform": "Linux/macOS",
            "command": "shp2pgsql -I -s 4326 /gis_data/us_states/states.shp us_states | psql -U admin -d analytics_db",
            "explanation": "This command converts the 'states.shp' file, creates a spatial index (-I), sets the SRID to WGS 84 (-s 4326), and pipes the resulting SQL to `psql` to execute against the 'analytics_db' database. The table 'us_states' will be created."
          },
          {
            "platform": "Windows",
            "command": "shp2pgsql -I -s 4326 \"C:\\Data\\GIS\\US_States\\states.shp\" us_states | psql -U admin -d analytics_db",
            "explanation": "The functionality is identical to the Linux version, but the file path is specified in the standard Windows format. Quoting the path is a good practice to handle spaces or special characters."
          }
        ]
      },
      "notes": [
        "The `shp2pgsql` utility is typically installed as part of the PostGIS extension, not core PostgreSQL. Ensure it's in your system's PATH.",
        "Always verify the SRID of your source Shapefile. Using the wrong SRID is a common source of errors in spatial analysis.",
        "For very large files, consider generating the SQL to a file first (`shp2pgsql ... > output.sql`) and then running it with `psql -d dbname -f output.sql` to have more control over the import process."
      ]
    }
  ]
};