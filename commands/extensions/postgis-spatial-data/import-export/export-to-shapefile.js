export const command24 = {
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Import/Export",
    "Export to Shapefile"
  ],
  "command": "pgsql2shp -f <output_shapefile_path> <database_name> \"<SELECT_query>\"",
  "description": "Export spatial data from a PostgreSQL/PostGIS database to the ESRI Shapefile format using a command-line utility. This is ideal for sharing data with other GIS applications.",
  "parameters": [
    {
      "flag": "pgsql2shp",
      "name": "shapefile exporter",
      "description": "The PostGIS command-line utility used to export data to a shapefile."
    },
    {
      "flag": "-f",
      "name": "output file",
      "description": "Specifies the path and filename for the output shapefile."
    },
    {
      "flag": "-h",
      "name": "host",
      "description": "The hostname of the database server. Defaults to localhost."
    },
    {
      "flag": "-u",
      "name": "username",
      "description": "The database username to connect with."
    },
    {
      "flag": "-P",
      "name": "password",
      "description": "The password for the database user. Be cautious using this in scripts."
    }
  ],
  "scenarios": [
    {
      "title": "Exporting Retail Store Locations for GIS Analysis",
      "details": {
        "setup": "You are a data analyst for a retail company. Your `analytics_db` database contains a PostGIS-enabled table called `stores`, which includes a geometry column `geom` for each store's location, along with other data like `store_id` and `region`.",
        "goal": "You need to provide the locations of all stores in the 'North' region to a marketing team that uses dedicated GIS software (like QGIS or ArcGIS) for spatial analysis and planning.",
        "outcome": "A new shapefile named `northern_stores.shp` and its associated files (.shx, .dbf, .prj) are created, containing only the stores from the 'North' region, ready to be imported into the GIS software."
      },
      "example": {
        "command": "pgsql2shp -f /data/exports/northern_stores.shp -u analyst analytics_db \"SELECT store_id, region, geom FROM stores WHERE region = 'North'\"",
        "explanation": "This command connects to the `analytics_db` as the `analyst` user. It executes a SQL query to select only the stores where the region is 'North'. The resulting geometry and attribute data are then written to the `northern_stores.shp` file in the specified directory."
      },
      "notes": [
        "The `pgsql2shp` utility must be run from your system's command line (like Bash or PowerShell), not from within a `psql` session.",
        "ESRI Shapefiles have a column name length limit of 10 characters. `pgsql2shp` will automatically truncate longer column names, which could lead to confusion.",
        "Ensure the user (`analyst` in this case) has the required `SELECT` permissions on the `stores` table."
      ]
    }
  ]
};