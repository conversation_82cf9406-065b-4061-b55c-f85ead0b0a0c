export const command25 = {
  // --- Differentiator for your app logic ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Import/Export",
    "Import GeoJSON"
  ],
  "description": "Parses a GeoJSON object and inserts its features as individual rows into a PostGIS table, mapping GeoJSON properties to table columns and converting the geometry.",

  // --- Query-specific fields ---
  "query_template": "INSERT INTO [target_table] ([prop_col], [geom_col]) SELECT properties->>'[prop_name]', ST_SetSRID(ST_GeomFromGeoJSON(geometry), [srid]) FROM json_array_elements([geojson_object]->'features') AS features;",
  "components": [
    {
      "component": "json_array_elements(...)",
      "name": "JSON Array Expansion",
      "description": "A crucial function that takes a JSON array (in this case, the 'features' array from the GeoJSON) and expands it into a set of rows, allowing you to process each feature individually."
    },
    {
      "component": "->'features'",
      "name": "JSON Navigation",
      "description": "JSON operators used to navigate the object hierarchy. `->>` extracts a field as text, while `->` extracts it as a JSON object."
    },
    {
      "component": "ST_GeomFromGeoJSON()",
      "name": "GeoJSON to PostGIS Geometry",
      "description": "The core PostGIS function that parses a GeoJSON geometry object and converts it into the native PostGIS geometry type."
    },
    {
      "component": "ST_SetSRID()",
      "name": "Set Spatial Reference ID",
      "description": "Assigns a spatial reference system (like WGS 84, which is SRID 4326) to the newly created geometry, which is essential for spatial indexing and analysis."
    }
  ],

  // --- Scenario-based example ---
  "scenarios": [
    {
      "title": "Bulk Importing Country Border Data from a GeoJSON File",
      "details": {
        "setup": "You have downloaded a `countries.geojson` file containing the borders and administrative names of all countries. You need to load this data into a `countries` table in your PostGIS-enabled database to power a map visualization application.",
        "goal": "To efficiently parse the entire GeoJSON file and insert each country's name and its border (as a MultiPolygon) into the `countries` table, ensuring the spatial data is correctly projected.",
        "outcome": "The `countries` table is fully populated. Each row contains a country's name and a valid, spatially-referenced geometry of its border, ready for mapping and spatial queries."
      },
      "example": {
        "query": "-- Pre-requisite: A table to hold the raw GeoJSON content\nCREATE TEMP TABLE geojson_import(content TEXT);\n\n-- Use psql's \\copy or other means to load your file into this table\n-- \\copy geojson_import FROM '~/data/countries.geojson';\n\n-- The actual import query\nINSERT INTO countries (name, border_geom)\nSELECT\n  feature->'properties'->>'ADMIN' AS country_name,\n  ST_SetSRID(ST_GeomFromGeoJSON(feature->'geometry'), 4326)\nFROM (\n  SELECT json_array_elements((SELECT content FROM geojson_import)::json->'features') AS feature\n) AS features;",
        "explanation": "This query first unnests the 'features' array from the raw GeoJSON text using `json_array_elements`. For each resulting feature, it extracts the country name from the 'properties' object using the `->>` operator. Simultaneously, it extracts the 'geometry' object and converts it to a PostGIS geometry using `ST_GeomFromGeoJSON`, immediately setting its SRID to 4326 (the standard for GPS coordinates)."
      },
      "notes": [
        "For very large GeoJSON files, loading the data into a temporary table first is more robust than trying to pass it as a single string.",
        "The property name ('ADMIN' in this example) must exactly match the key in the GeoJSON `properties` object.",
        "Ensure your target table's geometry column (e.g., `border_geom`) is created with the same SRID you are setting in the query (4326)."
      ]
    }
  ]
};