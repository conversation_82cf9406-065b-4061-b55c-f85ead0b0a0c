export const command6 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Installation",
    "Check PostGIS Version"
  ],
  "description": "Retrieves the version of the installed PostGIS extension and its dependent libraries like GEOS and PROJ, which is crucial for compatibility checks and debugging.",
  "query_template": "SELECT PostGIS_..._Version();",
  "components": [
    {
      "component": "PostGIS_Version()",
      "name": "PostGIS Version Function",
      "description": "Returns the core version number of the PostGIS extension."
    },
    {
      "component": "PostGIS_Full_Version()",
      "name": "PostGIS Full Version Function",
      "description": "Provides a detailed string including compilation options, library versions, and other build-time information."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying a New Server's Geospatial Capabilities",
      "details": {
        "setup": "You are a developer preparing to deploy a new mapping application to a production server. Your application requires PostGIS 3.2+ and specific features for coordinate projection that depend on a modern PROJ library.",
        "goal": "To perform a comprehensive check of the PostGIS installation on the server to ensure it meets all application requirements before deployment.",
        "outcome": "A detailed report of the PostGIS, GEOS, and PROJ library versions is generated, confirming the server is correctly configured for the application."
      },
      "example": {
        "query": "SELECT PostGIS_Version() AS postgis, PostGIS_Full_Version() AS details, PostGIS_GEOS_Version() AS geos, PostGIS_Proj_Version() AS proj;",
        "explanation": "This single query provides a complete diagnostic report. `PostGIS_Version()` confirms the main version. `PostGIS_Full_Version()` is vital for checking if important libraries were included at compile time. The GEOS and PROJ versions are checked to ensure all underlying libraries are up to date and compatible."
      },
      "notes": [
        "Running this check is a critical first step when debugging unexpected spatial query behavior.",
        "Keep this information available when reporting potential bugs to the PostGIS community.",
        "This query can be executed by any user with connection privileges to the database where the PostGIS extension is installed."
      ]
    }
  ]
};