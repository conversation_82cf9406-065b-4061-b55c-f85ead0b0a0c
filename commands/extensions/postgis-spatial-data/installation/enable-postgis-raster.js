export const command5 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Installation",
    "Enable PostGIS Raster"
  ],
  "description": "Enables the PostGIS raster extension in the current database, allowing it to store and analyze raster data such as satellite imagery, elevation models, and other gridded datasets.",
  "query_template": "CREATE EXTENSION IF NOT EXISTS postgis_raster;",
  "components": [
    {
      "component": "CREATE EXTENSION postgis_raster",
      "name": "Enable Extension",
      "description": "The core SQL command that installs the specified extension's functions, types, and operators into the current database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Safety Clause",
      "description": "An optional but highly recommended clause that prevents the command from failing if the extension is already installed."
    }
  ],
  "scenarios": [
    {
      "title": "Preparing a Database for Digital Elevation Model (DEM) Analysis",
      "details": {
        "setup": "An environmental consulting firm needs to analyze a watershed area. They have high-resolution Digital Elevation Model data in GeoTIFF format and want to use PostgreSQL to perform slope and aspect calculations.",
        "goal": "To prepare the `enviro_gis` database to handle raster data by enabling the necessary PostGIS raster functions and data types.",
        "outcome": "The `postgis_raster` extension is successfully installed. The database now supports the `raster` data type and hundreds of `ST_*` raster functions, making it ready for the DEM data to be imported."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS postgis_raster;",
        "explanation": "This command must be run once per database before any raster data can be imported or processed. It integrates the raster capabilities directly into the database's SQL engine. The `IF NOT EXISTS` clause makes deployment scripts safe to re-run without causing errors."
      },
      "notes": [
        "This extension depends on the core `postgis` extension. Ensure you have already run `CREATE EXTENSION postgis;` first.",
        "After enabling the extension, you can use the `raster2pgsql` command-line tool (which comes with PostGIS) to load raster files like GeoTIFFs into your database tables."
      ]
    }
  ]
};