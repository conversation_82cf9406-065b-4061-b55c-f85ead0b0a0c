export const command3 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Installation",
    "Enable PostGIS Core"
  ],
  "description": "Installs the core PostGIS extension into the current database, providing essential spatial data types (e.g., GEOMETRY, GEOGRAPHY), functions, and operators.",
  "query_template": "CREATE EXTENSION IF NOT EXISTS postgis;",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "SQL Command",
      "description": "The standard PostgreSQL command to install a registered extension into a database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Clause",
      "description": "An optional but recommended clause that prevents the command from failing if the extension is already installed, making scripts safer to re-run."
    },
    {
      "component": "postgis",
      "name": "Extension Name",
      "description": "The name of the core PostGIS extension that provides the foundational spatial capabilities."
    }
  ],
  "scenarios": [
    {
      "title": "Setting Up a New Database for a Location-Based Service",
      "details": {
        "setup": "You are a database administrator tasked with creating a new database named `geospatial_app_db` for an application that will track delivery vehicles and store customer addresses.",
        "goal": "To prepare the new database to handle spatial data by enabling the required PostGIS functionalities.",
        "outcome": "The `postgis` extension is successfully installed in `geospatial_app_db`. You can now create tables with `GEOMETRY` columns and use PostGIS functions like `ST_Point` and `ST_Distance`."
      },
      "example": {
        "query": "-- First, connect to your new database:\n\\c geospatial_app_db\n\n-- Then, enable the PostGIS extension:\nCREATE EXTENSION IF NOT EXISTS postgis;",
        "explanation": "This is the foundational step for any PostGIS-enabled database. The `CREATE EXTENSION` command loads the PostGIS types, functions, and operators into the backend. Executing this makes the database 'spatially aware'."
      },
      "notes": [
        "This command must be run individually for each database that requires PostGIS capabilities.",
        "You must have the necessary permissions (typically superuser or database owner) to create extensions.",
        "This assumes the PostGIS software has already been installed on the server machine itself. This SQL command just activates it within a specific database.",
        "For more advanced features, you may also need to install other related extensions like `postgis_raster` or `postgis_topology`."
      ]
    }
  ]
};