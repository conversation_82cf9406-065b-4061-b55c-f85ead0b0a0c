export const command4 = {
  "type": "query", // Identified as a query for database execution
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Installation",
    "Enable PostGIS Topology"
  ],
  "description": "Installs the PostGIS Topology extension, which provides a topological data model and functions for advanced spatial data validation and analysis, particularly useful for managing shared boundaries without gaps or overlaps.",
  "query_template": "CREATE EXTENSION IF NOT EXISTS postgis_topology;",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "Create Extension Command",
      "description": "A SQL command used to load a new extension into the current database, making its functions and types available."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Conditional Clause",
      "description": "Prevents an error if the extension is already installed, making the command idempotent."
    },
    {
      "component": "postgis_topology",
      "name": "Extension Name",
      "description": "The specific name of the PostGIS extension that provides topological data management capabilities, including the `TopoGeometry` type and functions like `CreateTopology()`."
    }
  ],
  "scenarios": [
    {
      "title": "Establishing Robust Data Integrity for Land Parcel Management",
      "details": {
        "setup": "You are developing a Geographic Information System (GIS) for a municipal government to manage land parcels. It's crucial that parcel boundaries are perfectly aligned without any gaps or overlaps, which is difficult to guarantee with standard geometric data types alone.",
        "goal": "To enable the advanced topological capabilities within PostGIS to ensure strict spatial data integrity for shared boundaries (e.g., adjacent properties, zoning areas) and prepare for complex topological operations.",
        "outcome": "The `postgis_topology` extension is successfully installed in your database, providing the necessary functions and data types (like `TopoGeometry`) to model and validate shared spatial features."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS postgis_topology;",
        "explanation": "This command installs the `postgis_topology` extension. Once installed, you can define a 'topology' for a given geographic area (e.g., `SELECT topology.CreateTopology('municipal_parcels', 4326, 0.5);`) and then store geometries as `TopoGeometry` types, which inherently enforce rules about shared edges and nodes, preventing common data errors."
      },
      "notes": [
        "Installing the `postgis_topology` extension creates a new `topology` schema in your database.",
        "After installation, you typically create one or more specific topology objects using `topology.CreateTopology()` for your data, specifying a spatial reference system (SRID) and a precision tolerance.",
        "Using topology adds a layer of complexity but offers significant advantages for data quality, especially in applications where spatial relationships and validation are paramount."
      ]
    }
  ]
};