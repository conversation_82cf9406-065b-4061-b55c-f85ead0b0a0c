export const command18 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Advanced Spatial Operations",
    "Centroid and Envelope"
  ],
  "description": "Calculates the geometric center (centroid) and the minimum bounding box (envelope) for a spatial geometry. This is commonly used to find a representative point for labeling or to define a zoom extent on a map.",
  "query_template": "SELECT [name_column], ST_Centroid([geom_column]), ST_Envelope([geom_column]) FROM [table_name] WHERE [condition];",
  "components": [
    {
      "component": "ST_Centroid(geometry)",
      "name": "Centroid Function",
      "description": "A PostGIS function that computes the geometric center of a geometry, which can be a point, line, or polygon."
    },
    {
      "component": "ST_Envelope(geometry)",
      "name": "Envelope Function",
      "description": "A PostGIS function that returns the minimum bounding rectangle (a polygon) that completely encloses a given geometry."
    }
  ],
  "scenarios": [
    {
      "title": "Generating Map Labels and Default Zoom Views for a GIS Application",
      "details": {
        "setup": "You are building a web-based mapping application that displays administrative regions (e.g., countries, states). The data is stored in a `regions` table with a `geom` column containing polygon data.",
        "goal": "For a user-selected region, you need to: 1) Find a central point to place a map marker or label. 2) Determine the rectangular area (bounding box) to set the map's initial zoom level to fit the entire region.",
        "outcome": "The query returns a single point for the label location and a rectangular polygon defining the bounding box, which can be passed to a front-end mapping library like Leaflet or OpenLayers."
      },
      "example": {
        "query": "SELECT name, ST_AsText(ST_Centroid(geom)) as center_point, ST_AsText(ST_Envelope(geom)) as bounding_box FROM regions WHERE name = 'France';",
        "explanation": "This query targets the 'France' polygon in the `regions` table. `ST_Centroid` calculates the mathematical center for placing a label. `ST_Envelope` creates the smallest rectangle covering France, which is perfect for setting the `map.fitBounds()` function in a mapping library. `ST_AsText` is used here to get a human-readable output."
      },
      "notes": [
        "Crucial: For irregularly shaped polygons (e.g., a crescent), the `ST_Centroid` may fall outside the polygon itself. For placing labels, it is often safer to use `ST_PointOnSurface(geom)`, which is guaranteed to return a point that is inside the polygon.",
        "The envelope is an approximation of the shape's extent and is much faster to calculate and render than the full, complex geometry."
      ]
    }
  ]
};