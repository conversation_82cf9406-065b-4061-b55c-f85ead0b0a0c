export const command17 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Advanced Spatial Operations",
    "Buffer Operations"
  ],
  "description": "Creates a buffer zone (a polygon) at a specified distance around a spatial object. This is essential for proximity analysis, such as finding all features within a certain radius of a point or a line.",
  "query_template": "SELECT [columns], ST_Buffer([geom_column]::geography, [distance_meters])::geometry AS buffer_geom FROM [table_name] WHERE [condition];",
  "components": [
    {
      "component": "<PERSON>_Buffer(geography, distance)",
      "name": "Buffer Function",
      "description": "The core PostGIS function that generates a polygon representing all points within a given distance from a source geometry."
    },
    {
      "component": "::geography",
      "name": "Cast to Geography",
      "description": "Crucial for accurate, real-world distance calculations. This cast allows you to specify the buffer distance in meters, even if your coordinates are in latitude/longitude (like SRID 4326)."
    },
    {
      "component": "[distance_meters]",
      "name": "<PERSON>uffer Distance",
      "description": "The radius of the buffer, specified in meters, because the geometry was cast to the geography type."
    },
    {
      "component": "::geometry",
      "name": "Cast back to Geometry",
      "description": "Converts the result back to the standard geometry type for compatibility with other PostGIS functions or visualization tools."
    }
  ],
  "scenarios": [
    {
      "title": "Identifying Properties Within a Proposed School District Zone",
      "details": {
        "setup": "An urban planning department needs to identify all residential properties that fall within 1.5 kilometers of a proposed new school. The database contains a `properties` table with a `geom` column for each property's location and a `schools` table with the `location` of the proposed school.",
        "goal": "To generate a precise geographical area (a polygon) representing the 1.5km radius around the new school. This polygon will then be used to create a list of affected properties.",
        "outcome": "The query returns the school's name along with a polygon geometry representing the 1.5km buffer zone. This polygon can be immediately visualized on a map or used in a subsequent query to select the properties."
      },
      "example": {
        "query": "SELECT name, ST_Buffer(location::geography, 1500)::geometry AS school_zone FROM schools WHERE school_id = 'S-NEW-01';",
        "explanation": "This query creates a 1500-meter buffer around the school with ID 'S-NEW-01'. The `location` column is cast to `geography` to ensure the distance is calculated accurately in meters on the Earth's surface. The resulting buffer polygon is named `school_zone`."
      },
      "notes": [
        "This is often the first step in a two-part query. The next step is to use this buffer to find intersecting geometries, e.g., `...WHERE ST_Intersects(properties.geom, school_zone.geom)`.",
        "The geography type is more computationally intensive than geometry. For large datasets, this pattern is a great balance: perform the accurate but expensive buffer operation on a single feature (the school), then use the resulting geometry (which is faster) to query against an indexed table of many features (the properties)."
      ]
    }
  ]
};