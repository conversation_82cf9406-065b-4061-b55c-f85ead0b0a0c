export const command16 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Advanced Spatial Operations",
    "Spatial Joins"
  ],
  "description": "Performs a spatial join to correlate records from two tables based on their geographic relationship, such as finding which polygon area contains a specific point.",
  "query_template": "SELECT a.*, b.* FROM [table_a] a JOIN [table_b] b ON ST_Within(a.[geom_point], b.[geom_polygon]);",
  "components": [
    {
      "component": "JOIN ... ON [spatial_relationship]",
      "name": "Spatial Join",
      "description": "The core of the query. Instead of joining on a traditional key (like id = foreign_id), the join condition is a PostGIS function that evaluates the spatial relationship between geometries."
    },
    {
      "component": "ST_Within(geometry A, geometry B)",
      "name": "Spatial Relationship Function",
      "description": "A boolean function that returns true if geometry A is completely inside geometry B. This is the predicate that links the two tables."
    }
  ],
  "scenarios": [
    {
      "title": "Assigning Sales Territories to Customer Locations",
      "details": {
        "setup": "You have a `customers` table with a `location` point column and a `sales_territories` table with a `boundary` polygon column. You need to assign a territory to each customer for reporting and logistics.",
        "goal": "To produce a list of all customers and the name of the specific sales territory they are located in.",
        "outcome": "A result set where each row contains a customer's details alongside the name and ID of the sales territory that geographically contains them."
      },
      "example": {
        "query": "SELECT c.customer_name, t.territory_name FROM customers c JOIN sales_territories t ON ST_Within(c.location, t.boundary);",
        "explanation": "This query joins the `customers` table with `sales_territories`. The `ST_Within` function serves as the join key, evaluating to true for each customer whose `location` point is inside a territory's `boundary` polygon. This effectively enriches the customer data with its corresponding territory information."
      },
      "notes": [
        "For this query to be performant on large tables, you MUST have a GIST spatial index on both the `customers(location)` and `sales_territories(boundary)` columns.",
        "The inverse function, `ST_Contains`, can also be used. `ST_Within(A, B)` is logically equivalent to `ST_Contains(B, A)`. The query planner typically handles them identically."
      ]
    },
    {
      "title": "Aggregating Sensor Readings by District",
      "details": {
        "setup": "A table `iot_sensors` is constantly recording environmental readings (e.g., temperature, air quality) at various geographic points (`geom`). You also have a `city_districts` table with polygon geometries.",
        "goal": "To calculate the average temperature for each city district by aggregating the readings from all sensors located within that district.",
        "outcome": "A summary report listing each district's name and its calculated average temperature."
      },
      "example": {
        "query": "SELECT d.district_name, AVG(s.temperature) as avg_temp FROM iot_sensors s JOIN city_districts d ON ST_Within(s.geom, d.geom) GROUP BY d.district_name;",
        "explanation": "First, the spatial join identifies which district each sensor belongs to. Then, the standard SQL `GROUP BY` and `AVG()` aggregate functions are used on this joined data to compute the average temperature for each distinct district name."
      },
      "notes": [
        "This demonstrates how spatial joins can be powerfully combined with standard SQL aggregate functions to create sophisticated geospatial reports."
      ]
    }
  ]
};