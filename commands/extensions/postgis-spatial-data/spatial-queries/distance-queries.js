export const command13 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Spatial Queries",
    "Distance Queries"
  ],
  "description": "A highly efficient query pattern to find the nearest points of interest to a specific location. It uses an index-friendly function (`ST_DWithin`) to quickly select a candidate set before calculating the precise distance with `ST_Distance`.",
  "query_template": "SELECT [columns], ST_Distance([geometry_column], [point_geometry]) AS distance FROM [table_name] WHERE ST_DWithin([geometry_column], [point_geometry], [search_radius]) ORDER BY distance;",
  "components": [
    {
      "component": "ST_DWithin([geom_col], [point], [radius])",
      "name": "Index-Optimized Filter",
      "description": "This is the most critical part for performance. It uses a spatial index to rapidly find all geometries within a given radius, creating a smaller subset of data to process."
    },
    {
      "component": "ST_Distance([geom_col], [point])",
      "name": "Precise Distance Calculation",
      "description": "Calculates the exact distance between two geometries. This function is run only on the rows that pass the `ST_DWithin` filter."
    },
    {
      "component": "ORDER BY distance",
      "name": "Sort by Nearest",
      "description": "Orders the final result set to show the closest locations first."
    }
  ],
  "scenarios": [
    {
      "title": "Powering a 'Find Nearest Store' Feature for a Retail Website",
      "details": {
        "setup": "You are building an API for a retail company's website. The database has a `stores` table with a `geom` column (using the `geography` type for accuracy) that stores the location of every store. A GIST index has been created on this column.",
        "goal": "To create a fast API endpoint that accepts a user's latitude and longitude and returns the 10 closest stores, ordered from nearest to farthest.",
        "outcome": "The query efficiently returns the 10 nearest stores, providing the data needed for the website's store locator feature."
      },
      "example": {
        "query": "SELECT \n  store_name, \n  address, \n  ST_Distance(geom, 'SRID=4326;POINT(-95.3698 29.7604)') AS distance_in_meters \nFROM \n  stores \nWHERE \n  ST_DWithin(geom, 'SRID=4326;POINT(-95.3698 29.7604)', 100000) -- 100km radius \nORDER BY \n  distance_in_meters \nLIMIT 10;",
        "explanation": "This query finds the 10 stores closest to a point in Houston. It first uses `ST_DWithin` to instantly narrow the search to stores within a 100km radius (a task the spatial index excels at). Then, it calculates the precise distance and sorts only that small subset, ensuring the query remains fast even with millions of store locations."
      },
      "notes": [
        "This `ST_DWithin` -> `ST_Distance` pattern is fundamental for all 'K-Nearest Neighbor' (KNN) searches in PostGIS.",
        "For longitude/latitude data, using the `geography` data type instead of `geometry` is often recommended. It calculates distances in meters on the Earth's surface, automatically accounting for curvature.",
        "Always include a `LIMIT` clause in these types of queries to ensure your application receives a predictable number of results and to further improve performance."
      ]
    }
  ]
};