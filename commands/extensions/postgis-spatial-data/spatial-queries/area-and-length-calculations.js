export const command15 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Spatial Queries",
    "Area and Length Calculations"
  ],
  "description": "Calculates the real-world area and perimeter of polygonal spatial data, converting the results into standard metric units (square kilometers and kilometers).",
  "query_template": "SELECT [name_col], ST_Area([geom_col]::geography)/1000000 AS area_sqkm, ST_Perimeter([geom_col]::geography)/1000 AS perim_km FROM [table_name] ORDER BY area_sqkm DESC;",
  "components": [
    {
      "component": "ST_Area(geom::geography)",
      "name": "Area Calculation",
      "description": "A PostGIS function that calculates the area of a polygon. Casting the geometry to the `geography` type ensures the calculation is performed on a sphere, yielding a highly accurate result in square meters."
    },
    {
      "component": "ST_Perimeter(geom::geography)",
      "name": "Perimeter Calculation",
      "description": "Calculates the length of the boundary of a polygon. The `geography` cast is essential for accurate measurements over large distances, returning the result in meters."
    },
    {
      "component": "ST_Length(line::geography)",
      "name": "Length Calculation (for lines)",
      "description": "Similar to ST_Perimeter, but used to calculate the length of linestring geometries (e.g., rivers, roads)."
    }
  ],
  "scenarios": [
    {
      "title": "Generating a Report on National Park Sizes for Environmental Analysis",
      "details": {
        "setup": "You are a GIS analyst with a `national_parks` table. The table contains a `geom` column (in SRID 4326) storing the boundary polygon for each park. You need to rank these parks by size for a conservation report.",
        "goal": "To produce a sorted list of all national parks, showing their names, calculated area in square kilometers, and boundary length in kilometers, for easy comparison.",
        "outcome": "The query returns a table with each park's name, its accurately calculated area, and its perimeter, sorted from the largest park to the smallest."
      },
      "example": {
        "query": "SELECT park_name, ST_Area(geom::geography)/1000000 AS area_km2, ST_Perimeter(geom::geography)/1000 AS perimeter_km FROM national_parks ORDER BY area_km2 DESC;",
        "explanation": "This query uses `ST_Area` and `ST_Perimeter` with a `::geography` cast to get accurate, meter-based results. It then divides the area by 1,000,000 to convert from square meters to square kilometers and the perimeter by 1,000 to convert to kilometers. The results are aliased for clarity and sorted in descending order."
      },
      "notes": [
        "Casting to `geography` is crucial for accurate area/length calculations when using latitude/longitude data (like SRID 4326). Without it, calculations would be based on planar degrees, which is meaningless.",
        "The `geography` type calculations are more computationally intensive than `geometry` calculations. Use them when accuracy over large distances is required.",
        "Make sure your source geometry is valid using `ST_IsValid()` before performing calculations, as invalid geometries can produce errors or incorrect results."
      ]
    }
  ]
};