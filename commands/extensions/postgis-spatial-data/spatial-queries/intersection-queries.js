export const command14 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Spatial Queries",
    "Find Intersecting Geometries"
  ],
  "description": "Finds all geometries from one layer that spatially intersect with geometries from another layer. This is a foundational query for spatial analysis.",
  "query_template": "SELECT a.[column], b.[column]\nFROM [table_a] a\nINNER JOIN [table_b] b ON ST_Intersects(a.[geom_column], b.[geom_column]);",
  "components": [
    {
      "component": "ST_Intersects(geom_a, geom_b)",
      "name": "ST_Intersects Function",
      "description": "The core PostGIS boolean function that returns `true` if two geometries share any portion of space (they touch, cross, or overlap)."
    },
    {
      "component": "INNER JOIN ... ON",
      "name": "Spatial Join",
      "description": "A standard SQL join where the join condition is a spatial relationship instead of a simple equality."
    }
  ],
  "scenarios": [
    {
      "title": "Urban Planning: Identify Properties Affected by a New Flood Zone",
      "details": {
        "setup": "A city planning department has a table of all property parcels (`property_parcels`) and a new, updated map of a 100-year flood plain (`flood_zones`). Both tables have indexed geometry columns.",
        "goal": "To generate a complete list of all property parcels that are even partially within the new flood zone to notify the owners.",
        "outcome": "A table of property IDs and their addresses that intersect with the flood zone geometry."
      },
      "example": {
        "query": "SELECT\n  p.parcel_id,\n  p.address\nFROM\n  property_parcels p\nINNER JOIN\n  flood_zones f ON ST_Intersects(p.geom, f.geom)\nWHERE\n  f.zone_name = '100-Year Flood Plain 2025';",
        "explanation": "This query efficiently joins the two tables on their spatial relationship. `ST_Intersects` is the correct function here because a property is considered affected even if it only slightly touches the flood zone boundary."
      },
      "notes": [
        "For this query to be performant on large tables, both `property_parcels.geom` and `flood_zones.geom` must have a GIST spatial index.",
        "Use `ST_Contains(zone.geom, property.geom)` if you only want properties that are *completely within* the zone.",
        "Use `ST_Within(property.geom, zone.geom)` to achieve the same result as ST_Contains but with the arguments reversed.",
        "Use `ST_Overlaps` if you need to find geometries that intersect but neither one completely contains the other."
      ]
    }
  ]
};