export const command19 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Coordinate System Operations",
    "Transform Coordinates"
  ],
  "description": "Transforms spatial data from one coordinate system (Spatial Reference System) to another. This is a fundamental operation for integrating datasets or preparing data for visualization.",
  "query_template": "SELECT [columns], ST_Transform([geometry_column], [target_srid]) FROM [table];",
  "components": [
    {
      "component": "ST_Transform(geometry, srid)",
      "name": "Transform Function",
      "description": "The core PostGIS function that takes a geometry and an integer SRID (Spatial Reference System Identifier) and returns a new geometry in the target coordinate system."
    },
    {
      "component": "[target_srid]",
      "name": "Target SRID",
      "description": "The integer ID of the coordinate system you want to convert to. For example, 3857 for Web Mercator or 2154 for France's Lambert-93."
    }
  ],
  "scenarios": [
    {
      "title": "Preparing Geographic Data for a Web Map",
      "details": {
        "setup": "Your application stores the locations of retail stores in a `stores` table using standard GPS coordinates (latitude/longitude), which corresponds to the WGS 84 coordinate system (SRID 4326). Your web front-end uses a mapping library like OpenLayers or Leaflet that displays data using the Web Mercator projection (SRID 3857).",
        "goal": "To query the store locations from the database and convert their coordinates on-the-fly into the Web Mercator projection so they can be accurately displayed on the map.",
        "outcome": "The query successfully returns the store name and its transformed geometry. The front-end application can now plot these coordinates correctly on the web map without distortion."
      },
      "example": {
        "query": "SELECT name, address, ST_AsGeoJSON(ST_Transform(location_geom, 3857)) as web_mercator_geom FROM stores WHERE country = 'USA';",
        "explanation": "This query retrieves all stores in the 'USA'. The `ST_Transform` function converts the `location_geom` from its source SRID (implicitly 4326) to the target SRID 3857. We also wrap it in `ST_AsGeoJSON` to format the output directly into GeoJSON, a common format for web mapping applications."
      },
      "notes": [
        "For `ST_Transform` to work, PostGIS must have the definitions for both the source and target SRIDs in the `spatial_ref_sys` table.",
        "Ensure your geometry column has its SRID correctly set in the first place. You can check with `SELECT ST_SRID(location_geom) FROM stores LIMIT 1;`. If it's 0 or -1, you need to set it using `ST_SetSRID`."
      ]
    }
  ]
};