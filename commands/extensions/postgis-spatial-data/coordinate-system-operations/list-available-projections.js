export const command20 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "PostGIS (Spatial Data)",
    "Coordinate System Operations",
    "List Available Projections"
  ],
  "description": "Lists the definitions of specific Spatial Reference Systems (SRS) available in the PostGIS database, which is essential for managing and transforming spatial data from different sources.",
  "query_template": "SELECT auth_name, auth_srid, srtext FROM spatial_ref_sys WHERE auth_srid IN ([srid_list]);",
  "components": [
    {
      "component": "spatial_ref_sys",
      "name": "SRS Table",
      "description": "The PostGIS system catalog table that contains definitions for all available coordinate systems."
    },
    {
      "component": "auth_srid",
      "name": "Authority SRID",
      "description": "The unique Spatial Reference System Identifier number, typically an EPSG code (e.g., 4326 for WGS 84)."
    },
    {
      "component": "srtext",
      "name": "Well-Known Text",
      "description": "The full text definition of the coordinate system's parameters (projection, ellipsoid, datum, etc.)."
    },
    {
      "component": "auth_name",
      "name": "Authority Name",
      "description": "The organization that defines the coordinate system, most commonly 'EPSG'."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Coordinate Systems Before Data Integration",
      "details": {
        "setup": "You have received two spatial datasets to import into your database. The first dataset's documentation says it uses 'WGS 84 longitude/latitude', and the second uses 'Web Mercator', which is common in web mapping applications.",
        "goal": "To find the official SRID (Spatial Reference System Identifier) for each of these coordinate systems within your PostGIS database before you import the data.",
        "outcome": "The query returns the details for the common SRIDs, confirming that WGS 84 corresponds to SRID 4326 and Web Mercator corresponds to SRID 3857. You can now confidently use these SRIDs during data import with `ST_SetSRID`."
      },
      "example": {
        "query": "SELECT auth_name, auth_srid, srtext FROM spatial_ref_sys WHERE auth_srid IN (4326, 3857) ORDER BY auth_srid;",
        "explanation": "This query directly checks the `spatial_ref_sys` table for the definitions of SRIDs 4326 and 3857. It's a precise way to confirm that your database supports the required projections and to see their detailed parameters."
      },
      "notes": [
        "If you don't know the SRID, you can search the `srtext` column. For example, use `WHERE srtext ILIKE '%France%'` to find projections relevant to France.",
        "SRID 4326 (WGS 84) is the most common standard for raw GPS data.",
        "SRID 3857 (Web Mercator) is used by nearly all major web mapping providers like Google Maps and OpenStreetMap."
      ]
    }
  ]
};