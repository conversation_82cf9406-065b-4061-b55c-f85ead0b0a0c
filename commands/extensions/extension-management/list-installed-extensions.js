export const command1 = {
  "path": [
    "Extensions and Advanced Features",
    "Extension Management",
    "List Installed Extensions"
  ],
  "command": "\\dx",
  "description": "List all currently installed extensions in the database. Use \\dx+ for detailed information.",
  "parameters": [
    {
      "flag": "\\dx",
      "name": "list extensions",
      "description": "Basic extension listing"
    },
    {
      "flag": "\\dx+",
      "name": "detailed list",
      "description": "Extended listing with version and schema information"
    },
    {
      "flag": "\\dx extension_name",
      "name": "specific extension",
      "description": "Show details for a specific extension"
    }
  ],
  "scenarios": [
    {
      "title": "Verifying PostGIS Installation Before Geospatial Data Import",
      "details": {
        "setup": "You are a developer preparing to deploy a new mapping feature for your application. This feature requires the PostGIS extension (version 3.0 or higher) to be active in the production database to handle spatial data types and functions.",
        "goal": "To confirm that the PostGIS extension is installed and verify its version number before running the data import scripts.",
        "outcome": "The command output shows that 'postgis' is installed, along with its version and the schema it uses, confirming the environment is ready for deployment."
      },
      "example": {
        "command": "\\dx+ postgis",
        "explanation": "This command specifically queries for the 'postgis' extension. Using the `+` modifier provides crucial details like the installed version, which is necessary to check for compatibility with your application's requirements."
      },
      "notes": [
        "This command must be run from within the `psql` interactive terminal.",
        "If an extension is not listed, it can be installed with the `CREATE EXTENSION` command, provided its binaries are on the server.",
        "This check is a critical first step in troubleshooting errors like 'type \"geometry\" does not exist'."
      ]
    }
  ]
};