export const command2 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Extension Management",
    "Install Extension"
  ],
  "description": "Installs an extension into the current database, adding new data types, functions, or capabilities. Using 'IF NOT EXISTS' is a best practice to prevent errors in automated scripts.",
  "query_template": "CREATE EXTENSION IF NOT EXISTS [extension_name] WITH SCHEMA [schema_name] VERSION [version_string];",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "Base Command",
      "description": "The fundamental SQL command to initiate the installation of an extension."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Conditional Clause",
      "description": "An optional clause that prevents the command from failing if the extension is already installed. Highly recommended for scripts."
    },
    {
      "component": "WITH SCHEMA",
      "name": "Schema Clause",
      "description": "An optional clause to install the extension's objects into a specific schema, helping to organize your database."
    },
    {
      "component": "VERSION",
      "name": "Version Clause",
      "description": "An optional clause to request a specific version of the extension."
    }
  ],
  "scenarios": [
    {
      "title": "Enabling Geospatial Capabilities with PostGIS",
      "details": {
        "setup": "You are building a mapping application and your database, 'map_app_db', needs to store and query geographic locations (e.g., longitude/latitude coordinates, polygons).",
        "goal": "To add spatial data types (like `geometry` and `geography`) and a comprehensive library of spatial functions to the database.",
        "outcome": "The PostGIS extension is installed successfully, allowing you to create tables with geometry columns and use functions like `ST_DWithin` or `ST_Intersects`."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS postgis;",
        "explanation": "This command installs the PostGIS extension. `IF NOT EXISTS` ensures that running this setup script multiple times won't cause an error. After this, you can immediately start defining tables with spatial columns."
      },
      "notes": [
        "Extensions are installed per-database. If you have another database that needs PostGIS, you must run this command in that database as well.",
        "Typically, you must be a database superuser to install new extensions."
      ]
    },
    {
      "title": "Adding UUID Support for Primary Keys",
      "details": {
        "setup": "Your application design requires using UUIDs (Universally Unique Identifiers) as primary keys for new tables to ensure globally unique IDs, but the functions to generate them aren't available by default.",
        "goal": "To install the `uuid-ossp` extension, which provides the `uuid_generate_v4()` function needed to create unique identifiers.",
        "outcome": "The extension is installed, and you can now define a table with a primary key that automatically generates a version 4 UUID for each new row."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";",
        "explanation": "This query installs the 'uuid-ossp' extension. The double quotes are used because the name contains a hyphen. Once installed, you can set a column's default value to `uuid_generate_v4()`."
      },
      "notes": [
        "Another common UUID generation extension is `pgcrypto`, which provides the `gen_random_uuid()` function.",
        "Using UUIDs can be beneficial in distributed systems where auto-incrementing integers might collide."
      ]
    }
  ]
};