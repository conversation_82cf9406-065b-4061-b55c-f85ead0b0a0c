export const command0 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Extension Management",
    "List Available Extensions"
  ],
  "description": "Displays a comprehensive list of all extensions that are available for installation in the current PostgreSQL instance, showing their names, default versions, and descriptions.",
  "query_template": "SELECT name, default_version, comment FROM pg_available_extensions ORDER BY name;",
  "components": [
    {
      "component": "SELECT name, ...",
      "name": "Extension Name",
      "description": "The official name of the extension, used in the `CREATE EXTENSION` command."
    },
    {
      "component": "SELECT ..., default_version, ...",
      "name": "Default Version",
      "description": "The version of the extension that will be installed if no specific version is requested."
    },
    {
      "component": "SELECT ..., comment",
      "name": "Description",
      "description": "A brief, helpful description of the extension's purpose."
    },
    {
      "component": "FROM pg_available_extensions",
      "name": "System View",
      "description": "The built-in system view that catalogs all extensions known to the PostgreSQL server."
    }
  ],
  "scenarios": [
    {
      "title": "Planning for New Functionality in a Project",
      "details": {
        "setup": "You are a database administrator or developer planning to add new capabilities to an application, such as full-text search, geospatial features, or UUID generation. You need to know which extensions your current PostgreSQL server supports before you commit to a technical path.",
        "goal": "To get a definitive list of all available extensions on the server to confirm if required extensions like `pg_trgm`, `postgis`, or `uuid-ossp` can be installed.",
        "outcome": "The query returns a sorted list of all available extensions. You can now see the exact names, versions, and descriptions, confirming you can proceed with your project plan."
      },
      "example": {
        "query": "SELECT name, default_version, comment FROM pg_available_extensions WHERE name IN ('postgis', 'pg_trgm', 'uuid-ossp');",
        "explanation": "This modified query filters the list to check specifically for three common and powerful extensions: `postgis` (for spatial data), `pg_trgm` (for advanced text search), and `uuid-ossp` (for generating UUIDs). It's a quick way to verify their availability."
      },
      "notes": [
        "This query shows extensions that *can be* installed, not what *is currently* installed. To see installed extensions, use the `\dx` meta-command in `psql` or query the `pg_extension` catalog.",
        "The list of available extensions is determined when PostgreSQL is compiled or installed via a package manager. If an extension is missing, it may need to be installed at the operating system level first."
      ]
    }
  ]
};