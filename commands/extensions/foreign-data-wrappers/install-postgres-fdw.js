export const command51 = {
  // --- This is a query, not a command-line tool ---
  "type": "query",

  "path": [
    "Extensions and Advanced Features",
    "Foreign Data Wrappers",
    "Install postgres_fdw"
  ],
  "description": "Installs the 'postgres_fdw' extension, enabling your database to connect to and query tables located in other remote PostgreSQL databases.",

  // --- Query-specific fields ---
  "query_template": "CREATE EXTENSION IF NOT EXISTS postgres_fdw;",
  "components": [
    {
      "component": "CREATE EXTENSION",
      "name": "Extension Installer",
      "description": "The standard SQL command to install an extension into the current database."
    },
    {
      "component": "IF NOT EXISTS",
      "name": "Conditional Clause",
      "description": "An optional but recommended clause that prevents the command from failing if the extension is already installed."
    },
    {
      "component": "postgres_fdw",
      "name": "Extension Name",
      "description": "The specific Foreign Data Wrapper used for connecting to other PostgreSQL servers."
    }
  ],

  // --- <PERSON><PERSON><PERSON> showing the full workflow ---
  "scenarios": [
    {
      "title": "Querying a Live Production Database from a Separate Reporting Server",
      "details": {
        "setup": "Your company has a live 'production_db' handling customer transactions. You also have a separate 'reporting_db' server for business intelligence. You need to run analytics on customer data without affecting the performance of the production server.",
        "goal": "To access the `customers` table from the `production_db` directly within the `reporting_db` as if it were a local table, enabling joins with other reporting data.",
        "outcome": "The `postgres_fdw` extension is enabled on the reporting server, and you can successfully run `SELECT * FROM remote_customers;` to see live data from the production database."
      },
      "example": {
        "query": "CREATE EXTENSION IF NOT EXISTS postgres_fdw;",
        "explanation": "This command is the essential first step. It must be run on the database you want to initiate the connection from (the 'reporting_db'). It installs the functions required to define and manage connections to remote PostgreSQL servers."
      },
      "notes": [
        "After running this command, you must perform the following steps:",
        "1. **Create Server**: `CREATE SERVER production_server FOREIGN DATA WRAPPER postgres_fdw OPTIONS (host 'prod.db.example.com', dbname 'production_db');`",
        "2. **Map User**: `CREATE USER MAPPING FOR reporting_user SERVER production_server OPTIONS (user 'remote_readonly_user', password 'secure_password');`",
        "3. **Create Foreign Table**: `CREATE FOREIGN TABLE remote_customers (id INT, name TEXT, signup_date DATE) SERVER production_server OPTIONS (schema_name 'public', table_name 'customers');`",
        "Ensure the firewall rules between the two servers allow connections on the PostgreSQL port (usually 5432)."
      ]
    }
  ]
};