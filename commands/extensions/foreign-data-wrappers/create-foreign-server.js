export const command52 = {
  "type": "query",
  "path": [
    "Extensions and Advanced Features",
    "Foreign Data Wrappers",
    "Create Foreign Server"
  ],
  "description": "Defines a connection to a remote data source, which is the first step in querying data from another PostgreSQL database (or other supported sources) as if it were local.",
  "query_template": "CREATE SERVER [server_name] FOREIGN DATA WRAPPER postgres_fdw OPTIONS (host '[remote_host]', dbname '[remote_database]', port '[port]');",
  "components": [
    {
      "component": "CREATE SERVER server_name",
      "name": "Define Server Name",
      "description": "Assigns a local, unique name for the foreign server you are defining."
    },
    {
      "component": "FOREIGN DATA WRAPPER postgres_fdw",
      "name": "Specify Wrapper",
      "description": "Tells PostgreSQL to use the 'postgres_fdw' extension to handle communication with the remote server. This extension must be created first."
    },
    {
      "component": "OPTIONS (...)",
      "name": "Connection Options",
      "description": "Provides the essential connection details for the remote database, such as its hostname, database name, and port."
    }
  ],
  "scenarios": [
    {
      "title": "Federating Data for a Centralized Analytics Platform",
      "details": {
        "setup": "Your company has an analytics database (`analytics_db`) and a separate, live production database (`production_db`) that handles user transactions. The analytics team needs to join user data with sales data without creating complex ETL pipelines.",
        "goal": "To establish a connection from the `analytics_db` to the `production_db` so that analysts can query production tables directly in a read-only manner.",
        "outcome": "A foreign server named `production_server_link` is created within `analytics_db`, pointing to the remote production database. This object securely stores the connection details for future use."
      },
      "example": {
        "query": "CREATE SERVER production_server_link FOREIGN DATA WRAPPER postgres_fdw OPTIONS (host 'prod-db.example.com', dbname 'production_db', port '5432');",
        "explanation": "This query creates a named server object that represents the remote production database. The `postgres_fdw` wrapper knows how to communicate with another PostgreSQL instance using the provided host and dbname. This is the foundational step before defining user mappings and importing foreign tables."
      },
      "notes": [
        "Before running this, you must have the foreign data wrapper installed. Run `CREATE EXTENSION postgres_fdw;` if you haven't already.",
        "This command only defines the connection. It does not handle authentication. The next step is typically `CREATE USER MAPPING` to provide credentials.",
        "Ensure the host is reachable and firewall rules on the remote server permit connections from the analytics server."
      ]
    }
  ]
};