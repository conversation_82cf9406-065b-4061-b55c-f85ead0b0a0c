export const command3 = {
  // --- Type identifier ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Analyzing Query Performance",
    "Analyze Query with Execution"
  ],
  "description": "Executes a query and returns the actual execution plan, including timing information and row counts. It is one of the most powerful tools for identifying performance bottlenecks.",
  
  // --- Query-specific fields ---
  "query_template": "EXPLAIN ANALYZE SELECT [columns] FROM [table_name] WHERE [condition];",
  "components": [
    {
      "component": "EXPLAIN ANALYZE",
      "name": "Analyze with Execution",
      "description": "Instructs PostgreSQL to actually run the query and return the detailed plan it used, including real-world timing for each step."
    },
    {
      "component": "SELECT ...",
      "name": "Target Query",
      "description": "The specific SQL query whose performance you want to investigate."
    }
  ],
  
  // --- Scenario for practical application ---
  "scenarios": [
    {
      "title": "Diagnosing a Slow Customer Search Feature",
      "details": {
        "setup": "An internal dashboard has a feature to search for customers by their last name. The `customers` table has grown to 5 million rows, and searches have become unacceptably slow. An index has already been created on the `last_name` column.",
        "goal": "To determine precisely why the query is slow by checking if the `idx_customers_last_name` index is being used effectively.",
        "outcome": "The query plan is generated, revealing that the database is performing a 'Seq Scan' (full table scan) instead of an 'Index Scan', which is the root cause of the performance issue."
      },
      "example": {
        "query": "EXPLAIN ANALYZE SELECT id, first_name, email FROM customers WHERE last_name = 'Johnson';",
        "explanation": "This command runs the search query for 'Johnson' and provides the execution plan. The key is to look at the `planning time` and `execution time`. More importantly, the plan will show either an `Index Scan` (fast) or a `Seq Scan` (slow), confirming whether the index is being used. If a `Seq Scan` is present, it indicates a problem with the index or table statistics."
      },
      "notes": [
        "If an index exists but a `Seq Scan` is used, the most common reason is outdated table statistics. Run `ANALYZE customers;` to update them and then try this command again.",
        "Unlike `EXPLAIN`, `EXPLAIN ANALYZE` actually runs the query, so be cautious with `UPDATE` or `DELETE` statements as they will modify data.",
        "The costs (e.g., `cost=0.43..5.45`) are estimates made by the planner; the `actual time` is the most reliable metric for performance diagnosis."
      ]
    }
  ]
};