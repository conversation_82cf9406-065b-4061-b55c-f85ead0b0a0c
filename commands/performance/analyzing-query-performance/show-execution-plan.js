export const command2 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Analyzing Query Performance",
    "Show Execution Plan"
  ],
  "description": "Displays the estimated execution plan for a query without actually running it. This is a critical tool for predicting query performance, checking for index usage, and understanding the query planner's decisions.",
  "query_template": "EXPLAIN SELECT [columns] FROM [table] WHERE [condition];",
  "components": [
    {
      "component": "EXPLAIN",
      "name": "Explain Command",
      "description": "A keyword that precedes a query (SELECT, INSERT, UPDATE, DELETE) to tell PostgreSQL to return the planner's intended execution plan."
    },
    {
      "component": "SELECT ...",
      "name": "Target Query",
      "description": "The actual SQL query whose plan you want to analyze. This query is not executed."
    }
  ],
  "scenarios": [
    {
      "title": "Verifying Index Usage Before Deploying a New Feature",
      "details": {
        "setup": "A developer has written a query to fetch recent user activity for a new dashboard feature. The `user_actions` table is very large, and an index exists on the `action_timestamp` column.",
        "goal": "To safely check if the PostgreSQL query planner intends to use the `idx_actions_timestamp` index for the query, without consuming server resources by running a potentially slow query on the production database.",
        "outcome": "The execution plan generated by `EXPLAIN` clearly shows an 'Index Scan' on the `user_actions` table, confirming the query is efficient and safe to deploy."
      },
      "example": {
        "query": "EXPLAIN SELECT user_id, action_type FROM user_actions WHERE action_timestamp >= '2025-09-01T00:00:00Z';",
        "explanation": "This command asks the planner: 'What is your plan for this query?' The output reveals the intended steps, such as using an 'Index Scan' versus a 'Sequential Scan'. This provides a zero-cost way to validate query performance before it impacts users."
      },
      "notes": [
        "Unlike `EXPLAIN ANALYZE`, `EXPLAIN` only shows the estimated plan and costs; it does not execute the query.",
        "If the planner chooses a 'Sequential Scan' when you expect an 'Index Scan', it could mean table statistics are stale. Running `ANALYZE user_actions;` may fix this."
      ]
    },
    {
      "title": "Understanding a Slow Join Operation in a Report",
      "details": {
        "setup": "An analyst is building a report by joining a large `orders` table with a `customers` table. The query is running much slower than expected in the development environment.",
        "goal": "To understand how PostgreSQL is joining the two tables (e.g., Hash Join, Nested Loop) and identify the cause of the poor performance.",
        "outcome": "The `EXPLAIN` output shows the planner is using a 'Nested Loop' join, which is inefficient for large datasets. This immediately suggests that an index is missing on the join key column (`orders.customer_id`)."
      },
      "example": {
        "query": "EXPLAIN SELECT c.customer_name, o.order_id FROM customers c JOIN orders o ON c.id = o.customer_id WHERE c.region = 'West';",
        "explanation": "The execution plan details the join strategy. Seeing a 'Nested Loop' is a red flag. After adding an index to `orders.customer_id`, running `EXPLAIN` again would likely show a much more efficient 'Hash Join' or 'Merge Join', confirming the problem is solved."
      },
      "notes": [
        "The join strategy chosen by the planner is one of the most critical factors for query performance.",
        "`EXPLAIN` is the primary tool for diagnosing these issues without waiting for a long-running query to finish."
      ]
    }
  ]
};