export const command0 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Monitoring Server Activity",
    "Show Active Connections"
  ],
  "description": "Views all current client connections to the database server, showing their status, origin, and the specific query they are currently executing. It is essential for real-time diagnostics.",
  "query_template": "SELECT pid, usename, datname, client_addr, state, query FROM pg_stat_activity;",
  "components": [
    {
      "component": "SELECT pid, usename, ...",
      "name": "Selected Columns",
      "description": "Shows key details for each connection: `pid` (Process ID), `usename` (User), `datname` (Database), `client_addr` (Client IP), `state` (e.g., 'active', 'idle'), and the `query` text."
    },
    {
      "component": "FROM pg_stat_activity",
      "name": "System View",
      "description": "A built-in PostgreSQL view that provides a real-time snapshot of all server processes and their current activity."
    }
  ],
  "scenarios": [
    {
      "title": "Identifying a Long-Running Query Causing Application Slowdown",
      "details": {
        "setup": "Users are reporting that a web application has become unresponsive. You suspect a database query is stuck or taking an excessive amount of time to complete, holding locks on important tables.",
        "goal": "To find the specific query that is causing the bottleneck, identify who is running it, and gather the necessary information (the PID) to potentially terminate it.",
        "outcome": "You successfully identify a query with a `state` of 'active' that has been running for several minutes, allowing you to diagnose the issue or terminate the process to restore application service."
      },
      "example": {
        "query": "SELECT pid, usename, now() - query_start AS duration, query FROM pg_stat_activity WHERE state = 'active' ORDER BY duration DESC LIMIT 10;",
        "explanation": "This enhanced query filters for only `active` connections, calculates how long each has been running (`duration`), and sorts the results to show the 10 longest-running queries at the top. This immediately pinpoints the most likely cause of the slowdown."
      },
      "notes": [
        "Once you have the `pid` of a problematic query, you can terminate it using `SELECT pg_terminate_backend(pid);`.",
        "Be cautious when terminating queries, as it will abruptly end the operation and roll back the transaction."
      ]
    },
    {
      "title": "Investigating 'Too Many Connections' Errors",
      "details": {
        "setup": "Your application is failing with errors like 'FATAL: remaining connection slots are reserved for non-replication superuser connections'. This means you have reached the `max_connections` limit.",
        "goal": "To understand where the connections are coming from, which users or applications are holding them, and their current state (e.g., are they actively working or just sitting idle?).",
        "outcome": "You get a clear count of connections per application or user, revealing that a specific service has a connection leak or is not properly closing its connections."
      },
      "example": {
        "query": "SELECT usename, client_addr, state, COUNT(*) AS connection_count FROM pg_stat_activity GROUP BY usename, client_addr, state ORDER BY connection_count DESC;",
        "explanation": "This query aggregates the connections by user, client IP address, and state. It quickly reveals which user or machine is consuming the most connections and whether those connections are `idle`, `active`, or `idle in transaction` (a particularly problematic state)."
      },
      "notes": [
        "A large number of connections in the `idle in transaction` state can hold locks and prevent VACUUM from cleaning up dead rows. This is a common source of performance degradation."
      ]
    }
  ]
};