export const command1 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Monitoring Server Activity",
    "Terminate a Specific Connection"
  ],
  "description": "Immediately terminates a specific backend process and its associated database connection. This is a powerful tool for stopping runaway queries or freeing locked resources. Requires superuser privileges or membership in the `pg_signal_backend` role.",
  "query_template": "SELECT pg_terminate_backend([pid]);",
  "components": [
    {
      "component": "pg_terminate_backend(pid)",
      "name": "Terminate Backend Function",
      "description": "A built-in administrative function that sends a SIGTERM signal to the backend process specified by the 'pid'."
    },
    {
      "component": "[pid]",
      "name": "Process ID",
      "description": "The unique integer identifier of the target backend process. You must find this ID by querying the `pg_stat_activity` view first."
    }
  ],
  "scenarios": [
    {
      "title": "Stopping a Long-Running Query Locking a Production Table",
      "details": {
        "setup": "A data analyst has run a complex, unoptimized query on the main `orders` table. The query has been running for 30 minutes, holding a strong lock that is now blocking new customer orders from being written to the database.",
        "goal": "To identify the process ID (PID) of the blocking query and terminate it immediately to unblock the `orders` table and allow business operations to resume.",
        "outcome": "The blocking query is terminated, its transaction is rolled back, the lock is released, and new orders can be processed successfully."
      },
      "example": {
        "query": "-- Step 1: Find the PID of the blocking query\nSELECT pid, usename, query, wait_event_type, state\nFROM pg_stat_activity\nWHERE state = 'active' AND wait_event_type = 'Lock';\n\n-- Assume the above query returns a row with pid = 54321 for the problem query.\n\n-- Step 2: Terminate the specific process\nSELECT pg_terminate_backend(54321);",
        "explanation": "The process is a two-step operation. First, you query the `pg_stat_activity` view to find the `pid` of the active query that is causing the lock. Once you have identified the correct `pid` (in this case, `54321`), you pass it as an argument to the `pg_terminate_backend` function to force the connection to close."
      },
      "notes": [
        "Use this command with caution. It abruptly ends the user's session, and any in-progress transaction will be rolled back.",
        "Consider using `pg_cancel_backend(pid)` first, as it attempts a cleaner, more graceful shutdown of the query (like pressing Ctrl+C). Use `pg_terminate_backend` if `pg_cancel_backend` is ineffective.",
        "In PostgreSQL 10 and newer, you can grant the `pg_signal_backend` role to non-superusers to allow them to cancel or terminate sessions."
      ]
    }
  ]
};