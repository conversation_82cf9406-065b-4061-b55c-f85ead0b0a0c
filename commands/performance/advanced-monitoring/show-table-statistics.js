export const command16 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Advanced Monitoring",
    "Show Table Statistics"
  ],
  "description": "Displays comprehensive statistics about table usage, update/delete activity, and maintenance (vacuum/analyze) history to identify performance issues like table bloat.",
  "query_template": "SELECT schemaname, tablename, n_live_tup, n_dead_tup, last_vacuum, last_autovacuum, last_analyze, last_autoanalyze FROM pg_stat_user_tables ORDER BY n_dead_tup DESC;",
  "components": [
    {
      "component": "pg_stat_user_tables",
      "name": "Statistics View",
      "description": "A built-in PostgreSQL view that collects and displays statistics for all non-system tables."
    },
    {
      "component": "n_dead_tup",
      "name": "Dead Tuples",
      "description": "Represents the number of row versions marked for deletion that still occupy disk space. This is a primary indicator of table bloat."
    },
    {
      "component": "last_autovacuum / last_autoanalyze",
      "name": "Maintenance Timestamps",
      "description": "Shows the last time the table was automatically processed by the vacuum or analyze daemons, which is key for diagnosing configuration issues."
    },
    {
      "component": "ORDER BY n_dead_tup DESC",
      "name": "Sorting Clause",
      "description": "Sorts the results to immediately bring the tables with the most significant bloat to the top of the list."
    }
  ],
  "scenarios": [
    {
      "title": "Proactively Identifying and Fixing Table Bloat",
      "details": {
        "setup": "Your application's main `orders` table undergoes thousands of updates and deletions daily. Over time, you notice that queries against this table are becoming slower, and the database storage size is growing faster than expected, even though the number of live rows remains stable.",
        "goal": "To identify which tables are the most 'bloated' (i.e., have the highest number of dead rows) and determine if the autovacuum process is keeping up with the workload.",
        "outcome": "The query returns a sorted list of tables, showing the `orders` table at the top with a very high number of dead tuples and a recent `last_autovacuum` timestamp, indicating the table is a candidate for more aggressive vacuum tuning or manual intervention."
      },
      "example": {
        "query": "SELECT schemaname, tablename, n_live_tup, n_dead_tup, last_vacuum, last_autovacuum, last_analyze, last_autoanalyze FROM pg_stat_user_tables ORDER BY n_dead_tup DESC LIMIT 10;",
        "explanation": "This query scans the statistics for all user tables and orders them by the number of dead tuples in descending order. By focusing on the top results, you can quickly spot which tables are not being cleaned up effectively. The `n_live_tup` vs. `n_dead_tup` ratio is a key health indicator. A high number of dead tuples suggests that `VACUUM` operations are not frequent or aggressive enough to reclaim wasted space."
      },
      "notes": [
        "A high `n_dead_tup` count is a clear signal for action. Consider running a manual `VACUUM ANALYZE your_table;` to clean up the table and update its statistics.",
        "If `last_autovacuum` is not recent for a table with high dead tuples, you may need to tune the autovacuum settings for that specific table.",
        "Note that these statistics are collected by PostgreSQL's statistics collector process and may not be real-time. They are reset when the server is restarted or `pg_stat_reset()` is called."
      ]
    }
  ]
};