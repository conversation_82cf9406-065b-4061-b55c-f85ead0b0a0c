export const command18 = {
  // --- Identifier for application logic ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Advanced Monitoring",
    "Show Cache Hit Ratios"
  ],
  "description": "Calculates the cache hit ratios for both indexes and tables to provide a key performance indicator of memory usage. A high hit rate means the database is efficiently serving data from RAM, while a low rate suggests excessive disk I/O.",

  // --- Query-specific fields ---
  "query_template": "SELECT 'index hit rate' AS name, (sum(idx_blks_hit)) / nullif(sum(idx_blks_hit + idx_blks_read),0) AS ratio FROM pg_statio_user_indexes UNION ALL SELECT 'table hit rate' AS name, sum(heap_blks_hit) / nullif(sum(heap_blks_hit) + sum(heap_blks_read),0) AS ratio FROM pg_statio_user_tables;",
  "components": [
    {
      "component": "pg_statio_user_indexes",
      "name": "Index Statistics View",
      "description": "A system view that provides I/O statistics for all user-defined indexes, tracking how many blocks were read from disk vs. found in cache."
    },
    {
      "component": "pg_statio_user_tables",
      "name": "Table Statistics View",
      "description": "A system view that provides I/O statistics for all user-defined tables (referred to as heaps)."
    },
    {
      "component": "sum(blks_hit) / sum(blks_hit + blks_read)",
      "name": "Hit Rate Formula",
      "description": "This calculation determines the ratio of data blocks served from memory (hit) versus the total blocks requested (hit + read from disk)."
    },
    {
      "component": "UNION ALL",
      "name": "Combine Results",
      "description": "Merges the results from the index hit rate query and the table hit rate query into a single, clean output."
    }
  ],

  // --- Scenario for practical application ---
  "scenarios": [
    {
      "title": "Evaluating Database Memory Performance After Scaling Users",
      "details": {
        "setup": "Your application has recently seen a 50% increase in active users. You've noticed a general slowdown in response times and higher-than-usual disk I/O on the database server. You suspect the server's memory configuration is no longer adequate for the workload.",
        "goal": "To quantitatively measure the memory cache performance of the database. The objective is to confirm if the cache hit rates are at or above the recommended 99% target for a healthy, well-configured system.",
        "outcome": "The query returns two rows: 'index hit rate' and 'table hit rate' with their respective ratios (e.g., 0.998 and 0.995). These high values would prove that memory is being used efficiently. Conversely, a low value like 0.89 would confirm that memory is a bottleneck."
      },
      "example": {
        "query": "SELECT\n  'index hit rate' AS name,\n  (sum(idx_blks_hit) * 100) / nullif(sum(idx_blks_hit + idx_blks_read),0) AS percentage\nFROM\n  pg_statio_user_indexes\nUNION ALL\nSELECT\n  'table hit rate' AS name,\n  (sum(heap_blks_hit) * 100) / nullif(sum(heap_blks_hit) + sum(heap_blks_read),0) AS percentage\nFROM\n  pg_statio_user_tables;",
        "explanation": "This version of the query is slightly modified to return a more readable percentage. A result above 99% for both indexes and tables indicates that PostgreSQL's shared buffer cache is well-sized and the server is not being forced to read frequently-accessed data from the much slower disk subsystem."
      },
      "notes": [
        "For a database to be considered well-tuned, both hit rates should ideally be above 99%.",
        "These statistics reset when the PostgreSQL server is restarted. For meaningful results, run this query when the server has been operating under a typical workload for several hours or days.",
        "If your hit rates are consistently low, the most common solution is to increase the `shared_buffers` parameter in your `postgresql.conf` configuration file."
      ]
    }
  ]
};