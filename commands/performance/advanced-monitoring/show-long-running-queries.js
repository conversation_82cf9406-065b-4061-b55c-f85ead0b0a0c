export const command15 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Advanced Monitoring",
    "Show Long Running Queries"
  ],
  "description": "Identifies currently active queries that have been running longer than a specified duration. This is essential for diagnosing performance bottlenecks and finding resource-intensive operations.",
  "query_template": "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '[duration]' AND state = 'active';",
  "components": [
    {
      "component": "pg_stat_activity",
      "name": "Statistics View",
      "description": "A system view that shows one row for each server process, providing real-time information about its current state, activity, and the query being run."
    },
    {
      "component": "now() - pg_stat_activity.query_start",
      "name": "Duration Calculation",
      "description": "Calculates the total elapsed time since the query started by subtracting the `query_start` timestamp from the current time."
    },
    {
      "component": "interval '[duration]'",
      "name": "Time Threshold",
      "description": "A configurable interval (e.g., '1 minute', '10 minutes') used to define what qualifies as a 'long-running' query."
    },
    {
      "component": "state = 'active'",
      "name": "State Filter",
      "description": "Filters the results to show only queries that are currently executing, excluding idle connections or those in a transaction block."
    }
  ],
  "scenarios": [
    {
      "title": "Identifying Performance Bottlenecks on a Live Application",
      "details": {
        "setup": "The database monitoring system shows a sustained high CPU load. Users are reporting that the application is slow and sometimes times out. You suspect a rogue or inefficient query is consuming excessive resources.",
        "goal": "To find all currently running queries that have been active for more than 2 minutes, identify the source, and decide on a course of action.",
        "outcome": "The query returns a list of resource-intensive operations, including their process ID (pid), exact runtime, and full query text, allowing for immediate analysis."
      },
      "example": {
        "query": "SELECT pid, now() - pg_stat_activity.query_start AS duration, query FROM pg_stat_activity WHERE (now() - pg_stat_activity.query_start) > interval '2 minutes' AND state = 'active' ORDER BY duration DESC;",
        "explanation": "This query scans the `pg_stat_activity` view for active processes running longer than 2 minutes. Adding `ORDER BY duration DESC` is a useful enhancement that lists the longest-running queries first, helping you prioritize your investigation."
      },
      "notes": [
        "Running this query typically requires superuser privileges, or membership in the `pg_monitor` role in modern PostgreSQL versions.",
        "Once you identify a problematic query's `pid`, you can terminate it immediately using `SELECT pg_terminate_backend(pid);` if it poses a critical threat to database stability.",
        "For a non-destructive analysis, copy the text of the slow query and run it with `EXPLAIN ANALYZE` to understand its execution plan and find opportunities for optimization."
      ]
    }
  ]
};