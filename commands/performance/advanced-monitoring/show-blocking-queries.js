export const command14 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Advanced Monitoring",
    "Show Blocking Queries"
  ],
  "description": "A powerful diagnostic query that identifies which queries are currently blocked and, more importantly, which queries are causing the block. This is essential for resolving application hangs and deadlock situations.",
  "query_template": "SELECT blocked_locks.pid AS blocked_pid, blocked_activity.usename AS blocked_user, blocking_locks.pid AS blocking_pid, blocking_activity.usename AS blocking_user, blocked_activity.query AS blocked_statement, blocking_activity.query AS current_statement_in_blocking_process FROM pg_catalog.pg_locks blocked_locks JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype AND ... JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid WHERE NOT blocked_locks.GRANTED;",
  "components": [
    {
      "component": "pg_catalog.pg_locks",
      "name": "Lock Information View",
      "description": "A system view that provides real-time information about all active locks in the database. This query joins the view to itself to correlate waiting processes with lock-holding processes."
    },
    {
      "component": "pg_catalog.pg_stat_activity",
      "name": "Activity Monitoring View",
      "description": "Provides a snapshot of all current processes (backends) in the PostgreSQL server, including the user, process ID (PID), and the exact query being executed."
    },
    {
      "component": "WHERE NOT blocked_locks.GRANTED",
      "name": "Filtering for Blocked Queries",
      "description": "The crucial condition that filters the result to only show locks that are in a waiting state, meaning the process is currently blocked."
    }
  ],
  "scenarios": [
    {
      "title": "Resolving an Application Hang During a Data Import",
      "details": {
        "setup": "A web application's dashboard has become completely unresponsive. Simultaneously, a data import script is running. You suspect the import script has locked a table (e.g., `invoices`) that the dashboard needs to read.",
        "goal": "To quickly and precisely identify the Process ID (PID) of the import script that is holding the lock and see the exact statement from the web application that is being blocked.",
        "outcome": "The query returns a clear result showing the `blocking_pid` (the import script) and the `blocked_pid` (the application's query), along with the full text of both SQL statements, allowing for immediate diagnosis."
      },
      "example": {
        "query": "SELECT blocked_locks.pid AS blocked_pid, blocked_activity.usename AS blocked_user, blocking_locks.pid AS blocking_pid, blocking_activity.usename AS blocking_user, blocked_activity.query AS blocked_statement, blocking_activity.query AS current_statement_in_blocking_process FROM pg_catalog.pg_locks blocked_locks JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid JOIN pg_catalog.pg_locks blocking_locks ON blocking_locks.locktype = blocked_locks.locktype AND blocking_locks.DATABASE IS NOT DISTINCT FROM blocked_locks.DATABASE AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation AND blocking_locks.page IS NOT DISTINCT FROM blocked_locks.page AND blocking_locks.tuple IS NOT DISTINCT FROM blocked_locks.tuple AND blocking_locks.virtualxid IS NOT DISTINCT FROM blocked_locks.virtualxid AND blocking_locks.transactionid IS NOT DISTINCT FROM blocked_locks.transactionid AND blocking_locks.classid IS NOT DISTINCT FROM blocked_locks.classid AND blocking_locks.objid IS NOT DISTINCT FROM blocked_locks.objid AND blocking_locks.objsubid IS NOT DISTINCT FROM blocked_locks.objsubid AND blocking_locks.pid != blocked_locks.pid JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid WHERE NOT blocked_locks.GRANTED;",
        "explanation": "This comprehensive query joins `pg_locks` and `pg_stat_activity` to provide a full picture of the locking situation. It shows which process is blocked, who is running it, and the query they are trying to run, as well as the details of the process that is causing the block."
      },
      "notes": [
        "Once you have the `blocking_pid`, you can choose to either wait for it to finish or terminate it.",
        "To gently ask a query to cancel, use `SELECT pg_cancel_backend(blocking_pid);`.",
        "To forcefully terminate the entire process, use `SELECT pg_terminate_backend(blocking_pid);`. Use with caution as this can trigger a database recovery.",
        "Newer versions of PostgreSQL (9.6+) have a `pg_blocking_pids()` function that can simplify finding blockers."
      ]
    }
  ]
};