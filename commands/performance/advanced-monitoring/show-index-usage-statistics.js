export const command17 = {
  // --- Identifier for the new structure ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Advanced Monitoring",
    "Show Index Usage Statistics"
  ],
  "description": "Monitors the usage of all user-defined indexes to identify which are being used effectively and which might be unused, helping to optimize database performance.",

  // --- Query-specific fields ---
  "query_template": "SELECT schemaname, tablename, indexname, idx_scan, idx_tup_read, idx_tup_fetch FROM pg_stat_user_indexes ORDER BY idx_scan ASC;",
  "components": [
    {
      "component": "SELECT ...",
      "name": "Usage Columns",
      "description": "Selects key statistics about index usage."
    },
    {
      "component": "FROM pg_stat_user_indexes",
      "name": "System View",
      "description": "The system catalog view that collects statistics on index access."
    },
    {
      "component": "idx_scan",
      "name": "Index Scans",
      "description": "The number of times the index has been scanned. A value of 0 indicates the index may be unused."
    },
    {
      "component": "idx_tup_fetch",
      "name": "<PERSON><PERSON> Fetched",
      "description": "The number of table rows fetched using this index. A high number indicates the index is effective at locating data."
    },
    {
      "component": "ORDER BY idx_scan ASC",
      "name": "Sort by Usage",
      "description": "Sorts the results to show the least-used indexes first, making unused indexes easy to spot."
    }
  ],

  // --- Scenario-based enrichment ---
  "scenarios": [
    {
      "title": "Identifying and Removing Unused Indexes to Improve Write Performance",
      "details": {
        "setup": "Your application's database has been in production for years. Over time, features have changed and database schemas have evolved, likely leaving behind indexes that are no longer used by any queries. These unused indexes still add overhead to every `INSERT`, `UPDATE`, and `DELETE` operation on their tables.",
        "goal": "To find all indexes that have zero or very low usage since the last statistics reset, so they can be safely evaluated for removal, thereby reducing write overhead and freeing up disk space.",
        "outcome": "The query returns a list of all indexes, with the least-used ones at the top. You identify several indexes with `idx_scan` equal to 0, marking them as strong candidates for being dropped after a final review."
      },
      "example": {
        "query": "SELECT schemaname, tablename, indexname, idx_scan, idx_tup_fetch\nFROM pg_stat_user_indexes\nWHERE schemaname NOT IN ('pg_catalog', 'information_schema')\nORDER BY idx_scan ASC, idx_tup_fetch ASC\nLIMIT 20;",
        "explanation": "This query retrieves usage statistics and sorts them to show the 20 least-scanned indexes first. Filtering out system schemas (`pg_catalog`) focuses the results on your application's indexes. An `idx_scan` count of 0 is the primary indicator of an unused index."
      },
      "notes": [
        "Be cautious! Statistics in `pg_stat_user_indexes` are reset when the PostgreSQL server is restarted or when statistics are manually reset.",
        "Before dropping an index, confirm it isn't for a new or rarely used feature (e.g., an annual report query).",
        "Dropping an unused index (`DROP INDEX index_name;`) can improve the performance of write operations on the associated table."
      ]
    }
  ]
};