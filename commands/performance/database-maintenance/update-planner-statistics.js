export const command7 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Update Planner Statistics"
  ],
  "description": "Collects statistics about the distribution of data within a table. This information is critical for the PostgreSQL query planner to create efficient execution plans. It should be run after the table's data has been significantly changed.",
  "query_template": "ANALYZE [ ( option [, ...] ) ] [ table_and_columns [, ...] ];",
  "components": [
    {
      "component": "ANALYZE",
      "name": "Analyze Command",
      "description": "The SQL command to gather statistics."
    },
    {
      "component": "VERBOSE",
      "name": "Verbose Option",
      "description": "(Optional) Displays progress messages during execution, which is helpful for large tables."
    },
    {
      "component": "table_and_columns",
      "name": "Target Table/Columns",
      "description": "Specifies the table to analyze. You can optionally provide a list of columns to analyze only specific ones."
    }
  ],
  "scenarios": [
    {
      "title": "Optimizing Query Performance After a Bulk Data Import",
      "details": {
        "setup": "You have just imported 500,000 new records into a `products` table from a supplier's CSV file. Queries that filter on product categories that were part of the new import are running much slower than expected.",
        "goal": "To update the query planner's statistics so it becomes aware of the new data's distribution (e.g., new categories, price distributions), enabling it to choose the most efficient query plans.",
        "outcome": "The planner's statistics are updated. A subsequent `EXPLAIN ANALYZE` on a slow query now shows an efficient 'Index Scan' instead of a 'Sequential Scan', and query performance is restored."
      },
      "example": {
        "query": "ANALYZE VERBOSE products;",
        "explanation": "This command scans the `products` table and updates the internal statistics PostgreSQL keeps. The `VERBOSE` option provides live feedback on the process. With fresh statistics, the planner can accurately estimate the cost of different query plans and will be far more likely to use indexes on the newly added data."
      },
      "notes": [
        "While PostgreSQL's autovacuum daemon is designed to run `ANALYZE` automatically, it may not trigger immediately after a single, large transaction. Manual execution is crucial in this scenario.",
        "Consider running `VACUUM ANALYZE your_table;` instead, which both reclaims storage from dead tuples and updates statistics in a single pass."
      ]
    },
    {
      "title": "Fixing a Suboptimal Plan for a Skewed Data Distribution",
      "details": {
        "setup": "An `events` table logs user interactions. After a viral marketing campaign, 90% of the new events are of type 'campaign_click'. A daily report query that filters for other event types has become extremely slow.",
        "goal": "To inform the query planner about this new, highly skewed data distribution so it can avoid using an index that is now inefficient for querying the rare event types.",
        "outcome": "After analyzing, the planner understands that 'campaign_click' is extremely common and that using an index to find other event types is now highly efficient. The report's runtime is reduced from minutes to seconds.",
      },
      "example": {
        "query": "ANALYZE events;",
        "explanation": "By running `ANALYZE`, you provide the planner with a new histogram of the data in the `events` table. It learns that filtering for `type = 'rare_event'` will now return a very small number of rows, making an index scan the optimal choice, whereas it might have previously chosen a less efficient plan."
      },
      "notes": [
        "You can use `EXPLAIN ANALYZE` on your slow query before and after running `ANALYZE` to visually confirm the change and improvement in the query execution plan.",
        "For columns with highly skewed data, you may also consider increasing the statistics target for just that column (`ALTER TABLE events ALTER COLUMN type SET STATISTICS 1000;`) before running `ANALYZE` to give the planner even more detail."
      ]
    }
  ]
};