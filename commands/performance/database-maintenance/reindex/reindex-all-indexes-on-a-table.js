export const command10 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Reindex",
    "Reindex All Indexes on a Table"
  ],
  "description": "Rebuilds all indexes for a specific table. This is useful for reclaiming space from bloated indexes and restoring their performance. This command acquires a strong lock on the table, blocking all read and write operations until it completes.",
  "query_template": "REINDEX TABLE [table_name];",
  "components": [
    {
      "component": "REINDEX TABLE",
      "name": "Reindex Command",
      "description": "The SQL command that initiates the rebuilding of indexes on a specified table."
    },
    {
      "component": "[table_name]",
      "name": "Table Name",
      "description": "The name of the table whose indexes you want to rebuild."
    }
  ],
  "scenarios": [
    {
      "title": "Restoring Performance by Rebuilding Bloated Indexes",
      "details": {
        "setup": "You are managing a `job_queue` table where jobs are frequently added, processed, and deleted. Over several months, you notice that even simple queries on indexed columns (like `status`) are becoming slower. An analysis reveals significant index bloat, where the index files on disk are much larger than necessary.",
        "goal": "To shrink the indexes for the `job_queue` table, reclaim wasted disk space, and improve query performance during a scheduled maintenance window.",
        "outcome": "The indexes are rebuilt to their optimal size, disk space is freed, and query performance on the `job_queue` table returns to its expected speed."
      },
      "example": {
        "query": "REINDEX TABLE job_queue;",
        "explanation": "This command rebuilds all indexes associated with the `job_queue` table. It physically rewrites the index files, removing the bloat caused by the high volume of deletions, making index scans much more efficient."
      },
      "notes": [
        "CRITICAL: `REINDEX TABLE` places an `ACCESS EXCLUSIVE` lock on the table, preventing all reads and writes. It should only be run during a planned maintenance period when the application can be offline.",
        "To check for index bloat, you can use various diagnostic queries that compare the estimated size of an index with its actual size on disk."
      ]
    },
    {
      "title": "Rebuilding an Index on a Production System with No Downtime",
      "details": {
        "setup": "You've identified a bloated index on the primary key of your `users` table, a critical table that must remain available 24/7. You cannot afford to lock the table for the time it would take to do a standard reindex.",
        "goal": "To rebuild the specific bloated index without blocking application queries from reading or writing to the `users` table.",
        "outcome": "A new, healthy version of the index is built in the background. Once complete, PostgreSQL atomically swaps the old index for the new one, with no application downtime."
      },
      "example": {
        "query": "REINDEX INDEX CONCURRENTLY users_pkey;",
        "explanation": "The `CONCURRENTLY` option performs the reindex without taking a strong lock. It creates a new index in parallel and, once finished, replaces the old one. This is the preferred method for high-availability production environments, though it is more resource-intensive and may take longer to complete."
      },
      "notes": [
        "This command must be run outside of a transaction block (`BEGIN`/`COMMIT`).",
        "`REINDEX TABLE CONCURRENTLY` is not a valid command; you must reindex each index individually with `REINDEX INDEX CONCURRENTLY`."
      ]
    }
  ]
};