export const command9 = {
  // --- Type identifier for SQL ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Reindex",
    "Reindex a Specific Index"
  ],
  "description": "Rebuilds a specific index to remove bloat and fragmentation, which can improve query performance and reduce disk space usage.",

  // --- Query-specific fields ---
  "query_template": "REINDEX INDEX [your_index_name];",
  "components": [
    {
      "component": "REINDEX INDEX",
      "name": "Reindex Command",
      "description": "The SQL command used to rebuild an index."
    },
    {
      "component": "[your_index_name]",
      "name": "Index Name",
      "description": "The name of the specific index you want to rebuild."
    }
  ],

  // --- Scenario-based enrichment ---
  "scenarios": [
    {
      "title": "Restoring Performance on a Frequently Updated Table",
      "details": {
        "setup": "You are managing an application with an `orders` table. The `status` column is updated frequently as orders move from 'pending' to 'shipped' to 'delivered'. Over months of operation, you notice that queries filtering by `status` have become slower, even though an index exists.",
        "goal": "To rebuild the `idx_orders_status` index to eliminate fragmentation (bloat), shrink its size on disk, and restore the performance of queries that rely on it.",
        "outcome": "The index is rebuilt, and subsequent queries using the `status` column are noticeably faster. You may also observe a reduction in the physical storage size of the index."
      },
      "example": {
        "query": "REINDEX INDEX idx_orders_status;",
        "explanation": "This command specifically targets the `idx_orders_status` index for a rebuild. It is the most direct way to fix performance degradation caused by the bloat of a single, known problematic index."
      },
      "notes": [
        "**CRITICAL:** `REINDEX INDEX` locks the associated table against writes (`INSERT`, `UPDATE`, `DELETE`) for the duration of the operation. This can cause significant downtime on a production system.",
        "For production environments, the preferred method is `REINDEX INDEX CONCURRENTLY your_index_name;`. This option rebuilds the index without taking exclusive locks but takes longer and consumes more CPU/IO resources.",
        "Before reindexing, you can use the `pgstattuple` extension to analyze the index and confirm the level of bloat."
      ]
    }
  ]
};