export const command8 = {
  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Vacuum and Analyze"
  ],
  "command": "VACUUM ANALYZE [your_table_name];",
  "description": "Performs vacuuming (reclaiming storage from dead tuples) and statistical analysis (for the query planner) on a table. This is a critical routine maintenance task for database health and query performance.",
  "parameters": [
    {
      "flag": "ANALYZE",
      "name": "Analyze Operation",
      "description": "Updates the statistical metadata that the query planner uses to choose the most efficient execution plan for queries."
    },
    {
      "flag": "VERBOSE",
      "name": "Verbose Output",
      "description": "(Optional) Prints detailed progress information about the vacuum and analyze operations, which is useful for monitoring large tables."
    },
    {
      "flag": "your_table_name",
      "name": "Target Table",
      "description": "The specific table you want to process. If omitted, VACUUM ANALYZE will run on all tables in the current database."
    }
  ],
  "scenarios": [
    {
      "title": "Reclaiming Space After a Major Data Purge",
      "details": {
        "setup": "Your application archives old data nightly. A recent run deleted over 5 million rows from the `user_sessions` table, leaving the table file on disk heavily bloated with unused space.",
        "goal": "To reclaim the storage occupied by the deleted rows, making it available for new data, and to update the table's statistics to reflect its new, smaller size.",
        "outcome": "The `user_sessions` table no longer consumes excess disk space, and the query planner is aware of the data change, ensuring future queries on this table remain fast."
      },
      "example": {
        "command": "VACUUM (VERBOSE, ANALYZE) user_sessions;",
        "explanation": "This command specifically targets the `user_sessions` table. `VACUUM` reclaims the space from the deleted rows. `ANALYZE` updates the table statistics. Including `VERBOSE` is a best practice for manual operations, as it provides a clear report of the actions being performed and the space recovered."
      },
      "notes": [
        "Standard `VACUUM` does not shrink the file size on disk; it makes the space internally available. To return space to the OS, a more disruptive `VACUUM FULL` is needed, which locks the table.",
        "PostgreSQL's autovacuum daemon is designed to handle this automatically, but a manual `VACUUM ANALYZE` is often necessary after exceptionally large data changes."
      ]
    },
    {
      "title": "Optimizing Performance After a Large Data Import",
      "details": {
        "setup": "You have just completed a bulk import of 10 million product records into a new `products_2025_catalog` table. The initial search queries are running much slower than expected.",
        "goal": "To generate vital statistics for the newly loaded table so the PostgreSQL query planner can make intelligent decisions about how to execute queries against it.",
        "outcome": "PostgreSQL now has a detailed statistical profile of the `products_2025_catalog` table, leading to a dramatic improvement in query execution times."
      },
      "example": {
        "command": "VACUUM ANALYZE products_2025_catalog;",
        "explanation": "In this case, `ANALYZE` is the most critical part. Without it, the planner has no information on the data distribution within the new table and may choose slow methods like sequential scans. Running `VACUUM` has little effect here (as there are no dead rows) but running them together is a standard, safe practice."
      },
      "notes": [
        "It is a best practice to run `VACUUM ANALYZE` on any table immediately after a large bulk data loading operation.",
        "For tables with complex data distributions, you might need to increase the `default_statistics_target` for more granular statistics."
      ]
    }
  ]
};