export const command5 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Vacuum",
    "Verbose Vacuum"
  ],
  "description": "Performs a standard VACUUM operation while providing detailed, real-time output about the process. This is invaluable for diagnosing table bloat and understanding the impact of the maintenance task.",
  "query_template": "VACUUM (VERBOSE) [your_table_name];",
  "components": [
    {
      "component": "VACUUM",
      "name": "Vacuum Command",
      "description": "The core command that reclaims storage occupied by dead tuples, making it available for reuse."
    },
    {
      "component": "VERBOSE",
      "name": "Verbose Option",
      "description": "Instructs the command to emit detailed progress messages, including the number of dead tuples removed and pages processed."
    },
    {
      "component": "(ANALYZE)",
      "name": "Analyze Option (Recommended)",
      "description": "An optional keyword that can be added to also update the statistical information used by the query planner. Often used together: `VACUUM (VERBOSE, ANALYZE) ...`"
    }
  ],
  "scenarios": [
    {
      "title": "Investigating Performance Degradation on a High-Traffic Table",
      "details": {
        "setup": "An `orders` table in an e-commerce database is experiencing slow queries after a major sales event. You suspect table bloat due to a high volume of `UPDATE` and `DELETE` operations, and you believe autovacuum hasn't caught up.",
        "goal": "To manually clean the `orders` table and get immediate, detailed feedback to confirm if a significant number of dead tuples are being removed.",
        "outcome": "The command outputs a live log, confirming that thousands of dead tuples were removed and the table's free space map was updated. This validates the bloat hypothesis and provides insight into the table's health."
      },
      "example": {
        "query": "VACUUM (VERBOSE) orders;",
        "explanation": "The `VERBOSE` option provides a step-by-step report of the vacuum process for the `orders` table. This output is critical for diagnostics, allowing you to see exactly how much space is being reclaimed in real-time without querying system catalogs."
      },
      "notes": [
        "A standard `VACUUM` reclaims space for re-use within the table but does not shrink the file size on disk. To return space to the OS, the more aggressive `VACUUM FULL` is needed, but it locks the table completely.",
        "For a complete maintenance operation, it's best practice to also update query planner statistics: `VACUUM (VERBOSE, ANALYZE) orders;`"
      ]
    },
    {
      "title": "Monitoring a Scheduled Maintenance Script",
      "details": {
        "setup": "You have a nightly maintenance script that runs `VACUUM` on several key tables. You need to improve the script's logging to create a clear audit trail of its activity for later review.",
        "goal": "To capture detailed output from the `VACUUM` process into a log file, confirming which tables were processed and how much cleanup was performed.",
        "outcome": "The script's log file now contains detailed reports for each table, showing completion times and the number of dead rows removed, which helps in tracking database health over time."
      },
      "example": {
        "query": "VACUUM (VERBOSE, ANALYZE) user_sessions;",
        "explanation": "When this command is executed within a script (e.g., via `psql -c`), the `VERBOSE` output is sent to standard output. This allows it to be easily redirected to a log file (`>> /var/log/pg_maintenance.log`), creating a self-documenting record of the maintenance work."
      },
      "notes": [
        "Regularly reviewing these logs can help you identify tables that might need more aggressive autovacuum settings.",
        "The `ANALYZE` keyword is included to ensure that after cleaning, the query planner has the most current statistics for optimal query performance."
      ]
    }
  ]
};