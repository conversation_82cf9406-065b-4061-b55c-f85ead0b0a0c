export const command4 = {
  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Vacuum",
    "Standard Vacuum"
  ],
  "command": "VACUUM [ ( option ) ] [ table_name ];",
  "description": "Reclaims storage occupied by dead tuples (i.e., from deleted or updated rows). This makes the space available for re-use by future inserts and updates. It also updates the table's visibility map, which can speed up index-only scans. It is safe to run during normal database operations as it does not exclusively lock the table.",
  "parameters": [
    {
      "flag": "VERBOSE",
      "name": "Verbose Output",
      "description": "Prints a detailed report of the vacuuming activity, which is useful for confirming its effect."
    },
    {
      "flag": "table_name",
      "name": "Target Table",
      "description": "Specifies a single table to vacuum. If this is omitted, VACUUM will process every table in the current database that the user has permission to access."
    }
  ],
  "scenarios": [
    {
      "title": "Managing Table Bloat After Bulk Data Archiving",
      "details": {
        "setup": "You manage a large `event_logs` table where millions of records are stored. A data retention policy requires a nightly script to `DELETE` all records older than 180 days. You notice that even after these large deletions, the table's size on disk never decreases.",
        "goal": "To reclaim the storage space within the table file that was used by the deleted log entries, making it available for new data and preventing uncontrolled table growth (bloat).",
        "outcome": "The internal free space within the `event_logs` table is now available for new `INSERT` operations. The physical size of the table file on disk stabilizes instead of constantly growing."
      },
      "example": {
        "command": "VACUUM VERBOSE event_logs;",
        "explanation": "This command specifically targets the `event_logs` table to recover the space left by the deleted rows. The `VERBOSE` option provides a helpful summary, confirming how many 'dead tuples' were removed and how many pages are now available."
      },
      "notes": [
        "A standard `VACUUM` does not shrink the table file to return disk space to the operating system. It only frees up space for reuse *within* the file. To return space to the OS, you must use `VACUUM FULL`, which locks the table and is a much more disruptive operation.",
        "PostgreSQL's `autovacuum` daemon is designed to handle this process automatically. A manual `VACUUM` is typically only necessary after an unusually large number of rows have been updated or deleted in a short period."
      ]
    }
  ]
};