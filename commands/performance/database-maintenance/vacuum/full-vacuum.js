export const command6 = {
  // --- Set type to 'query' ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Database Maintenance",
    "Vacuum",
    "Full Vacuum"
  ],
  "description": "Reclaims all fragmented space by completely rewriting the table and its indexes, minimizing its physical size on disk. This command is slow and requires an ACCESS EXCLUSIVE lock, blocking all other operations (including reads) on the table.",
  
  // --- Query-specific fields ---
  "query_template": "VACUUM FULL [VERBOSE] [table_name];",
  "components": [
    {
      "component": "VACUUM FULL",
      "name": "Full Vacuum Operation",
      "description": "The command that initiates a full table rewrite. Unlike a standard VACUUM, it returns disk space to the operating system."
    },
    {
      "component": "VERBOSE",
      "name": "Verbose Output (Optional)",
      "description": "Provides detailed progress messages, which is highly recommended for long-running operations."
    },
    {
      "component": "[table_name]",
      "name": "Target Table",
      "description": "The specific table you want to shrink. If omitted, it processes all tables in the current database, which is extremely disruptive and should be done with caution."
    }
  ],
  
  // --- <PERSON><PERSON><PERSON> highlighting the correct use case ---
  "scenarios": [
    {
      "title": "Reclaiming Disk Space After Archiving Massive Amounts of Old Data",
      "details": {
        "setup": "You manage a large `audit_trail` table that has grown to 1TB. As part of a data retention policy, you have just deleted 90% of the rows (900GB of data). A standard `VACUUM` has run, but the OS still reports the table file is using 1TB of disk space.",
        "goal": "To shrink the physical file size of the `audit_trail` table on the disk and return the 900GB of free space to the operating system.",
        "outcome": "The `audit_trail` table's physical size is reduced to approximately 100GB, and the reclaimed disk space is now available to the operating system."
      },
      "example": {
        "query": "VACUUM FULL VERBOSE audit_trail;",
        "explanation": "This command locks the `audit_trail` table and creates a new, compacted copy of it with no wasted space. Once complete, the old file is deleted, and the disk space is released. The `VERBOSE` option allows you to monitor its progress during the maintenance window."
      },
      "notes": [
        "CRITICAL WARNING: `VACUUM FULL` takes an `ACCESS EXCLUSIVE` lock, which blocks ALL reads and writes to the table. It must only be run during a planned maintenance window.",
        "This operation can be very slow and requires extra disk space (up to the size of the table being rewritten) to complete.",
        "For routine maintenance to prevent table bloat, use the standard `VACUUM` command, which does not lock the table against reads/writes.",
        "Consider using the `pg_repack` extension as a more production-friendly alternative. It can reclaim space without requiring a long-held exclusive lock."
      ]
    }
  ]
};