export const command19 = {
  // --- Set as a query type ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Query Analysis",
    "Show Most Expensive Queries"
  ],
  "description": "Identifies the most time-consuming queries that have run on the server, providing critical data for performance tuning. Requires the `pg_stat_statements` extension.",
  
  // --- Query-specific structure ---
  "query_template": "SELECT query, calls, total_exec_time, mean_exec_time, rows FROM pg_stat_statements ORDER BY total_exec_time DESC LIMIT [N];",
  "components": [
    {
      "component": "FROM pg_stat_statements",
      "name": "Statistics View",
      "description": "The data source; a special view created by the `pg_stat_statements` extension that tracks statistics for every executed query."
    },
    {
      "component": "ORDER BY total_exec_time DESC",
      "name": "Ranking Clause",
      "description": "Sorts the queries to show the ones that have consumed the most cumulative execution time at the top. This is the key to finding the biggest performance drains."
    },
    {
      "component": "LIMIT 10",
      "name": "Limit Clause",
      "description": "Restricts the output to the top N (in this case, 10) results, making the list manageable and focused on the worst offenders."
    }
  ],

  // --- Scenario-based enrichment ---
  "scenarios": [
    {
      "title": "Identifying Performance Bottlenecks in a Production Application",
      "details": {
        "setup": "A web application has become noticeably slower over the past few weeks. The database is the primary suspect, but it's unclear which features or API calls are causing the slowdown. The `pg_stat_statements` extension is enabled and has been collecting data.",
        "goal": "To get a data-driven list of the most resource-intensive SQL queries, so the development team can prioritize optimization efforts effectively.",
        "outcome": "The query returns a ranked list of the top 10 queries that have consumed the most server time, providing a clear starting point for deeper analysis with `EXPLAIN`."
      },
      "example": {
        "query": "SELECT query, calls, total_exec_time, mean_exec_time, rows FROM pg_stat_statements ORDER BY total_exec_time DESC LIMIT 10;",
        "explanation": "This query reads from the statistics view and ranks all tracked queries by their total accumulated execution time (`total_exec_time`). The output shows the query text, how many times it was called (`calls`), and its average execution time (`mean_exec_time`), immediately highlighting the most impactful queries on the system."
      },
      "notes": [
        "**Prerequisite**: The `pg_stat_statements` extension must be enabled. This requires adding `shared_preload_libraries = 'pg_stat_statements'` to `postgresql.conf` (needs a server restart) and then running `CREATE EXTENSION pg_stat_statements;` in the database.",
        "A high `total_exec_time` but low `mean_exec_time` often points to a very frequent query that is fast individually but slow in aggregate (e.g., an N+1 problem in an ORM).",
        "For a fresh analysis over a specific period, you can reset the statistics for all databases by running `SELECT pg_stat_statements_reset();` (requires superuser privileges).",
        "In PostgreSQL versions before 13, the time columns are named `total_time` and `mean_time`."
      ]
    }
  ]
};