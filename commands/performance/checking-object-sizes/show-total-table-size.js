export const command13 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Checking Object Sizes",
    "Show Total Table Size"
  ],
  "description": "Calculates the total disk space used by a specific table, including its data, all associated indexes, and any TOAST data, presenting it in a human-readable format (e.g., MB, GB).",
  "query_template": "SELECT pg_size_pretty(pg_total_relation_size('[table_name]'));",
  "components": [
    {
      "component": "pg_total_relation_size('[table_name]')",
      "name": "Total Size Function",
      "description": "The core function that computes the complete size of the relation (table). It aggregates the size of the main data heap, all indexes, the TOAST table (for large field storage), and the free space map."
    },
    {
      "component": "pg_size_pretty(...)",
      "name": "Formatting Function",
      "description": "A convenient wrapper function that takes a size in bytes (as a bigint) and converts it into an easy-to-read string with units like KB, MB, GB, or TB."
    }
  ],
  "scenarios": [
    {
      "title": "Investigating Disk Space Usage for Capacity Planning",
      "details": {
        "setup": "You are a database administrator managing a production server. You've received an alert that disk space is nearing 80% capacity. You suspect that the `activity_logs` table, which records user actions, is the primary cause of the recent growth.",
        "goal": "To quickly determine the exact total disk space consumed by the `activity_logs` table and all of its indexes to confirm your suspicion and inform your capacity planning strategy.",
        "outcome": "The query returns a single, clear value, such as '72 GB', immediately showing the table's total footprint on the disk."
      },
      "example": {
        "query": "SELECT pg_size_pretty(pg_total_relation_size('activity_logs'));",
        "explanation": "This query provides a comprehensive size measurement. `pg_total_relation_size` ensures that the space used by indexes (which can be substantial) is included in the calculation, giving a true picture of the table's impact. `pg_size_pretty` makes the result instantly understandable without manual byte-to-gigabyte conversion."
      },
      "notes": [
        "To see a breakdown of table vs. index size, you can use: `SELECT pg_size_pretty(pg_relation_size('table_name')) AS data_size, pg_size_pretty(pg_indexes_size('table_name')) AS indexes_size;`",
        "This command is essential for identifying table bloat. If a table's size is much larger than expected based on its row count, it may need maintenance with `VACUUM FULL` or a tool like `pg_repack`."
      ]
    }
  ]
};