export const command11 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "Checking Object Sizes",
    "Show Database Size"
  ],
  "description": "Calculates the total disk space used by a specific database. It is commonly used with pg_size_pretty() to get a human-readable size.",
  "query_template": "SELECT pg_size_pretty(pg_database_size('your_database_name'));",
  "components": [
    {
      "component": "pg_database_size('database_name')",
      "name": "Database Size Function",
      "description": "The core function that takes a database name as a text argument and returns its total size in bytes (as a bigint)."
    },
    {
      "component": "pg_size_pretty(...)",
      "name": "Pretty Formatting Function",
      "description": "A convenient helper function that takes a size in bytes (from another function) and converts it into a human-readable string with units like kB, MB, GB, or TB."
    }
  ],
  "scenarios": [
    {
      "title": "Monitoring Database Growth for Capacity Planning",
      "details": {
        "setup": "You are a database administrator responsible for server maintenance. You need to track the weekly growth of the main production database, 'prod_analytics', to forecast future storage requirements and prevent the server from running out of disk space.",
        "goal": "To get the current, total size of the 'prod_analytics' database in a simple, readable format to log in your weekly report.",
        "outcome": "The query returns a single, easy-to-read string, such as '152 GB', which is recorded for trend analysis."
      },
      "example": {
        "query": "SELECT pg_size_pretty(pg_database_size('prod_analytics'));",
        "explanation": "This query first calls `pg_database_size` to calculate the total disk space used by the 'prod_analytics' database in bytes. The result is then passed to `pg_size_pretty`, which formats the large byte value into a clear and understandable string for reporting purposes."
      },
      "notes": [
        "You can run this query while connected to any database on the same server, not just the one you are measuring.",
        "For a complete overview, you can get the sizes of all databases at once with: `SELECT datname, pg_size_pretty(pg_database_size(datname)) AS size FROM pg_database ORDER BY size DESC;`"
      ]
    }
  ]
};