export const command12 = {
  // --- Identifier for your application logic ---
  "type": "query",

  "path": [
    "Performance Monitoring and Maintenance",
    "Checking Object Sizes",
    "Show Table Size"
  ],
  "description": "Calculates and displays the disk space used by a specific table in a human-readable format (e.g., '15 GB'). This size excludes any indexes associated with the table.",

  // --- Query-specific fields ---
  "query_template": "SELECT pg_size_pretty(pg_relation_size('[table_name]'));",
  "components": [
    {
      "component": "pg_relation_size('[table_name]')",
      "name": "Get Relation Size",
      "description": "A built-in PostgreSQL function that returns the size, in bytes, of the main data fork of a table or index."
    },
    {
      "component": "pg_size_pretty(...)",
      "name": "Format Size",
      "description": "A formatting function that converts a size in bytes (from `pg_relation_size`) into a human-readable string with units like KB, MB, GB, or TB."
    }
  ],

  // --- Scenarios ---
  "scenarios": [
    {
      "title": "Investigating Bloat in a High-Traffic Table",
      "details": {
        "setup": "You are a database administrator monitoring a production database. You suspect that the `user_sessions` table is consuming a large amount of disk space due to frequent updates and deletions, which can cause bloat.",
        "goal": "To quickly check the current on-disk size of the `user_sessions` table's primary data to confirm your suspicion before deciding to run maintenance tasks like `VACUUM FULL`.",
        "outcome": "The query returns a clear, formatted size (e.g., '22 GB'), giving you a precise measure of the table's footprint and helping you decide on the next steps for optimization."
      },
      "example": {
        "query": "SELECT pg_size_pretty(pg_relation_size('user_sessions'));",
        "explanation": "This query provides a direct and easy-to-read assessment of the table's data size. By focusing only on the table and not its indexes, you can isolate where the storage is being used most heavily."
      },
      "notes": [
        "This query only shows the size of the table data itself. To get the total size, including all indexes and TOAST data, use the `pg_total_relation_size` function instead.",
        "To see the sizes of all your tables at once, you can query the `pg_tables` system catalog, but this function is excellent for targeted checks."
      ]
    }
  ]
};