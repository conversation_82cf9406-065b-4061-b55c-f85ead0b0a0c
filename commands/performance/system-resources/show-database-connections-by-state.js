export const command20 = {
  "type": "query",
  "path": [
    "Performance Monitoring and Maintenance",
    "System Resources",
    "Show Database Connections by State"
  ],
  "description": "Provides a summary of all current database connections, grouped by their state. This is a crucial first step for diagnosing connection-related issues like pool exhaustion or stuck transactions.",
  "query_template": "SELECT state, count(*) FROM pg_stat_activity GROUP BY state;",
  "components": [
    {
      "component": "SELECT state, count(*)",
      "name": "Aggregation",
      "description": "Counts the number of connections and groups them by the `state` column."
    },
    {
      "component": "FROM pg_stat_activity",
      "name": "Statistics View",
      "description": "Queries the `pg_stat_activity` view, which provides a real-time snapshot of all active backend processes and their current status."
    }
  ],
  "scenarios": [
    {
      "title": "Investigating 'Too Many Connections' Errors",
      "details": {
        "setup": "A web application is intermittently failing with 'FATAL: sorry, too many clients already' errors, even though the `max_connections` limit seems adequate. You suspect the application's connection pool is not releasing connections properly.",
        "goal": "To get a quick overview of how the connection slots are being used and identify if connections are getting stuck in a particular state.",
        "outcome": "The query returns a list of states and the number of connections in each. This allows you to see if there is an abnormally high count of 'idle', 'active', or 'idle in transaction' connections."
      },
      "example": {
        "query": "SELECT state, count(*) FROM pg_stat_activity GROUP BY state;",
        "explanation": "This query aggregates all connections by their current state. A large number of 'idle in transaction' connections is a common red flag, indicating that the application is beginning transactions but not closing them with a COMMIT or ROLLBACK. A high 'idle' count might suggest the connection pool is oversized."
      },
      "notes": [
        "The 'idle in transaction' state is particularly problematic as these connections hold locks and can prevent VACUUM from cleaning up dead rows.",
        "If you see many 'idle in transaction' connections, you can run `SELECT pid, client_addr, state, query_start, query FROM pg_stat_activity WHERE state = 'idle in transaction';` to get more details on the specific backends.",
        "This query is very lightweight and safe to run on a busy production server."
      ]
    }
  ]
};