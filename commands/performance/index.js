import { command0 } from './monitoring-server-activity/show-active-connections.js';
import { command1 } from './monitoring-server-activity/terminate-a-specific-connection.js';
import { command2 } from './analyzing-query-performance/show-execution-plan.js';
import { command3 } from './analyzing-query-performance/execute-and-analyze-query.js';
import { command4 } from './database-maintenance/vacuum/standard-vacuum.js';
import { command5 } from './database-maintenance/vacuum/verbose-vacuum.js';
import { command6 } from './database-maintenance/vacuum/full-vacuum.js';
import { command7 } from './database-maintenance/update-planner-statistics.js';
import { command8 } from './database-maintenance/vacuum-and-analyze.js';
import { command9 } from './database-maintenance/reindex/reindex-a-specific-index.js';
import { command10 } from './database-maintenance/reindex/reindex-all-indexes-on-a-table.js';
import { command11 } from './checking-object-sizes/show-database-size.js';
import { command12 } from './checking-object-sizes/show-table-size.js';
import { command13 } from './checking-object-sizes/show-total-table-size.js';
import { command14 } from './advanced-monitoring/show-blocking-queries.js';
import { command15 } from './advanced-monitoring/show-long-running-queries.js';
import { command16 } from './advanced-monitoring/show-table-statistics.js';
import { command17 } from './advanced-monitoring/show-index-usage-statistics.js';
import { command18 } from './advanced-monitoring/show-cache-hit-ratios.js';
import { command19 } from './query-analysis/show-most-expensive-queries.js';
import { command20 } from './system-resources/show-database-connections-by-state.js';

export const performanceCommands = [
  command0,
  command1,
  command2,
  command3,
  command4,
  command5,
  command6,
  command7,
  command8,
  command9,
  command10,
  command11,
  command12,
  command13,
  command14,
  command15,
  command16,
  command17,
  command18,
  command19,
  command20
];

export function validatePerformanceCommands() {
  return performanceCommands.every(cmd => {
    const hasPath = cmd.path && cmd.path.length > 0 && cmd.path[0] === "Performance Monitoring and Maintenance";
    const hasDescription = cmd.description;
    const hasType = cmd.type;

    if (!hasPath || !hasDescription || !hasType) {
      return false;
    }

    switch (cmd.type) {
      case 'command':
        return cmd.command;
      case 'query':
        return cmd.query_template && cmd.components;
      case 'config':
        return cmd.config_template && cmd.directives;
      default:
        return false;
    }
  });
}
