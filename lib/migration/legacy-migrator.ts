/**
 * Migration utility for converting legacy JavaScript command definitions
 * to the new TypeScript structure with enhanced scenarios.
 * 
 * This utility handles the transformation of existing data while preserving
 * all valuable information and adding the new enhanced structure.
 */

import {
  Content,
  ContentType,
  DifficultyLevel,
  EnvironmentType,
  ScenarioStepType
} from '../../types/core';
import {
  LegacyCommand,
  MigrationResult,
  MigrationSummary,
  ValidationError
} from '../../types/validation';
import {
  createQueryBuilder,
  createCommandBuilder,
  createConfigBuilder
} from '../builders/content-builder';
import {
  createScenarioBuilder,
  createScenarioStepBuilder,
  createSetupStep,
  createExecutionStep,
  createValidationStep
} from '../builders/scenario-builder';
import { ContentValidator } from '../validation/validator';

// ============================================================================
// MIGRATION UTILITIES
// ============================================================================

export class LegacyMigrator {
  private validator = ContentValidator.getInstance();

  // ============================================================================
  // MAIN MIGRATION METHODS
  // ============================================================================

  public migrateLegacyCommand(legacyData: LegacyCommand): MigrationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    try {
      // Determine content type
      const contentType = this.determineContentType(legacyData);
      
      // Create appropriate builder
      const builder = this.createBuilderForType(contentType);
      
      // Migrate basic information
      this.migrateBasicInfo(builder, legacyData, errors, warnings);
      
      // Migrate type-specific content
      this.migrateTypeSpecificContent(builder, legacyData, contentType, errors, warnings);
      
      // Migrate scenarios
      this.migrateScenarios(builder, legacyData, errors, warnings);
      
      // Build the content
      const migratedContent = builder.build();
      
      return {
        success: true,
        migratedContent,
        errors,
        warnings,
        originalData: legacyData
      };
      
    } catch (error) {
      errors.push({
        field: 'migration',
        message: `Migration failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        code: 'MIGRATION_FAILED',
        severity: 'error'
      });
      
      return {
        success: false,
        errors,
        warnings,
        originalData: legacyData
      };
    }
  }

  public migrateBatch(legacyCommands: LegacyCommand[]): MigrationSummary {
    const results = legacyCommands.map(cmd => this.migrateLegacyCommand(cmd));
    
    const successful = results.filter(r => r.success);
    const failed = results.filter(r => !r.success);
    
    const allErrors = results.flatMap(r => r.errors);
    const totalWarnings = results.reduce((sum, r) => sum + r.warnings.length, 0);
    
    return {
      totalFiles: legacyCommands.length,
      successfulMigrations: successful.length,
      failedMigrations: failed.length,
      warnings: totalWarnings,
      errors: allErrors,
      migratedContent: successful.map(r => r.migratedContent!).filter(Boolean)
    };
  }

  // ============================================================================
  // CONTENT TYPE DETERMINATION
  // ============================================================================

  private determineContentType(legacyData: LegacyCommand): ContentType {
    // Check explicit type first
    if (legacyData.type) {
      switch (legacyData.type.toLowerCase()) {
        case 'query':
          return ContentType.QUERY;
        case 'command':
          return ContentType.COMMAND;
        case 'config':
          return ContentType.CONFIG;
      }
    }

    // Infer from content
    if (legacyData.query_template) {
      return ContentType.QUERY;
    }
    
    if (legacyData.command) {
      return ContentType.COMMAND;
    }
    
    // Default to command if unclear
    return ContentType.COMMAND;
  }

  private createBuilderForType(type: ContentType) {
    switch (type) {
      case ContentType.QUERY:
        return createQueryBuilder();
      case ContentType.COMMAND:
        return createCommandBuilder();
      case ContentType.CONFIG:
        return createConfigBuilder();
      default:
        throw new Error(`Unknown content type: ${type}`);
    }
  }

  // ============================================================================
  // BASIC INFO MIGRATION
  // ============================================================================

  private migrateBasicInfo(builder: any, legacyData: LegacyCommand, errors: ValidationError[], warnings: ValidationError[]): void {
    // Generate ID from path
    const id = this.generateIdFromPath(legacyData.path);
    builder.setId(id);

    // Set path hierarchy
    if (legacyData.path && legacyData.path.length >= 2) {
      const category = legacyData.path[0];
      const subcategory = legacyData.path.length > 2 ? legacyData.path[1] : undefined;
      const topic = legacyData.path[legacyData.path.length - 1];
      
      builder.setPath(category, subcategory, topic);
      builder.setTitle(topic);
    } else {
      errors.push({
        field: 'path',
        message: 'Invalid path structure in legacy data',
        code: 'INVALID_PATH',
        severity: 'error'
      });
    }

    // Set description
    if (legacyData.description) {
      builder.setDescription(legacyData.description);
    } else {
      errors.push({
        field: 'description',
        message: 'Missing description in legacy data',
        code: 'MISSING_DESCRIPTION',
        severity: 'error'
      });
    }

    // Set default difficulty (can be enhanced later)
    builder.setDifficulty(DifficultyLevel.INTERMEDIATE);

    // Set default PostgreSQL versions
    builder.setPostgresVersions(['12', '13', '14', '15', '16']);

    // Add basic learning objectives
    builder.addLearningObjective(`Understand how to use ${legacyData.path?.[legacyData.path.length - 1] || 'this feature'}`);
    builder.addLearningObjective('Apply the knowledge in real-world scenarios');
  }

  // ============================================================================
  // TYPE-SPECIFIC CONTENT MIGRATION
  // ============================================================================

  private migrateTypeSpecificContent(builder: any, legacyData: LegacyCommand, type: ContentType, errors: ValidationError[], warnings: ValidationError[]): void {
    switch (type) {
      case ContentType.QUERY:
        this.migrateQueryContent(builder, legacyData, errors, warnings);
        break;
      case ContentType.COMMAND:
        this.migrateCommandContent(builder, legacyData, errors, warnings);
        break;
      case ContentType.CONFIG:
        this.migrateConfigContent(builder, legacyData, errors, warnings);
        break;
    }
  }

  private migrateQueryContent(builder: any, legacyData: LegacyCommand, errors: ValidationError[], warnings: ValidationError[]): void {
    if (legacyData.query_template) {
      builder.setQueryTemplate(legacyData.query_template);
    }

    // Migrate components
    if (legacyData.components) {
      legacyData.components.forEach(comp => {
        builder.addComponent({
          component: comp.component,
          name: comp.name,
          description: comp.description,
          isOptional: false,
          examples: [],
          relatedConcepts: []
        });
      });
    }

    // Add examples from scenarios
    if (legacyData.scenarios) {
      legacyData.scenarios.forEach(scenario => {
        if (scenario.example?.query) {
          builder.addExample(
            scenario.example.query,
            scenario.example.explanation,
            undefined // expectedResult not available in legacy
          );
        }
      });
    }
  }

  private migrateCommandContent(builder: any, legacyData: LegacyCommand, errors: ValidationError[], warnings: ValidationError[]): void {
    if (legacyData.command) {
      builder.setCommandTemplate(legacyData.command);
    }

    // Migrate parameters
    if (legacyData.parameters) {
      legacyData.parameters.forEach(param => {
        builder.addParameter({
          flag: param.flag,
          name: param.name,
          description: param.description,
          isRequired: false, // Default to optional
          defaultValue: undefined,
          examples: [],
          validationPattern: undefined
        });
      });
    }

    // Add examples from scenarios
    if (legacyData.scenarios) {
      legacyData.scenarios.forEach(scenario => {
        if (scenario.example?.command) {
          builder.addExample(
            scenario.example.command,
            scenario.example.explanation,
            undefined // expectedOutput not available in legacy
          );
        }
      });
    }
  }

  private migrateConfigContent(builder: any, legacyData: LegacyCommand, errors: ValidationError[], warnings: ValidationError[]): void {
    // For config content, we'll need to infer the template
    if (legacyData.command) {
      builder.setConfigTemplate(legacyData.command);
    }

    // Migrate parameters as config parameters
    if (legacyData.parameters) {
      legacyData.parameters.forEach(param => {
        builder.addParameter({
          flag: param.flag,
          name: param.name,
          description: param.description,
          isRequired: false,
          defaultValue: undefined,
          examples: [],
          validationPattern: undefined
        });
      });
    }
  }

  // ============================================================================
  // SCENARIO MIGRATION
  // ============================================================================

  private migrateScenarios(builder: any, legacyData: LegacyCommand, errors: ValidationError[], warnings: ValidationError[]): void {
    if (!legacyData.scenarios || legacyData.scenarios.length === 0) {
      warnings.push({
        field: 'scenarios',
        message: 'No scenarios found in legacy data. Creating default scenario.',
        code: 'NO_SCENARIOS',
        severity: 'warning'
      });
      
      // Create a basic default scenario
      const defaultScenario = this.createDefaultScenario(legacyData);
      builder.addScenario(defaultScenario);
      return;
    }

    // Migrate existing scenarios
    legacyData.scenarios.forEach((legacyScenario, index) => {
      try {
        const enhancedScenario = this.migrateLegacyScenario(legacyScenario, legacyData, index);
        builder.addScenario(enhancedScenario);
      } catch (error) {
        errors.push({
          field: `scenarios[${index}]`,
          message: `Failed to migrate scenario: ${error instanceof Error ? error.message : 'Unknown error'}`,
          code: 'SCENARIO_MIGRATION_FAILED',
          severity: 'error'
        });
      }
    });
  }

  private migrateLegacyScenario(legacyScenario: any, legacyData: LegacyCommand, index: number) {
    const scenarioBuilder = createScenarioBuilder();
    
    // Basic info
    const id = `${this.generateIdFromPath(legacyData.path)}-scenario-${index + 1}`;
    scenarioBuilder
      .setId(id)
      .setTitle(legacyScenario.title || `Scenario ${index + 1}`)
      .setDescription(legacyScenario.details?.setup || 'Legacy scenario migrated from JavaScript definition')
      .setDifficulty(DifficultyLevel.INTERMEDIATE)
      .setEstimatedDuration(15); // Default 15 minutes

    // Learning objectives
    if (legacyScenario.details?.goal) {
      scenarioBuilder.addLearningObjective(
        legacyScenario.details.goal,
        DifficultyLevel.INTERMEDIATE,
        [],
        [legacyScenario.details?.outcome || 'Complete the scenario successfully']
      );
    }

    // Context and value
    scenarioBuilder
      .setRealWorldContext(legacyScenario.details?.setup || 'Real-world application scenario')
      .setBusinessValue('Practical application of PostgreSQL/PostGIS knowledge');

    // Environment
    scenarioBuilder.setEnvironment(EnvironmentType.LOCAL, {
      postgresVersion: '15',
      postgisVersion: '3.3'
    });

    // Initial setup
    scenarioBuilder.setInitialSetup(
      'Prepare the environment for this scenario',
      ['-- Initial setup commands will be added here'],
      'Environment is ready for the scenario'
    );

    // Create steps from legacy scenario
    this.createStepsFromLegacyScenario(scenarioBuilder, legacyScenario, legacyData);

    // Success criteria
    if (legacyScenario.details?.outcome) {
      scenarioBuilder.addSuccessCriteria(legacyScenario.details.outcome);
    }

    return scenarioBuilder.build();
  }

  private createStepsFromLegacyScenario(scenarioBuilder: any, legacyScenario: any, legacyData: LegacyCommand): void {
    // Setup step
    const setupStep = createSetupStep(
      'setup',
      'Environment Setup',
      legacyScenario.details?.setup || 'Set up the environment for this scenario',
      ['Follow the setup instructions to prepare your environment']
    );
    scenarioBuilder.addStep(setupStep);

    // Execution step
    if (legacyScenario.example?.command || legacyScenario.example?.query) {
      const command = legacyScenario.example.command || legacyScenario.example.query;
      const executionStep = createExecutionStep(
        'execution',
        'Execute the Command/Query',
        'Run the main command or query for this scenario',
        [command],
        'Expected output as described in the scenario'
      );
      scenarioBuilder.addStep(executionStep);
    }

    // Validation step
    if (legacyScenario.details?.outcome) {
      const validationStep = createValidationStep(
        'validation',
        'Validate Results',
        'Verify that the scenario completed successfully',
        [legacyScenario.details.outcome]
      );
      scenarioBuilder.addStep(validationStep);
    }
  }

  private createDefaultScenario(legacyData: LegacyCommand) {
    const scenarioBuilder = createScenarioBuilder();
    
    const id = `${this.generateIdFromPath(legacyData.path)}-default-scenario`;
    const title = `Using ${legacyData.path?.[legacyData.path.length - 1] || 'this feature'}`;
    
    return scenarioBuilder
      .setId(id)
      .setTitle(title)
      .setDescription('A basic scenario demonstrating the usage of this feature')
      .setDifficulty(DifficultyLevel.BEGINNER)
      .setEstimatedDuration(10)
      .addLearningObjective('Understand the basic usage', DifficultyLevel.BEGINNER, [], ['Successfully execute the command/query'])
      .setRealWorldContext('Basic usage scenario')
      .setBusinessValue('Foundation knowledge for PostgreSQL/PostGIS')
      .setEnvironment(EnvironmentType.LOCAL)
      .setInitialSetup('Basic setup', ['-- Setup commands'], 'Ready to proceed')
      .addStep(createSetupStep('setup', 'Setup', 'Basic setup', ['Prepare your environment']))
      .addSuccessCriteria('Command/query executes successfully')
      .build();
  }

  // ============================================================================
  // UTILITY METHODS
  // ============================================================================

  private generateIdFromPath(path: string[]): string {
    if (!path || path.length === 0) {
      return 'unknown-command';
    }
    
    return path
      .map(segment => segment.toLowerCase().replace(/[^a-z0-9]/g, '-'))
      .join('-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }
}
