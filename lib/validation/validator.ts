/**
 * Validation engine for the PostgreSQL/PostGIS Learning Platform
 * 
 * This file contains the main validation logic that uses the schemas
 * to validate content, scenarios, and other data structures.
 */

import {
  Content,
  QueryContent,
  CommandContent,
  ConfigContent,
  EnhancedScenario,
  ScenarioStep,
  ContentType
} from '../../types/core';
import {
  ValidationError,
  ValidationResult,
  ContentValidationResult,
  ScenarioValidationResult,
  StepValidationResult,
  SchemaValidationRule
} from '../../types/validation';
import {
  ValidationUtils,
  CONTENT_SCHEMAS,
  SCENARIO_RULES,
  SCENARIO_STEP_RULES
} from './schemas';

// ============================================================================
// MAIN VALIDATOR CLASS
// ============================================================================

export class ContentValidator {
  private static instance: ContentValidator;

  public static getInstance(): ContentValidator {
    if (!ContentValidator.instance) {
      ContentValidator.instance = new ContentValidator();
    }
    return ContentValidator.instance;
  }

  // ============================================================================
  // CONTENT VALIDATION
  // ============================================================================

  public validateContent(content: Content): ContentValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Validate base content structure
    const baseValidation = this.validateAgainstRules(content, CONTENT_SCHEMAS.baseRules);
    errors.push(...baseValidation.errors);
    warnings.push(...baseValidation.warnings);

    // Validate type-specific rules
    switch (content.type) {
      case ContentType.QUERY:
        const queryValidation = this.validateQueryContent(content as QueryContent);
        errors.push(...queryValidation.errors);
        warnings.push(...queryValidation.warnings);
        break;
      
      case ContentType.COMMAND:
        const commandValidation = this.validateCommandContent(content as CommandContent);
        errors.push(...commandValidation.errors);
        warnings.push(...commandValidation.warnings);
        break;
      
      case ContentType.CONFIG:
        const configValidation = this.validateConfigContent(content as ConfigContent);
        errors.push(...configValidation.errors);
        warnings.push(...configValidation.warnings);
        break;
    }

    // Validate scenarios
    const scenarioValidations: ScenarioValidationResult[] = [];
    for (const scenario of content.scenarios) {
      const scenarioValidation = this.validateScenario(scenario);
      scenarioValidations.push(scenarioValidation);
      errors.push(...scenarioValidation.errors);
      warnings.push(...scenarioValidation.warnings);
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      contentId: content.id,
      contentType: content.type,
      scenarioValidations
    };
  }

  private validateQueryContent(content: QueryContent): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const queryValidation = this.validateAgainstRules(content, CONTENT_SCHEMAS.queryRules || []);
    errors.push(...queryValidation.errors);
    warnings.push(...queryValidation.warnings);

    // Additional query-specific validations
    if (content.components) {
      for (let i = 0; i < content.components.length; i++) {
        const component = content.components[i];
        if (!component.component || !component.name || !component.description) {
          errors.push(ValidationUtils.createError(
            `components[${i}]`,
            'Component must have component, name, and description fields',
            'INCOMPLETE_COMPONENT'
          ));
        }
      }
    }

    if (content.examples) {
      for (let i = 0; i < content.examples.length; i++) {
        const example = content.examples[i];
        if (!example.query || !example.explanation) {
          errors.push(ValidationUtils.createError(
            `examples[${i}]`,
            'Example must have query and explanation fields',
            'INCOMPLETE_EXAMPLE'
          ));
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private validateCommandContent(content: CommandContent): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const commandValidation = this.validateAgainstRules(content, CONTENT_SCHEMAS.commandRules || []);
    errors.push(...commandValidation.errors);
    warnings.push(...commandValidation.warnings);

    // Additional command-specific validations
    if (content.parameters) {
      for (let i = 0; i < content.parameters.length; i++) {
        const param = content.parameters[i];
        if (!param.flag || !param.name || !param.description) {
          errors.push(ValidationUtils.createError(
            `parameters[${i}]`,
            'Parameter must have flag, name, and description fields',
            'INCOMPLETE_PARAMETER'
          ));
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private validateConfigContent(content: ConfigContent): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const configValidation = this.validateAgainstRules(content, CONTENT_SCHEMAS.configRules || []);
    errors.push(...configValidation.errors);
    warnings.push(...configValidation.warnings);

    return { isValid: errors.length === 0, errors, warnings };
  }

  // ============================================================================
  // SCENARIO VALIDATION
  // ============================================================================

  public validateScenario(scenario: EnhancedScenario): ScenarioValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    // Validate scenario structure
    const scenarioValidation = this.validateAgainstRules(scenario, SCENARIO_RULES);
    errors.push(...scenarioValidation.errors);
    warnings.push(...scenarioValidation.warnings);

    // Validate steps
    const stepValidations: StepValidationResult[] = [];
    if (scenario.steps) {
      for (const step of scenario.steps) {
        const stepValidation = this.validateScenarioStep(step);
        stepValidations.push(stepValidation);
        errors.push(...stepValidation.errors);
        warnings.push(...stepValidation.warnings);
      }
    }

    // Additional scenario-specific validations
    this.validateScenarioLogic(scenario, errors, warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      scenarioId: scenario.id,
      stepValidations
    };
  }

  public validateScenarioStep(step: ScenarioStep): StepValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    const stepValidation = this.validateAgainstRules(step, SCENARIO_STEP_RULES);
    errors.push(...stepValidation.errors);
    warnings.push(...stepValidation.warnings);

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      stepId: step.id,
      stepType: step.type
    };
  }

  private validateScenarioLogic(scenario: EnhancedScenario, errors: ValidationError[], warnings: ValidationError[]): void {
    // Check for logical step sequence
    if (scenario.steps && scenario.steps.length > 0) {
      const stepTypes = scenario.steps.map(step => step.type);
      
      // Warn if no cleanup step is present
      if (!stepTypes.includes('cleanup' as any)) {
        warnings.push(ValidationUtils.createError(
          'steps',
          'Consider adding a cleanup step to ensure proper resource management',
          'MISSING_CLEANUP',
          'warning'
        ));
      }

      // Check for duplicate step IDs
      const stepIds = scenario.steps.map(step => step.id);
      const duplicateIds = stepIds.filter((id, index) => stepIds.indexOf(id) !== index);
      if (duplicateIds.length > 0) {
        errors.push(ValidationUtils.createError(
          'steps',
          `Duplicate step IDs found: ${duplicateIds.join(', ')}`,
          'DUPLICATE_STEP_IDS'
        ));
      }
    }

    // Validate learning objectives
    if (scenario.learningObjectives && scenario.learningObjectives.length > 0) {
      for (let i = 0; i < scenario.learningObjectives.length; i++) {
        const objective = scenario.learningObjectives[i];
        if (!objective.description || !objective.outcomes || objective.outcomes.length === 0) {
          errors.push(ValidationUtils.createError(
            `learningObjectives[${i}]`,
            'Learning objective must have description and at least one outcome',
            'INCOMPLETE_OBJECTIVE'
          ));
        }
      }
    }
  }

  // ============================================================================
  // GENERIC RULE VALIDATION
  // ============================================================================

  private validateAgainstRules(data: any, rules: SchemaValidationRule[]): ValidationResult {
    const errors: ValidationError[] = [];
    const warnings: ValidationError[] = [];

    for (const rule of rules) {
      const value = this.getNestedValue(data, rule.field);

      // Check required fields
      if (rule.required) {
        const requiredError = ValidationUtils.validateRequired(value, rule.field);
        if (requiredError) {
          errors.push(requiredError);
          continue; // Skip other validations if required field is missing
        }
      }

      // Skip validation if value is undefined/null and not required
      if (value === undefined || value === null) {
        continue;
      }

      // Type validation
      switch (rule.type) {
        case 'string':
          const stringError = ValidationUtils.validateString(value, rule.field, rule.minLength, rule.maxLength);
          if (stringError) errors.push(stringError);
          break;

        case 'array':
          const arrayError = ValidationUtils.validateArray(value, rule.field);
          if (arrayError) errors.push(arrayError);
          break;

        case 'enum':
          if (rule.enumValues && !rule.enumValues.includes(value)) {
            errors.push(ValidationUtils.createError(
              rule.field,
              `${rule.field} must be one of: ${rule.enumValues.join(', ')}`,
              'INVALID_ENUM_VALUE'
            ));
          }
          break;

        case 'number':
          if (typeof value !== 'number' || isNaN(value)) {
            errors.push(ValidationUtils.createError(
              rule.field,
              `${rule.field} must be a valid number`,
              'INVALID_NUMBER'
            ));
          }
          break;
      }

      // Pattern validation
      if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
        errors.push(ValidationUtils.createError(
          rule.field,
          `${rule.field} does not match the required pattern`,
          'PATTERN_MISMATCH'
        ));
      }

      // Custom validation
      if (rule.customValidator) {
        const customError = rule.customValidator(value);
        if (customError) {
          if (customError.severity === 'warning') {
            warnings.push(customError);
          } else {
            errors.push(customError);
          }
        }
      }
    }

    return { isValid: errors.length === 0, errors, warnings };
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => {
      return current && current[key] !== undefined ? current[key] : undefined;
    }, obj);
  }

  // ============================================================================
  // BATCH VALIDATION
  // ============================================================================

  public validateMultipleContents(contents: Content[]): ContentValidationResult[] {
    return contents.map(content => this.validateContent(content));
  }

  public validateContentCollection(contents: Content[]): ValidationResult {
    const allResults = this.validateMultipleContents(contents);
    const allErrors = allResults.flatMap(result => result.errors);
    const allWarnings = allResults.flatMap(result => result.warnings);

    // Check for duplicate content IDs
    const contentIds = contents.map(content => content.id);
    const duplicateIds = contentIds.filter((id, index) => contentIds.indexOf(id) !== index);
    if (duplicateIds.length > 0) {
      allErrors.push(ValidationUtils.createError(
        'collection',
        `Duplicate content IDs found: ${duplicateIds.join(', ')}`,
        'DUPLICATE_CONTENT_IDS'
      ));
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors,
      warnings: allWarnings
    };
  }
}
