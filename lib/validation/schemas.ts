/**
 * Schema validation definitions for the PostgreSQL/PostGIS Learning Platform
 * 
 * This file contains the validation schemas and rules for ensuring data integrity
 * across all content types, scenarios, and user interactions.
 */

import {
  ContentType,
  DifficultyLevel,
  ScenarioStepType,
  EnvironmentType,
  Content,
  EnhancedScenario,
  ScenarioStep
} from '../../types/core';
import {
  ValidationError,
  ValidationResult,
  SchemaValidationRule,
  ContentSchema
} from '../../types/validation';

// ============================================================================
// VALIDATION UTILITIES
// ============================================================================

export class ValidationUtils {
  static createError(field: string, message: string, code: string, severity: 'error' | 'warning' | 'info' = 'error'): ValidationError {
    return { field, message, code, severity };
  }

  static isValidEnum<T>(value: any, enumObject: Record<string, T>): boolean {
    return Object.values(enumObject).includes(value);
  }

  static isValidUrl(url: string): boolean {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }

  static isValidSemVer(version: string): boolean {
    const semVerRegex = /^(\d+)\.(\d+)\.(\d+)(?:-([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?(?:\+([0-9A-Za-z-]+(?:\.[0-9A-Za-z-]+)*))?$/;
    return semVerRegex.test(version);
  }

  static isValidPostgresVersion(version: string): boolean {
    // PostgreSQL version format: major.minor or just major (for v10+)
    const postgresVersionRegex = /^(\d{1,2})(?:\.(\d+))?$/;
    return postgresVersionRegex.test(version);
  }

  static validateRequired(value: any, field: string): ValidationError | null {
    if (value === undefined || value === null || value === '') {
      return this.createError(field, `${field} is required`, 'REQUIRED_FIELD');
    }
    return null;
  }

  static validateString(value: any, field: string, minLength?: number, maxLength?: number): ValidationError | null {
    if (typeof value !== 'string') {
      return this.createError(field, `${field} must be a string`, 'INVALID_TYPE');
    }
    
    if (minLength && value.length < minLength) {
      return this.createError(field, `${field} must be at least ${minLength} characters`, 'MIN_LENGTH');
    }
    
    if (maxLength && value.length > maxLength) {
      return this.createError(field, `${field} must be at most ${maxLength} characters`, 'MAX_LENGTH');
    }
    
    return null;
  }

  static validateArray(value: any, field: string, minItems?: number, maxItems?: number): ValidationError | null {
    if (!Array.isArray(value)) {
      return this.createError(field, `${field} must be an array`, 'INVALID_TYPE');
    }
    
    if (minItems && value.length < minItems) {
      return this.createError(field, `${field} must have at least ${minItems} items`, 'MIN_ITEMS');
    }
    
    if (maxItems && value.length > maxItems) {
      return this.createError(field, `${field} must have at most ${maxItems} items`, 'MAX_ITEMS');
    }
    
    return null;
  }
}

// ============================================================================
// CONTENT VALIDATION SCHEMAS
// ============================================================================

export const BASE_CONTENT_RULES: SchemaValidationRule[] = [
  {
    field: 'id',
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9_-]+$/
  },
  {
    field: 'type',
    required: true,
    type: 'enum',
    enumValues: Object.values(ContentType)
  },
  {
    field: 'path.category',
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  {
    field: 'path.topic',
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  {
    field: 'title',
    required: true,
    type: 'string',
    minLength: 5,
    maxLength: 200
  },
  {
    field: 'description',
    required: true,
    type: 'string',
    minLength: 20,
    maxLength: 1000
  },
  {
    field: 'difficulty',
    required: true,
    type: 'enum',
    enumValues: Object.values(DifficultyLevel)
  },
  {
    field: 'scenarios',
    required: true,
    type: 'array',
    customValidator: (scenarios: EnhancedScenario[]) => {
      if (!Array.isArray(scenarios) || scenarios.length === 0) {
        return ValidationUtils.createError('scenarios', 'At least one scenario is required', 'MIN_SCENARIOS');
      }
      return null;
    }
  },
  {
    field: 'learningObjectives',
    required: true,
    type: 'array',
    customValidator: (objectives: string[]) => {
      if (!Array.isArray(objectives) || objectives.length === 0) {
        return ValidationUtils.createError('learningObjectives', 'At least one learning objective is required', 'MIN_OBJECTIVES');
      }
      return null;
    }
  },
  {
    field: 'postgresVersions',
    required: true,
    type: 'array',
    customValidator: (versions: string[]) => {
      if (!Array.isArray(versions) || versions.length === 0) {
        return ValidationUtils.createError('postgresVersions', 'At least one PostgreSQL version is required', 'MIN_VERSIONS');
      }
      
      for (const version of versions) {
        if (!ValidationUtils.isValidPostgresVersion(version)) {
          return ValidationUtils.createError('postgresVersions', `Invalid PostgreSQL version format: ${version}`, 'INVALID_VERSION');
        }
      }
      
      return null;
    }
  }
];

export const QUERY_CONTENT_RULES: SchemaValidationRule[] = [
  {
    field: 'queryTemplate',
    required: true,
    type: 'string',
    minLength: 10,
    maxLength: 5000,
    customValidator: (template: string) => {
      if (!template.toUpperCase().includes('SELECT') && 
          !template.toUpperCase().includes('INSERT') && 
          !template.toUpperCase().includes('UPDATE') && 
          !template.toUpperCase().includes('DELETE') &&
          !template.toUpperCase().includes('CREATE') &&
          !template.toUpperCase().includes('ALTER') &&
          !template.toUpperCase().includes('DROP')) {
        return ValidationUtils.createError('queryTemplate', 'Query template must contain a valid SQL statement', 'INVALID_SQL');
      }
      return null;
    }
  },
  {
    field: 'components',
    required: true,
    type: 'array',
    customValidator: (components: any[]) => {
      if (!Array.isArray(components) || components.length === 0) {
        return ValidationUtils.createError('components', 'At least one component is required for queries', 'MIN_COMPONENTS');
      }
      return null;
    }
  }
];

export const COMMAND_CONTENT_RULES: SchemaValidationRule[] = [
  {
    field: 'commandTemplate',
    required: true,
    type: 'string',
    minLength: 3,
    maxLength: 1000,
    customValidator: (template: string) => {
      // Basic validation for command structure
      if (!template.trim()) {
        return ValidationUtils.createError('commandTemplate', 'Command template cannot be empty', 'EMPTY_COMMAND');
      }
      return null;
    }
  },
  {
    field: 'parameters',
    required: true,
    type: 'array',
    customValidator: (parameters: any[]) => {
      if (!Array.isArray(parameters)) {
        return ValidationUtils.createError('parameters', 'Parameters must be an array', 'INVALID_TYPE');
      }
      return null;
    }
  }
];

export const CONFIG_CONTENT_RULES: SchemaValidationRule[] = [
  {
    field: 'configTemplate',
    required: true,
    type: 'string',
    minLength: 5,
    maxLength: 2000
  },
  {
    field: 'parameters',
    required: true,
    type: 'array'
  }
];

// ============================================================================
// SCENARIO VALIDATION SCHEMAS
// ============================================================================

export const SCENARIO_RULES: SchemaValidationRule[] = [
  {
    field: 'id',
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100,
    pattern: /^[a-zA-Z0-9_-]+$/
  },
  {
    field: 'title',
    required: true,
    type: 'string',
    minLength: 10,
    maxLength: 200
  },
  {
    field: 'description',
    required: true,
    type: 'string',
    minLength: 50,
    maxLength: 1000
  },
  {
    field: 'difficulty',
    required: true,
    type: 'enum',
    enumValues: Object.values(DifficultyLevel)
  },
  {
    field: 'estimatedDuration',
    required: true,
    type: 'number',
    customValidator: (duration: number) => {
      if (duration < 1 || duration > 480) { // 1 minute to 8 hours
        return ValidationUtils.createError('estimatedDuration', 'Duration must be between 1 and 480 minutes', 'INVALID_DURATION');
      }
      return null;
    }
  },
  {
    field: 'realWorldContext',
    required: true,
    type: 'string',
    minLength: 50,
    maxLength: 500
  },
  {
    field: 'businessValue',
    required: true,
    type: 'string',
    minLength: 30,
    maxLength: 300
  },
  {
    field: 'steps',
    required: true,
    type: 'array',
    customValidator: (steps: ScenarioStep[]) => {
      if (!Array.isArray(steps) || steps.length === 0) {
        return ValidationUtils.createError('steps', 'At least one step is required', 'MIN_STEPS');
      }
      
      // Validate step sequence
      const hasSetup = steps.some(step => step.type === ScenarioStepType.SETUP);
      if (!hasSetup) {
        return ValidationUtils.createError('steps', 'Scenario must include at least one setup step', 'MISSING_SETUP');
      }
      
      return null;
    }
  }
];

export const SCENARIO_STEP_RULES: SchemaValidationRule[] = [
  {
    field: 'id',
    required: true,
    type: 'string',
    minLength: 1,
    maxLength: 100
  },
  {
    field: 'type',
    required: true,
    type: 'enum',
    enumValues: Object.values(ScenarioStepType)
  },
  {
    field: 'title',
    required: true,
    type: 'string',
    minLength: 5,
    maxLength: 100
  },
  {
    field: 'description',
    required: true,
    type: 'string',
    minLength: 20,
    maxLength: 500
  },
  {
    field: 'instructions',
    required: true,
    type: 'array',
    customValidator: (instructions: string[]) => {
      if (!Array.isArray(instructions) || instructions.length === 0) {
        return ValidationUtils.createError('instructions', 'At least one instruction is required', 'MIN_INSTRUCTIONS');
      }
      return null;
    }
  }
];

// ============================================================================
// CONTENT SCHEMA REGISTRY
// ============================================================================

export const CONTENT_SCHEMAS: ContentSchema = {
  baseRules: BASE_CONTENT_RULES,
  queryRules: QUERY_CONTENT_RULES,
  commandRules: COMMAND_CONTENT_RULES,
  configRules: CONFIG_CONTENT_RULES
};
