/**
 * Builder classes for creating type-safe content and scenarios
 * 
 * These builders provide a fluent API for constructing content objects
 * with proper validation and type safety.
 */

import {
  Content,
  QueryContent,
  CommandContent,
  ConfigContent,
  ContentType,
  DifficultyLevel,
  PathHierarchy,
  Component,
  Parameter,
  EnhancedScenario,
  BaseMetadata
} from '../../types/core';
import {
  ContentBuilder as IContentBuilder,
  ValidationResult
} from '../../types/validation';
import { ContentValidator } from '../validation/validator';

// ============================================================================
// BASE CONTENT BUILDER
// ============================================================================

export class ContentBuilder implements IContentBuilder {
  private content: Partial<Content> = {};
  private validator = ContentValidator.getInstance();

  constructor() {
    // Initialize with default metadata
    this.content.metadata = {
      id: '',
      createdAt: new Date(),
      updatedAt: new Date(),
      version: '1.0.0',
      tags: []
    };
    this.content.scenarios = [];
    this.content.learningObjectives = [];
    this.content.prerequisites = [];
    this.content.relatedTopics = [];
    this.content.postgresVersions = [];
  }

  public setId(id: string): ContentBuilder {
    this.content.id = id;
    if (this.content.metadata) {
      this.content.metadata.id = id;
    }
    return this;
  }

  public setType(type: ContentType): ContentBuilder {
    this.content.type = type;
    return this;
  }

  public setPath(category: string, subcategory: string | undefined, topic: string): ContentBuilder {
    this.content.path = {
      category,
      subcategory,
      topic
    };
    return this;
  }

  public setTitle(title: string): ContentBuilder {
    this.content.title = title;
    return this;
  }

  public setDescription(description: string): ContentBuilder {
    this.content.description = description;
    return this;
  }

  public setDifficulty(difficulty: DifficultyLevel): ContentBuilder {
    this.content.difficulty = difficulty;
    return this;
  }

  public addScenario(scenario: EnhancedScenario): ContentBuilder {
    if (!this.content.scenarios) {
      this.content.scenarios = [];
    }
    this.content.scenarios.push(scenario);
    return this;
  }

  public addLearningObjective(objective: string): ContentBuilder {
    if (!this.content.learningObjectives) {
      this.content.learningObjectives = [];
    }
    this.content.learningObjectives.push(objective);
    return this;
  }

  public addPrerequisite(prerequisite: string): ContentBuilder {
    if (!this.content.prerequisites) {
      this.content.prerequisites = [];
    }
    this.content.prerequisites.push(prerequisite);
    return this;
  }

  public addRelatedTopic(topic: string): ContentBuilder {
    if (!this.content.relatedTopics) {
      this.content.relatedTopics = [];
    }
    this.content.relatedTopics.push(topic);
    return this;
  }

  public setPostgresVersions(versions: string[]): ContentBuilder {
    this.content.postgresVersions = versions;
    return this;
  }

  public setPostgisVersions(versions: string[]): ContentBuilder {
    this.content.postgisVersions = versions;
    return this;
  }

  public addTag(tag: string): ContentBuilder {
    if (!this.content.metadata) {
      this.content.metadata = {
        id: this.content.id || '',
        createdAt: new Date(),
        updatedAt: new Date(),
        version: '1.0.0',
        tags: []
      };
    }
    this.content.metadata.tags.push(tag);
    return this;
  }

  public validate(): ValidationResult {
    if (!this.isComplete()) {
      return {
        isValid: false,
        errors: [{
          field: 'content',
          message: 'Content is incomplete. Missing required fields.',
          code: 'INCOMPLETE_CONTENT',
          severity: 'error'
        }],
        warnings: []
      };
    }

    return this.validator.validateContent(this.content as Content);
  }

  public build(): Content {
    if (!this.isComplete()) {
      throw new Error('Cannot build incomplete content. Missing required fields.');
    }

    const validation = this.validate();
    if (!validation.isValid) {
      throw new Error(`Content validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    return this.content as Content;
  }

  private isComplete(): boolean {
    return !!(
      this.content.id &&
      this.content.type &&
      this.content.path &&
      this.content.title &&
      this.content.description &&
      this.content.difficulty &&
      this.content.scenarios &&
      this.content.learningObjectives &&
      this.content.prerequisites !== undefined &&
      this.content.relatedTopics &&
      this.content.postgresVersions &&
      this.content.metadata
    );
  }
}

// ============================================================================
// SPECIALIZED BUILDERS
// ============================================================================

export class QueryContentBuilder extends ContentBuilder {
  private queryContent: Partial<QueryContent> = {};

  constructor() {
    super();
    this.setType(ContentType.QUERY);
    this.queryContent.components = [];
    this.queryContent.examples = [];
  }

  public setQueryTemplate(template: string): QueryContentBuilder {
    this.queryContent.queryTemplate = template;
    return this;
  }

  public addComponent(component: Component): QueryContentBuilder {
    if (!this.queryContent.components) {
      this.queryContent.components = [];
    }
    this.queryContent.components.push(component);
    return this;
  }

  public addExample(query: string, explanation: string, expectedResult?: string): QueryContentBuilder {
    if (!this.queryContent.examples) {
      this.queryContent.examples = [];
    }
    this.queryContent.examples.push({
      query,
      explanation,
      expectedResult
    });
    return this;
  }

  public build(): QueryContent {
    const baseContent = super.build();
    return {
      ...baseContent,
      ...this.queryContent,
      type: ContentType.QUERY
    } as QueryContent;
  }
}

export class CommandContentBuilder extends ContentBuilder {
  private commandContent: Partial<CommandContent> = {};

  constructor() {
    super();
    this.setType(ContentType.COMMAND);
    this.commandContent.parameters = [];
    this.commandContent.examples = [];
  }

  public setCommandTemplate(template: string): CommandContentBuilder {
    this.commandContent.commandTemplate = template;
    return this;
  }

  public addParameter(parameter: Parameter): CommandContentBuilder {
    if (!this.commandContent.parameters) {
      this.commandContent.parameters = [];
    }
    this.commandContent.parameters.push(parameter);
    return this;
  }

  public addExample(command: string, explanation: string, expectedOutput?: string): CommandContentBuilder {
    if (!this.commandContent.examples) {
      this.commandContent.examples = [];
    }
    this.commandContent.examples.push({
      command,
      explanation,
      expectedOutput
    });
    return this;
  }

  public build(): CommandContent {
    const baseContent = super.build();
    return {
      ...baseContent,
      ...this.commandContent,
      type: ContentType.COMMAND
    } as CommandContent;
  }
}

export class ConfigContentBuilder extends ContentBuilder {
  private configContent: Partial<ConfigContent> = {};

  constructor() {
    super();
    this.setType(ContentType.CONFIG);
    this.configContent.parameters = [];
    this.configContent.examples = [];
  }

  public setConfigTemplate(template: string): ConfigContentBuilder {
    this.configContent.configTemplate = template;
    return this;
  }

  public addParameter(parameter: Parameter): ConfigContentBuilder {
    if (!this.configContent.parameters) {
      this.configContent.parameters = [];
    }
    this.configContent.parameters.push(parameter);
    return this;
  }

  public addExample(config: string, explanation: string, context: string): ConfigContentBuilder {
    if (!this.configContent.examples) {
      this.configContent.examples = [];
    }
    this.configContent.examples.push({
      config,
      explanation,
      context
    });
    return this;
  }

  public build(): ConfigContent {
    const baseContent = super.build();
    return {
      ...baseContent,
      ...this.configContent,
      type: ContentType.CONFIG
    } as ConfigContent;
  }
}

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

export function createQueryBuilder(): QueryContentBuilder {
  return new QueryContentBuilder();
}

export function createCommandBuilder(): CommandContentBuilder {
  return new CommandContentBuilder();
}

export function createConfigBuilder(): ConfigContentBuilder {
  return new ConfigContentBuilder();
}

export function createContentBuilder(type: ContentType): ContentBuilder {
  switch (type) {
    case ContentType.QUERY:
      return new QueryContentBuilder();
    case ContentType.COMMAND:
      return new CommandContentBuilder();
    case ContentType.CONFIG:
      return new ConfigContentBuilder();
    default:
      throw new Error(`Unknown content type: ${type}`);
  }
}
