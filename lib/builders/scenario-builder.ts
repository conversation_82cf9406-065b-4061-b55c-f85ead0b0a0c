/**
 * Builder class for creating enhanced scenarios
 * 
 * This builder provides a fluent API for constructing comprehensive
 * learning scenarios with proper validation and type safety.
 */

import {
  EnhancedScenario,
  ScenarioStep,
  LearningObjective,
  EnvironmentRequirement,
  DataSetup,
  DifficultyLevel,
  EnvironmentType,
  ScenarioStepType
} from '../../types/core';
import {
  ScenarioBuilder as IScenarioBuilder,
  ValidationResult
} from '../../types/validation';
import { ContentValidator } from '../validation/validator';

// ============================================================================
// SCENARIO STEP BUILDER
// ============================================================================

export class ScenarioStepBuilder {
  private step: Partial<ScenarioStep> = {};

  constructor() {
    this.step.instructions = [];
    this.step.commands = [];
    this.step.hints = [];
    this.step.validationCriteria = [];
  }

  public setId(id: string): ScenarioStepBuilder {
    this.step.id = id;
    return this;
  }

  public setType(type: ScenarioStepType): ScenarioStepBuilder {
    this.step.type = type;
    return this;
  }

  public setTitle(title: string): ScenarioStepBuilder {
    this.step.title = title;
    return this;
  }

  public setDescription(description: string): ScenarioStepBuilder {
    this.step.description = description;
    return this;
  }

  public addInstruction(instruction: string): ScenarioStepBuilder {
    if (!this.step.instructions) {
      this.step.instructions = [];
    }
    this.step.instructions.push(instruction);
    return this;
  }

  public addCommand(command: string): ScenarioStepBuilder {
    if (!this.step.commands) {
      this.step.commands = [];
    }
    this.step.commands.push(command);
    return this;
  }

  public setExpectedOutput(output: string): ScenarioStepBuilder {
    this.step.expectedOutput = output;
    return this;
  }

  public addHint(hint: string): ScenarioStepBuilder {
    if (!this.step.hints) {
      this.step.hints = [];
    }
    this.step.hints.push(hint);
    return this;
  }

  public addTroubleshooting(issue: string, solution: string): ScenarioStepBuilder {
    if (!this.step.troubleshooting) {
      this.step.troubleshooting = {
        commonIssues: [],
        solutions: []
      };
    }
    this.step.troubleshooting.commonIssues.push(issue);
    this.step.troubleshooting.solutions.push(solution);
    return this;
  }

  public addValidationCriteria(criteria: string): ScenarioStepBuilder {
    if (!this.step.validationCriteria) {
      this.step.validationCriteria = [];
    }
    this.step.validationCriteria.push(criteria);
    return this;
  }

  public setEstimatedTime(minutes: number): ScenarioStepBuilder {
    this.step.estimatedTime = minutes;
    return this;
  }

  public build(): ScenarioStep {
    if (!this.isComplete()) {
      throw new Error('Cannot build incomplete scenario step. Missing required fields.');
    }
    return this.step as ScenarioStep;
  }

  private isComplete(): boolean {
    return !!(
      this.step.id &&
      this.step.type &&
      this.step.title &&
      this.step.description &&
      this.step.instructions &&
      this.step.instructions.length > 0
    );
  }
}

// ============================================================================
// MAIN SCENARIO BUILDER
// ============================================================================

export class ScenarioBuilder implements IScenarioBuilder {
  private scenario: Partial<EnhancedScenario> = {};
  private validator = ContentValidator.getInstance();

  constructor() {
    this.scenario.learningObjectives = [];
    this.scenario.prerequisites = [];
    this.scenario.steps = [];
    this.scenario.successCriteria = [];
    this.scenario.commonMistakes = [];
    this.scenario.furtherLearning = [];
    this.scenario.tags = [];
    this.scenario.relatedScenarios = [];
  }

  public setId(id: string): ScenarioBuilder {
    this.scenario.id = id;
    return this;
  }

  public setTitle(title: string): ScenarioBuilder {
    this.scenario.title = title;
    return this;
  }

  public setDescription(description: string): ScenarioBuilder {
    this.scenario.description = description;
    return this;
  }

  public setDifficulty(difficulty: DifficultyLevel): ScenarioBuilder {
    this.scenario.difficulty = difficulty;
    return this;
  }

  public setEstimatedDuration(minutes: number): ScenarioBuilder {
    this.scenario.estimatedDuration = minutes;
    return this;
  }

  public addLearningObjective(description: string, skillLevel: DifficultyLevel, prerequisites?: string[], outcomes?: string[]): ScenarioBuilder {
    if (!this.scenario.learningObjectives) {
      this.scenario.learningObjectives = [];
    }
    
    const objective: LearningObjective = {
      description,
      skillLevel,
      prerequisites: prerequisites || [],
      outcomes: outcomes || []
    };
    
    this.scenario.learningObjectives.push(objective);
    return this;
  }

  public setRealWorldContext(context: string): ScenarioBuilder {
    this.scenario.realWorldContext = context;
    return this;
  }

  public setBusinessValue(value: string): ScenarioBuilder {
    this.scenario.businessValue = value;
    return this;
  }

  public setEnvironment(type: EnvironmentType, requirements?: Partial<EnvironmentRequirement>): ScenarioBuilder {
    this.scenario.environment = {
      type,
      ...requirements
    };
    return this;
  }

  public addPrerequisite(prerequisite: string): ScenarioBuilder {
    if (!this.scenario.prerequisites) {
      this.scenario.prerequisites = [];
    }
    this.scenario.prerequisites.push(prerequisite);
    return this;
  }

  public setInitialSetup(description: string, sqlCommands: string[], expectedOutcome: string): ScenarioBuilder {
    this.scenario.initialSetup = {
      description,
      sqlCommands,
      expectedOutcome,
      dataFiles: []
    };
    return this;
  }

  public addDataFile(name: string, type: 'sql' | 'csv' | 'json' | 'geojson', url?: string, content?: string): ScenarioBuilder {
    if (!this.scenario.initialSetup) {
      this.scenario.initialSetup = {
        description: '',
        sqlCommands: [],
        expectedOutcome: '',
        dataFiles: []
      };
    }
    
    if (!this.scenario.initialSetup.dataFiles) {
      this.scenario.initialSetup.dataFiles = [];
    }
    
    this.scenario.initialSetup.dataFiles.push({
      name,
      type,
      url,
      content
    });
    return this;
  }

  public addStep(step: ScenarioStep): ScenarioBuilder {
    if (!this.scenario.steps) {
      this.scenario.steps = [];
    }
    this.scenario.steps.push(step);
    return this;
  }

  public addStepBuilder(stepBuilder: ScenarioStepBuilder): ScenarioBuilder {
    return this.addStep(stepBuilder.build());
  }

  public addSuccessCriteria(criteria: string): ScenarioBuilder {
    if (!this.scenario.successCriteria) {
      this.scenario.successCriteria = [];
    }
    this.scenario.successCriteria.push(criteria);
    return this;
  }

  public addCommonMistake(mistake: string): ScenarioBuilder {
    if (!this.scenario.commonMistakes) {
      this.scenario.commonMistakes = [];
    }
    this.scenario.commonMistakes.push(mistake);
    return this;
  }

  public addFurtherLearning(learning: string): ScenarioBuilder {
    if (!this.scenario.furtherLearning) {
      this.scenario.furtherLearning = [];
    }
    this.scenario.furtherLearning.push(learning);
    return this;
  }

  public addTag(tag: string): ScenarioBuilder {
    if (!this.scenario.tags) {
      this.scenario.tags = [];
    }
    this.scenario.tags.push(tag);
    return this;
  }

  public addRelatedScenario(scenarioId: string): ScenarioBuilder {
    if (!this.scenario.relatedScenarios) {
      this.scenario.relatedScenarios = [];
    }
    this.scenario.relatedScenarios.push(scenarioId);
    return this;
  }

  public validate(): ValidationResult {
    if (!this.isComplete()) {
      return {
        isValid: false,
        errors: [{
          field: 'scenario',
          message: 'Scenario is incomplete. Missing required fields.',
          code: 'INCOMPLETE_SCENARIO',
          severity: 'error'
        }],
        warnings: []
      };
    }

    return this.validator.validateScenario(this.scenario as EnhancedScenario);
  }

  public build(): EnhancedScenario {
    if (!this.isComplete()) {
      throw new Error('Cannot build incomplete scenario. Missing required fields.');
    }

    const validation = this.validate();
    if (!validation.isValid) {
      throw new Error(`Scenario validation failed: ${validation.errors.map(e => e.message).join(', ')}`);
    }

    return this.scenario as EnhancedScenario;
  }

  private isComplete(): boolean {
    return !!(
      this.scenario.id &&
      this.scenario.title &&
      this.scenario.description &&
      this.scenario.difficulty &&
      this.scenario.estimatedDuration &&
      this.scenario.learningObjectives &&
      this.scenario.realWorldContext &&
      this.scenario.businessValue &&
      this.scenario.environment &&
      this.scenario.prerequisites !== undefined &&
      this.scenario.initialSetup &&
      this.scenario.steps &&
      this.scenario.successCriteria &&
      this.scenario.commonMistakes !== undefined &&
      this.scenario.furtherLearning !== undefined &&
      this.scenario.tags !== undefined
    );
  }
}

// ============================================================================
// FACTORY FUNCTIONS
// ============================================================================

export function createScenarioBuilder(): ScenarioBuilder {
  return new ScenarioBuilder();
}

export function createScenarioStepBuilder(): ScenarioStepBuilder {
  return new ScenarioStepBuilder();
}

// ============================================================================
// HELPER FUNCTIONS FOR COMMON STEP TYPES
// ============================================================================

export function createSetupStep(id: string, title: string, description: string, instructions: string[]): ScenarioStep {
  return new ScenarioStepBuilder()
    .setId(id)
    .setType(ScenarioStepType.SETUP)
    .setTitle(title)
    .setDescription(description)
    .addInstruction(...instructions)
    .build();
}

export function createExecutionStep(id: string, title: string, description: string, commands: string[], expectedOutput?: string): ScenarioStep {
  const builder = new ScenarioStepBuilder()
    .setId(id)
    .setType(ScenarioStepType.EXECUTION)
    .setTitle(title)
    .setDescription(description);
  
  commands.forEach(cmd => builder.addCommand(cmd));
  
  if (expectedOutput) {
    builder.setExpectedOutput(expectedOutput);
  }
  
  return builder.build();
}

export function createValidationStep(id: string, title: string, description: string, criteria: string[]): ScenarioStep {
  const builder = new ScenarioStepBuilder()
    .setId(id)
    .setType(ScenarioStepType.VALIDATION)
    .setTitle(title)
    .setDescription(description);
  
  criteria.forEach(criterion => builder.addValidationCriteria(criterion));
  
  return builder.build();
}
