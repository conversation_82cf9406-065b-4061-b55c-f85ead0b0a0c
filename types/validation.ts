/**
 * Validation types and schemas for the PostgreSQL/PostGIS Learning Platform
 * 
 * This file defines validation schemas, error types, and utility types
 * for ensuring data integrity and providing meaningful error messages.
 */

import { 
  Content, 
  EnhancedScenario, 
  ScenarioStep, 
  ContentType, 
  DifficultyLevel,
  ScenarioStepType,
  EnvironmentType 
} from './core';

// ============================================================================
// VALIDATION RESULT TYPES
// ============================================================================

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  severity: 'error' | 'warning' | 'info';
}

export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationError[];
}

export interface ContentValidationResult extends ValidationResult {
  contentId: string;
  contentType: ContentType;
  scenarioValidations?: ScenarioValidationResult[];
}

export interface ScenarioValidationResult extends ValidationResult {
  scenarioId: string;
  stepValidations?: StepValidationResult[];
}

export interface StepValidationResult extends ValidationResult {
  stepId: string;
  stepType: ScenarioStepType;
}

// ============================================================================
// SCHEMA VALIDATION TYPES
// ============================================================================

export interface SchemaValidationRule {
  field: string;
  required: boolean;
  type: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'enum';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  enumValues?: string[];
  customValidator?: (value: any) => ValidationError | null;
}

export interface ContentSchema {
  baseRules: SchemaValidationRule[];
  queryRules?: SchemaValidationRule[];
  commandRules?: SchemaValidationRule[];
  configRules?: SchemaValidationRule[];
}

// ============================================================================
// MIGRATION AND TRANSFORMATION TYPES
// ============================================================================

export interface LegacyCommand {
  type?: string;
  path: string[];
  command?: string;
  query_template?: string;
  description: string;
  parameters?: Array<{
    flag: string;
    name: string;
    description: string;
  }>;
  components?: Array<{
    component: string;
    name: string;
    description: string;
  }>;
  scenarios?: Array<{
    title: string;
    details: {
      setup: string;
      goal: string;
      outcome: string;
    };
    example?: {
      command?: string;
      query?: string;
      explanation: string;
    };
    notes?: string[];
  }>;
}

export interface MigrationResult {
  success: boolean;
  migratedContent?: Content;
  errors: ValidationError[];
  warnings: ValidationError[];
  originalData: LegacyCommand;
}

export interface MigrationSummary {
  totalFiles: number;
  successfulMigrations: number;
  failedMigrations: number;
  warnings: number;
  errors: ValidationError[];
  migratedContent: Content[];
}

// ============================================================================
// TESTING AND QUALITY ASSURANCE TYPES
// ============================================================================

export interface TestCase {
  id: string;
  name: string;
  description: string;
  contentId: string;
  scenarioId?: string;
  stepId?: string;
  testType: 'syntax' | 'execution' | 'output' | 'performance' | 'integration';
  input: any;
  expectedOutput: any;
  timeout?: number;
}

export interface TestResult {
  testId: string;
  passed: boolean;
  executionTime: number;
  actualOutput?: any;
  error?: string;
  warnings?: string[];
}

export interface QualityMetrics {
  contentId: string;
  completeness: number; // 0-100
  accuracy: number; // 0-100
  clarity: number; // 0-100
  difficulty: number; // 0-100
  engagement: number; // 0-100
  lastUpdated: Date;
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;

export type OptionalFields<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

// ============================================================================
// CONTENT BUILDER TYPES
// ============================================================================

export interface ContentBuilder {
  setId(id: string): ContentBuilder;
  setType(type: ContentType): ContentBuilder;
  setPath(category: string, subcategory: string | undefined, topic: string): ContentBuilder;
  setTitle(title: string): ContentBuilder;
  setDescription(description: string): ContentBuilder;
  setDifficulty(difficulty: DifficultyLevel): ContentBuilder;
  addScenario(scenario: EnhancedScenario): ContentBuilder;
  addLearningObjective(objective: string): ContentBuilder;
  addPrerequisite(prerequisite: string): ContentBuilder;
  addRelatedTopic(topic: string): ContentBuilder;
  setPostgresVersions(versions: string[]): ContentBuilder;
  setPostgisVersions(versions: string[]): ContentBuilder;
  build(): Content;
  validate(): ValidationResult;
}

export interface ScenarioBuilder {
  setId(id: string): ScenarioBuilder;
  setTitle(title: string): ScenarioBuilder;
  setDescription(description: string): ScenarioBuilder;
  setDifficulty(difficulty: DifficultyLevel): ScenarioBuilder;
  setEstimatedDuration(minutes: number): ScenarioBuilder;
  addLearningObjective(objective: string, skillLevel: DifficultyLevel): ScenarioBuilder;
  setRealWorldContext(context: string): ScenarioBuilder;
  setBusinessValue(value: string): ScenarioBuilder;
  setEnvironment(env: EnvironmentType, requirements?: any): ScenarioBuilder;
  addPrerequisite(prerequisite: string): ScenarioBuilder;
  setInitialSetup(setup: any): ScenarioBuilder;
  addStep(step: ScenarioStep): ScenarioBuilder;
  addSuccessCriteria(criteria: string): ScenarioBuilder;
  addCommonMistake(mistake: string): ScenarioBuilder;
  addFurtherLearning(learning: string): ScenarioBuilder;
  addTag(tag: string): ScenarioBuilder;
  addRelatedScenario(scenarioId: string): ScenarioBuilder;
  build(): EnhancedScenario;
  validate(): ValidationResult;
}

// ============================================================================
// API TYPES
// ============================================================================

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  metadata?: {
    timestamp: Date;
    version: string;
    requestId: string;
  };
}

export interface PaginatedResponse<T> extends APIResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    hasNext: boolean;
    hasPrevious: boolean;
  };
}

export interface SearchFilters {
  contentType?: ContentType[];
  difficulty?: DifficultyLevel[];
  categories?: string[];
  tags?: string[];
  postgresVersions?: string[];
  postgisVersions?: string[];
  searchQuery?: string;
}

export interface SearchResult {
  content: Content;
  relevanceScore: number;
  matchedFields: string[];
  highlights: {
    field: string;
    snippet: string;
  }[];
}
