/**
 * Core type definitions for the PostgreSQL/PostGIS Learning Platform
 * 
 * This file defines the foundational types that ensure type safety and consistency
 * across the entire application. These types represent the enhanced data structure
 * for commands, queries, configurations, and learning scenarios.
 */

// ============================================================================
// ENUMS AND CONSTANTS
// ============================================================================

export enum ContentType {
  QUERY = 'query',
  COMMAND = 'command',
  CONFIG = 'config'
}

export enum DifficultyLevel {
  BEGINNER = 'beginner',
  INTERMEDIATE = 'intermediate',
  ADVANCED = 'advanced',
  EXPERT = 'expert'
}

export enum ScenarioStepType {
  SETUP = 'setup',
  PREREQUISITE = 'prerequisite',
  DATA_PREPARATION = 'data_preparation',
  EXECUTION = 'execution',
  VALIDATION = 'validation',
  CLEANUP = 'cleanup'
}

export enum EnvironmentType {
  LOCAL = 'local',
  DOCKER = 'docker',
  CLOUD = 'cloud',
  SANDBOX = 'sandbox'
}

// ============================================================================
// BASIC BUILDING BLOCKS
// ============================================================================

export interface BaseMetadata {
  id: string;
  createdAt: Date;
  updatedAt: Date;
  version: string;
  tags: string[];
}

export interface PathHierarchy {
  category: string;
  subcategory?: string;
  topic: string;
}

// ============================================================================
// COMPONENT SYSTEM
// ============================================================================

export interface Component {
  component: string;
  name: string;
  description: string;
  isOptional?: boolean;
  examples?: string[];
  relatedConcepts?: string[];
}

export interface Parameter {
  flag: string;
  name: string;
  description: string;
  isRequired?: boolean;
  defaultValue?: string;
  examples?: string[];
  validationPattern?: string;
}

// ============================================================================
// ENHANCED SCENARIO SYSTEM
// ============================================================================

export interface EnvironmentRequirement {
  type: EnvironmentType;
  postgresVersion?: string;
  postgisVersion?: string;
  extensions?: string[];
  minimumResources?: {
    memory?: string;
    storage?: string;
    cpu?: string;
  };
}

export interface DataSetup {
  description: string;
  sqlCommands: string[];
  dataFiles?: {
    name: string;
    type: 'sql' | 'csv' | 'json' | 'geojson';
    url?: string;
    content?: string;
  }[];
  expectedOutcome: string;
}

export interface ScenarioStep {
  id: string;
  type: ScenarioStepType;
  title: string;
  description: string;
  instructions: string[];
  commands?: string[];
  expectedOutput?: string;
  hints?: string[];
  troubleshooting?: {
    commonIssues: string[];
    solutions: string[];
  };
  validationCriteria?: string[];
  estimatedTime?: number; // in minutes
}

export interface LearningObjective {
  description: string;
  skillLevel: DifficultyLevel;
  prerequisites?: string[];
  outcomes: string[];
}

export interface EnhancedScenario {
  id: string;
  title: string;
  description: string;
  difficulty: DifficultyLevel;
  estimatedDuration: number; // in minutes
  
  // Learning context
  learningObjectives: LearningObjective[];
  realWorldContext: string;
  businessValue: string;
  
  // Technical requirements
  environment: EnvironmentRequirement;
  prerequisites: string[];
  
  // Scenario content
  initialSetup: DataSetup;
  steps: ScenarioStep[];
  
  // Validation and assessment
  successCriteria: string[];
  commonMistakes: string[];
  furtherLearning: string[];
  
  // Metadata
  tags: string[];
  relatedScenarios?: string[];
}

// ============================================================================
// MAIN CONTENT TYPES
// ============================================================================

export interface BaseContent {
  id: string;
  type: ContentType;
  path: PathHierarchy;
  title: string;
  description: string;
  difficulty: DifficultyLevel;
  
  // Enhanced scenarios
  scenarios: EnhancedScenario[];
  
  // Learning metadata
  learningObjectives: string[];
  prerequisites: string[];
  relatedTopics: string[];
  
  // Technical metadata
  postgresVersions: string[];
  postgisVersions?: string[];
  
  // Content metadata
  metadata: BaseMetadata;
}

export interface QueryContent extends BaseContent {
  type: ContentType.QUERY;
  queryTemplate: string;
  components: Component[];
  examples: {
    query: string;
    explanation: string;
    expectedResult?: string;
  }[];
}

export interface CommandContent extends BaseContent {
  type: ContentType.COMMAND;
  commandTemplate: string;
  parameters: Parameter[];
  examples: {
    command: string;
    explanation: string;
    expectedOutput?: string;
  }[];
}

export interface ConfigContent extends BaseContent {
  type: ContentType.CONFIG;
  configTemplate: string;
  parameters: Parameter[];
  examples: {
    config: string;
    explanation: string;
    context: string;
  }[];
}

export type Content = QueryContent | CommandContent | ConfigContent;

// ============================================================================
// COLLECTION AND ORGANIZATION
// ============================================================================

export interface ContentCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
  subcategories: ContentSubcategory[];
}

export interface ContentSubcategory {
  id: string;
  name: string;
  description: string;
  contents: Content[];
}

export interface LearningPath {
  id: string;
  title: string;
  description: string;
  difficulty: DifficultyLevel;
  estimatedDuration: number;
  prerequisites: string[];
  contents: string[]; // Content IDs in order
  milestones: {
    title: string;
    description: string;
    contentIds: string[];
  }[];
}

// ============================================================================
// USER PROGRESS AND INTERACTION
// ============================================================================

export interface UserProgress {
  userId: string;
  contentId: string;
  scenarioId?: string;
  stepId?: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'failed';
  startedAt?: Date;
  completedAt?: Date;
  attempts: number;
  score?: number;
  notes?: string;
}

export interface UserSession {
  sessionId: string;
  userId: string;
  startedAt: Date;
  lastActivity: Date;
  currentContent?: string;
  currentScenario?: string;
  currentStep?: string;
}
